<?PHP
/*
utf8
Funkce vklada do tab. content udaje o dokumentu
Inputs: pole s udaji
Outputs: true/false
*/
function put_content($a_part, &$id_sql, &$db){
	
		$t1 = Trim($a_part["name"]);
		$t2 = Trim($a_part["descr"]);
		$t3 = $a_part["id_m"];
//		$t4 = $a_part["date_expir"];
		$t5 = $a_part["path"];
		$t6 = $a_part["sect2"];
		
		$SEL = "INSERT INTO m_content (name, descr, owner, path, date_insert, date_edit, sect2) VALUES ('$t1', '$t2', '$t3', '$t5', Now(), Now(), '$t6' )";
		$tmp = $db->Query($SEL);
		$id_sql = $db->getInsertId();
		If ($tmp){
			return true;
		} else {
			return false;
		}
}
?>
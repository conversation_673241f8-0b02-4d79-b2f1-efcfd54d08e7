<?PHP
/**
 * Funkce vraci vyber z polozek dokumentu
 * INPUTS: sekce, a pole odkazem, mesic vyhledavani.
 *
 * @version: 2016-01-10, TK: manualy se radi podle abacedy
 *
 * @param $db
 * @param $men umisteni ve strukture stranek
 * @param $a_vypis
 * @param string $s_od
 * @param string $s_do
 * @param int $i_switch prepinac $i_switch = 1; zobrazi data o objektu v db podle jeho id_c, $i_switch = 2; zobrazi vse v dane sekci
 * @param int $id_c
 * @param int $page
 * @param int $step
 * @param int $celkem
 * @return mixed pocet nalezenych zaznamu/pole se zaznamy (path, name, to_date, id, descr).
 */
function get_content(&$db, $men, &$a_vypis, $s_od = "2000-01-01", $s_do = "2200-01-01", $i_switch = 0, $id_c = 0, $page = 1, $step = 10, &$celkem = 0)
{
    preg_match("/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts);
    $s1 = $parts[1];
    $s2 = $parts[2];
    $s3 = $parts[3];
    $s4 = $parts[4];
    $s_0 = $s1 . "." . $s2 . "." . $s3 . "." . $s4;


    // defaultne se radi podle data editace sestupne
    $sloupec_razeni = ' date_edit DESC ';
    // na zaklade usneseni RM ze dne 2016-01-10 se manualy maji radit abecedne dle nazvu
    if ($s1 == 14 and $s2 == 7) $sloupec_razeni = ' name ASC ';


    switch ($i_switch) {
        case 1:
            $SEL = "SELECT c.*, date_format(c.date_edit, '%e.%c.%Y') AS date_edit2, date_format(c.date_expir, '%e.%c.%Y') AS date_expir2, m.jmeno, m.prijmeni FROM m_content AS c INNER JOIN m_members AS m ON c.owner=m.id_m WHERE c.id=$id_c";
            $SEL2 = "SELECT COUNT(c.id) AS pocet FROM m_content AS c INNER JOIN m_members AS m ON c.owner=m.id_m WHERE c.id=$id_c";

            break;
        case 2:
            $SEL = "SELECT c.*, date_format(c.date_edit, '%e.%c.%Y') AS date_edit2, date_format(c.date_expir, '%e.%c.%Y') AS date_expir2, m.jmeno, m.prijmeni FROM m_content AS c INNER JOIN m_members AS m ON c.owner=m.id_m WHERE c.sect2='$s_0' ORDER BY {$sloupec_razeni} LIMIT " . ($page * $step) . ", $step";
            $SEL2 = "SELECT COUNT(c.id) AS pocet FROM m_content AS c INNER JOIN m_members AS m ON c.owner=m.id_m WHERE c.sect2='$s_0'";

            break;
        default:
            $SEL = "SELECT c.*, date_format(c.date_edit, '%e.%c.%Y') AS date_edit2, date_format(c.date_expir, '%e.%c.%Y') AS date_expir2, m.jmeno, m.prijmeni FROM m_content AS c INNER JOIN m_members AS m ON c.owner=m.id_m WHERE (c.sect2='$s_0') AND (c.date_expir >='$s_od') AND (c.date_expir<='$s_do') ORDER BY {$sloupec_razeni} LIMIT " . ($page * $step) . ", $step";
            $SEL2 = "SELECT COUNT(c.id) AS pocet FROM m_content AS c INNER JOIN m_members AS m ON c.owner=m.id_m WHERE (c.sect2='$s_0') AND (c.date_expir >='$s_od') AND (c.date_expir<='$s_do')";
    }


    $tmp = $db->Query($SEL);
    $tmp2 = $db->Query($SEL2);

    $celkem = $db->getResult($tmp2, 0, "pocet");
    $i = $db->getNumRows($tmp);

    while ($i--) {
        preg_match("/\.(.*)\$/", $db->getResult($tmp, $i, "path"), $a);
        $a_vypis["suffix"][$i] = $a[count($a) - 1];
        $a_vypis["name"][$i] = $db->getResult($tmp, $i, "name");
        $a_vypis["date_edit2"][$i] = $db->getResult($tmp, $i, "date_edit2");
        $a_vypis["date_expir2"][$i] = $db->getResult($tmp, $i, "date_expir2");
        $a_vypis["id_c"][$i] = $db->getResult($tmp, $i, "id");
        $a_vypis["descr"][$i] = $db->getResult($tmp, $i, "descr");
        $a_vypis["sect2"][$i] = $db->getResult($tmp, $i, "sect2");
        $a_vypis["path"][$i] = $db->getResult($tmp, $i, "path");
        $a_vypis["vlozil"][$i] = $db->getResult($tmp, $i, "jmeno") . " " . $db->getResult($tmp, $i, "prijmeni");
    }

    return $db->getNumRows($tmp);
}


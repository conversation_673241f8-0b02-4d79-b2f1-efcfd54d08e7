<?PHP
function inquiry_task(&$db, $id_ank){

	$SEL="select * from m_ank_volby where id_ank=$id_ank ORDER BY id_volby desc";
	$tmp=$db->Query($SEL);

	$i=$db->getNumRows($tmp);
	while ($i--){
		$a_inqtask[$i]["id_volby"]=$db->getResult($tmp, $i, "id_volby");
		$a_inqtask[$i]["id_ank"]=$db->getResult($tmp, $i, "id_ank");
		$a_inqtask[$i]["value"]=$db->getResult($tmp, $i, "value");
	}
	return $a_inqtask;
}
?>
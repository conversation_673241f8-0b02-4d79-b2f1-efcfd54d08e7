<script type="text/javascript" language="JavaScript">
    //<!--
    function opendiv(key) {
        muj = 'div_' + key;
        obr = 'img_' + key;

        if (document.getElementById(muj).style.display === 'none') {
            document.getElementById(muj).style.display = 'block';
            document.getElementById(obr).src = '/images/minus.gif';
        } else {
            document.getElementById(muj).style.display = 'none';
            document.getElementById(obr).src = '/images/plus.gif';
        }
    }
    //-->
</script>


<?php

function make_menu($a_user, $men, &$db, $src_men, $level = 0, &$struktura)
{
    global $a_user;

    require_once("../ssl_library_new/get_list_path.l");
    require_once("../ssl_library_new/get_path.l");
    require_once("../ssl_library_new/get_list.l");
    require_once("../ssl_library_new/html_pause.l");

    // jestlize se jedna o aplikace, menu se neposouva
    if (empty($a_path[count($a_path ?? []) - 1]["path"])) {
        $a_list = get_list($men, $db);
    } else {
        require_once("../ssl_library_new/downgrade_menu.l");
        $men_down = downgrade_menu($men);
        $a_list = get_list($men_down, $db);
    }

    $i = count($a_list ?? []);
    $tmp_menu = "";

    while ($i--) {
        // jestlize mas prava, pak uvidis!
        if (access("read", $a_list[$i]["men"], $a_user["id_m"], $db)) {
            $a_list[$i]["acc"] = true;
            ($src_men == $a_list[$i]["men"]) ? $cls = " class='a_selected'" : $cls = " class='a_normal'";

            if (!$a_list[$i]["posledni"]) {
                if ($level == 0) {
                    $last_menu = "<li ###STR0###>###STR###<a href='index.php?men=" . $a_list[$i]["men"] . "'$cls><strong>" . $a_list[$i]["class_descr"] . "</strong></a></li>\n";
                } else {
                    $last_menu = "<li ###STR0###>###STR###<a href='index.php?men=" . $a_list[$i]["men"] . "'$cls>" . $a_list[$i]["class_descr"] . "</a></li>\n";
                }

                //$tmp_menu.= "<li ###STR###><a href='index.php?men=".$a_list[$i]["men"]."'$cls>".($a_list[$i]["class_descr"])."</a></li>\n";
                $x2 = "";
                $ret = make_menu($a_user, $a_list[$i]["men"], $db, $src_men, ($level + 1), $x2);
                if (strlen($ret) > 0) {
                    //if ($level==1){
                    $a_list[$i]["poduroven"] = $x2;
                    if ($a_list[$i]["box"] == "Y") {
                        if ($cls == " class='a_selected'" OR strpos($ret, "a_selected") > 0) {
                            $img = "<img src='/images/minus.gif' style='position: relative; top: 1px; left: -18px;' id='img_" . $a_list[$i]["men"] . "' onclick='javascript:opendiv('" . $a_list[$i]["men"] . "')' width='10' height='10' alt=''>";
                            $menu1 = "<ul class='ul_menu'><div id='div_" . $a_list[$i]["men"] . "' style='display:block;'>$ret<div></ul>";
                        } else {
                            $img = "<img src='/images/plus.gif' style='position: relative; top: 1px; left: -18px;' id='img_" . $a_list[$i]["men"] . "' onclick='javascript:opendiv('" . $a_list[$i]["men"] . "')' width='10' height='10' alt=''>";
                            $menu1 = "<ul class='ul_menu'><div id='div_" . $a_list[$i]["men"] . "' style='display:none;'>$ret<div></ul>";
                        }
                        $last_menu = str_replace("###STR###", $img, $last_menu);
                        $last_menu = str_replace("###STR0###", "style='list-style-type:none;'", $last_menu);
                    } else {
                        $img = "";
                        $menu1 = "<ul class='ul_menu'>$ret</ul>";
                        $last_menu = str_replace("###STR###", $img, $last_menu);
                        $last_menu = str_replace("###STR0###", "", $last_menu);
                    }
                    $tmp_menu .= $last_menu . $menu1;
                } elseif ($level == 0) {
                    $last_menu = str_replace("###STR###", "", $last_menu);
                    $last_menu = str_replace("###STR0###", "", $last_menu);
                    $tmp_menu .= $last_menu;
                    $tmp_menu .= "<div style='font-size:5px;'>&nbsp;</div>";
                } else {
                    $last_menu = str_replace("###STR0###", "", $last_menu);
                    $tmp_menu .= str_replace("###STR###", "", $last_menu);
                }
            }
        } else {
            $tmp_menu .= "<li>" . ($a_list[$i]["class_descr"]) . "</li>";
            $a_list[$i]["acc"] = false;
        }
    }

    $struktura = $a_list;

    return $tmp_menu;
}

$menu2 = "";

if (is_array($menu)) {
    //echo "FROM CACHE!<br>";
    echo write_menu2($menu, $men, 0);
    //print_r($menu);
} else {

    $menu = "";
    echo make_menu($a_user, "men0.0.0.0", $db, $men, 0, $menu);
    // $menu = $_SESSION['menu'];
    //session_register("menu");
    $_SESSION["menu"] = $menu;
}

function write_menu2($arr, $sel, $level)
{
    $tmp_menu = "";

    foreach ($arr as $var => $key) {
        if ($key['acc']) {
            ($sel == $key["men"]) ? $cls = " class='a_selected'" : $cls = " class='a_normal'";

            if (!$key["posledni"]) {
                if ($level == 0) {
                    $last_menu = "<li ###STR0###>###STR###<a href='index.php?men=" . $key["men"] . "'$cls><strong>" . $key["class_descr"] . "</strong></a></li>\n";
                } else {
                    $last_menu = "<li ###STR0###>###STR###<a href='index.php?men=" . $key["men"] . "'$cls>" . $key["class_descr"] . "</a></li>\n";
                }

                // $x2 = "";
                if (is_array(@$key["poduroven"])) {
                    $ret = write_menu2($key['poduroven'], $sel, $level + 1);
                    if ($key["box"] == "Y") {
                        if ($cls == " class='a_selected'" OR strpos($ret, "a_selected") > 0) {
                            $img = "<img src='/images/minus.gif' style='position: relative; top: 1px; left: -18px;' id='img_" . $key["men"] . "' onclick='javascript:opendiv('" . $key["men"] . "')' width='10' height='10' alt=''>";
                            $menu1 = "<ul class='ul_menu'><div id='div_" . $key["men"] . "' style='display:block;'>$ret<div></ul>";
                        } else {
                            $img = "<img src='/images/plus.gif' style='position: relative; top: 1px; left: -18px;' id='img_" . $key["men"] . "' onclick='javascript:opendiv('" . $key["men"] . "')' width='10' height='10' alt=''>";
                            $menu1 = "<ul class='ul_menu'><div id='div_" . $key["men"] . "' style='display:none;'>$ret<div></ul>";
                        }
                        $last_menu = str_replace("###STR###", $img, $last_menu);
                        $last_menu = str_replace("###STR0###", "style='list-style-type:none;'", $last_menu);
                    } else {
                        $img = "";
                        $menu1 = "<ul class='ul_menu'>$ret</ul>";
                        $last_menu = str_replace("###STR###", $img, $last_menu);
                        $last_menu = str_replace("###STR0###", "", $last_menu);
                    }
                    $tmp_menu .= $last_menu . $menu1;
                } elseif ($level == 0) {
                    $last_menu = str_replace("###STR###", "", $last_menu);
                    $last_menu = str_replace("###STR0###", "", $last_menu);
                    $tmp_menu .= $last_menu;
                    $tmp_menu .= "<div style='font-size:5px;'>&nbsp;</div>";
                } else {
                    $last_menu = str_replace("###STR0###", "", $last_menu);
                    $tmp_menu .= str_replace("###STR###", "", $last_menu);
                }
            }


        } else {
            // $tmp_menu .= "<li>" . ($key["class_descr"]) . "</li>";
        }
    }

    return $tmp_menu;
}


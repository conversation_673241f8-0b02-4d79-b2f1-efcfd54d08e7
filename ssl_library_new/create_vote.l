<?PHP
function create_vote($db, $a_record)
{
	$owner=$a_record["owner"];
	$name=$a_record["name"];
	$popis=$a_record["popis"];
	$switch=$a_record["switch"];
	$text=$a_record["text"];
	$start=$a_record["start"];
	$end=$a_record["end"];
	$kategorie=$a_record["kategorie"];

	$INS="INSERT INTO m_ank_ankety (owner, name, popis, switch, text, start, end, kategorie) VALUES('$owner', '$name', '$popis', '$switch', '$text', '$start', '$end', '$kategorie')";
	$tmp = $db->Query($INS);
	$id_ank = $db->getInsertId();
	return($id_ank);
}
?>
<?PHP
/*
//utf8
	funkce nastavuje prava jednotlivym rolim.
*/
function set_role_acc($to_sect_selected, $acc_individual, $id_m, $s_edit_role, $switch=0, &$db){
	$i_uid = $id_m;
	$i_uid_to = $s_edit_role;

	preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $to_sect_selected, $parts );
	$s2= $parts[1];
	$s3= $parts[2];
	$s4= $parts[3];
	$s5= $parts[4];
	
	$SEL = "SELECT class_id FROM m_acc_class WHERE s1=$s2 AND s2=$s3 AND s3=$s4 AND s4=$s5";
	$tmp = $db->Query($SEL);
	$class_id = $db->getResult($tmp, 0, "class_id");

switch ($switch){
	case 0:
		$SEL = "INSERT INTO m_acc_permission (value, id_m, role_id, class_id, created, created_by) VALUES ($acc_individual, $id_m, $i_uid_to, $class_id, Now(), $i_uid)";
	break;
	case 1:
		$SEL = "UPDATE m_acc_permission SET value=$acc_individual, created=Now(), created_by=$i_uid WHERE id_m=$id_m AND role_id=$i_uid_to AND class_id=$class_id";
	break;
}
		If ($db->Query($SEL)){		
			return true;
		} else {
			return false;
		}

}
?>
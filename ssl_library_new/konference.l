<?php
/**

<PERSON><PERSON> to v<PERSON><PERSON><PERSON><PERSON><PERSON> zpr<PERSON>vy z konferenčních mailboxů a dává je do databáze a posílá upozornění, pokud vybírání z emailů selhalo nebo byl z<PERSON>án <PERSON>ý email:
ftp://<EMAIL>/ssl_library_new/konference.l

Zmenovnik

2013-Oct-17, TK: Zalozeni zmenovniku (pred tim v srpnu upraveni na GMAIL)
2015-Mar-03, PM: oprava proměnné $prispevky_min, z $prispevky_min = date("Y",mktime(0,0,0,date("m")-2,1,date("Y")))
*/

use \mailer\MailerFactory;
use \mailer\MailerInterface;

function konference($konference, $pop3_server, $pop3_port){
  foreach ($konference as $id=>$konf){
    check_konf($pop3_server, $pop3_port, $konf, $id);
  }
}
function subject2str($subject){
  $subj = imap_mime_header_decode($subject);
  $return = '';
  foreach ($subj as $spart){
    if ($spart->charset == 'default') {
      $return .= $spart->text;
    } else {
      $return .= iconv($spart->charset, "utf-8", $spart->text);
    }
  }
  return $return;
}
function parse_part(&$message, $structure, $imap, $msgno, $sectionid){
  global $mime_primary;
  if ($structure->type == 1 ) {//multipart
    for ($i=0;$i<count($structure->parts);$i++){
      parse_part($message, $structure->parts[$i], $imap, $msgno, $sectionid.'.'.$i);
    }
  } else {
  	$is_attachment = false;
    $filename = '';
  	$charset = null;
  	$content = imap_fetchbody($imap, $msgno, $sectionid);
		if($structure->encoding == 3) { // 3 = BASE64
			$content = base64_decode($content);
		}
		elseif($structure->encoding == 4) { // 4 = QUOTED-PRINTABLE
			$content = quoted_printable_decode($content);
		}
  	if($structure->ifdparameters) {
  		foreach($structure->dparameters as $object) {
  			if(strtolower($object->attribute) == 'filename') {
  				$is_attachment = true;
  				$filename = $object->value;
  			}
  			if(strtolower($object->attribute) == 'charset') {
  				$charset = $object->value;
  			}
  		}
  	}

  	if($structure->ifparameters) {
  		foreach($structure->parameters as $object) {
  			if(strtolower($object->attribute) == 'name') {
  				$is_attachment = true;
  				$filename = $object->value;
  			}
  			if(strtolower($object->attribute) == 'charset') {
  				$charset = $object->value;
  			}
  		}
  	}

  	if($is_attachment) {
  	  //attachement part
  	  $p = new stdClass();
  		$p->content = $content;
  		$p->name = $filename;
  		$p->mime = $mime_primary[$structure->type];
      if ($structure->ifsubtype){
        $p->mime .= '/'.strtolower($structure->subtype);
      }
  		$message->att[] = $p;
  	} else {
  	  //non-attachement part (expect body or altbody)
  	  if ($structure->subtype=='PLAIN'){
  	    $message->text_body = $content;
        if ($charset && $charset != 'default'){
          $message->text_body = iconv($charset, "utf-8", $message->text_body);
        }
  	  } elseif ($structure->subtype=='HTML'){
  	    $message->html_body = $content;
  	    if ($charset && $charset != 'default'){
          $message->html_body = iconv($charset, "utf-8", $message->html_body);
        }
  	  }
  	}
  }
}
function parse_message($imap, $msgno){
  global $mime_primary;
  $structure = imap_fetchstructure($imap, $msgno); //get mail structure
  //echo '<pre>';var_dump($structure);echo'<pre>';
  $message = new stdClass(); //return object
  $message->header = imap_headerinfo($imap, $msgno);
  $message->att = array();
  $message->subject = subject2str($message->header->subject);
  $message->text_body = null;
  $message->html_body = null;
  $message->structure = $structure;
  if($structure->type == 1) {
    for($i = 0; $i < count($structure->parts); $i++) {
      parse_part($message, $structure->parts[$i], $imap, $msgno, (string)$i+1);
    }
  } else {
    parse_part($message, $structure, $imap, $msgno, '1');
  }

/*
  if ($structure->type == 0){
    //single-part message
    if ($structure->subtype == 'PLAIN'){
      $message->text_body = imap_body($imap, $msgno);
    } elseif ($structure->subtype == 'HTML') {
      $message->html_body = imap_body($imap, $msgno);
    }
    return $message;
  }

*/
  return $message;
}


function check_konf($server, $port, $konf, $idk){
  global $db;
  $mailbox = @imap_open('{'.$server.':'.$port.'/pop3/ssl/novalidate-cert}', $konf['login'], $konf['password']);
//  $mailbox = @imap_open('{'.$server.':'.$port.'/pop3/notls}', $konf['login'], $konf['password']);
  if (!$mailbox){
    echo "selhalo pripojeni ke konferenci: {$konf['name']}<br>";
    return false;
  }
  $count = imap_num_msg($mailbox);
  for ($i = 1; $i <= $count; $i++){
    $message = parse_message($mailbox, $i);
    $email_from = $message->header->from[0]->mailbox.'@'.$message->header->from[0]->host;
    //dodelat odeslani informacniho mailu v pripade neuspechu a overeni, zda je uzivatel v konferenci
    try {
      $prispevky_min = date("Y",mktime(0,0,0,date("m"),1,date("Y")-1));  
      if (isset($konf['auth_send']) && (is_numeric($konf['auth_send']) || is_array($konf['auth_send']))){
        //mohou prispivat pouze vybrani
        $q_idm = $db->Query(
          "SELECT
            id_m
          FROM
            m_members
          WHERE
            LOWER(email)='".$db->getEscapedString(strtolower($email_from))."'
            AND prizpevky>=$prispevky_min
            AND disable='N'
          ");
        if (!$q_idm || $db->getNumRows($q_idm) == 0){
          //e-mail nenalezen v DB
          throw new Exception("Váš e-mail '{$email_from}' nebyl nalezen mezi aktivními účty intranetu Mensy. E-maily do mensovní konference musíte zasílat z toho e-mailu, který máte nastaven ve vašem účtu na intranetu. (A)");
        }
        list($id_m) = $db->FetchRow($q_idm);
        $auth_send = is_numeric($konf['auth_send']) ? array($konf['auth_send']) : $konf['auth_send'];
        $success = false;
        foreach ($auth_send as $id){
          if ($id == $id_m){
            $success = true;
            break;
          }
        }
        if (!$success){
          throw new Exception("Nemáte dostatečné oprávnění k zasílání zpráv do této konference. Do této konference mohou zasílat pouze vybraní uživatelé.");
        }
      } else {
        //mohou prispivat vsichni, kdo maji aktivni prijem
        $q_idm = $db->Query(
          "SELECT
             m_ae_konfcfg.id_m
           FROM m_members
             LEFT JOIN m_ae_konfcfg
               ON m_members.id_m=m_ae_konfcfg.id_m
               AND m_ae_konfcfg.id_konf=$idk
               AND m_ae_konfcfg.banned=0
           WHERE
             m_members.email='".$db->getEscapedString($email_from)."'
             AND m_members.prizpevky>=$prispevky_min
             AND m_members.disable='N'
             ");
        if (!$q_idm || $db->getNumRows($q_idm) == 0){
          //e-mail nenalezen v DB
          throw new Exception("Váš e-mail '{$email_from}' nebyl nalezen mezi aktivními účty intranetu Mensy. E-maily do mensovní konference musíte zasílat z toho e-mailu, který máte nastavený ve vašem účtu na intranetu. (B)");
        }
        list($id_m) = $db->FetchRow($q_idm);
        if ($id_m == null){
          //neni prihlasen do konference
          throw new Exception("Abyste mohli přispívat do konference, musíte být přihlášen/a k jejímu odběru. Toto lze nastavit v příjmu zpráv ve Vašem účtu v intranetu Mensy.");
        }
      }
    } catch (Exception $ex){
      imap_delete($mailbox, $i);

        $mailbody = "
            <p>
                E-mail, který jste zaslali na adresu {$konf['alias']}, nelze rozeslat do konference.<br /><br />
                
                Důvod: {$ex->getMessage()}<br />
                Předmět Vašeho e-mailu byl: {$message->subject}<br />
            </p>
        ";

        MailerFactory::getMailer()->sendDefaultMail(
            MailerInterface::NOREPLY_MAIL_ADDRESS,
            "Intranet Mensy",
            [$email_from, $email_from],
            SUBJECT_PREFIX." Nelze rozeslat hromadný e-mail",
            $mailbody
        );

      echo "Nezdarilo se odeslat mail od {$email_from} do konference {$idk}, protoze: {$ex->getMessage()}";
      continue;

    }
    if ($message->html_body !== null){
      $html_body = "'".$db->getEscapedString($message->html_body)."'";
    } else {
      $html_body = 'null';
    }

    if ($message->text_body !== null) {
      $text_body = "'".$db->getEscapedString($message->text_body)."'";
    } else {
      $text_body = 'null';
    }
    $db->Query($a="INSERT INTO m_ae_mails (id_m, cas, konference, subject, html_body, text_body, uid) VALUES ($id_m, NOW(), $idk, '".$db->getEscapedString($message->subject)."', $html_body, $text_body, '".md5($message->header->message_id)."')");
    $mailid = $db->getInsertId();
    if ($mailid != 0){
      foreach ($message->att as $attachment){
        $db->Query("INSERT INTO m_ae_atts(id_mail,filename,contenttype) VALUES ($mailid, '".$db->getEscapedString($attachment->name)."', '".$db->getEscapedString($attachment->mime)."')");
        $attid = $db->getInsertId();
        file_put_contents('../files/auto_email2/'.$attid.'.att', $attachment->content);
      }
    }
    imap_delete($mailbox, $i);
  }
  imap_close($mailbox, CL_EXPUNGE);
}

require_once '../ssl_pages_new/auto_email2/config.i';
require_once '../ssl_pages_new/auto_email2/konference.config.i';

$mime_primary = array(
  0=>'text',
  1=>'multipart',
  2=>'message',
  3=>'application',
  4=>'audio',
  5=>'image',
  6=>'video',
  7=>'model',
);

konference($konference, $pop3_server, $pop3_port);

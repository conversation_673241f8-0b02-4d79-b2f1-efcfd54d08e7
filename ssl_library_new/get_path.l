<?PHP
// funkce vraci umisteni v menu
function get_path($men, &$db){

	$a_path = array();

	preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts );
	$s1= $parts[1];
	$s2= $parts[2];
	$s3= $parts[3];
	$s4= $parts[4];

	$tmp = 5;
	$pocet = 0;
	while($tmp--){
		if ($parts[$tmp]<>0){
		   $pocet = $pocet + 1;
		}
	}	
	$p = $pocet;

   If($pocet>0){
   		while($pocet--){
//		echo $pocet."-";
			switch($pocet){
				case 3:
					$SEL = "SELECT * FROM m_acc_class WHERE s1=$s1 AND s2=$s2 AND s3=$s3 AND s4=$s4";
				break;
				case 2:
					$SEL = "SELECT * FROM m_acc_class WHERE s1=$s1 AND s2=$s2 AND s3=$s3 AND s4=0";
				break;
				case 1:
					$SEL = "SELECT * FROM m_acc_class WHERE s1=$s1 AND s2=$s2 AND s3=0 AND s4=0";
				break;
				case 0:
					$SEL = "SELECT * FROM m_acc_class WHERE s1<>0 AND s1=$s1 AND s2=0 AND s3=0 AND s4=0";
				break;
			}

			$tmp = $db->Query($SEL);
			$a_path[$pocet]["class_desc"] = $db->getResult($tmp, 0,"class_descr");
			$a_path[$pocet]["men"] = "men".$db->getResult($tmp, 0,"s1").".".$db->getResult($tmp, 0,"s2").".".$db->getResult($tmp, 0,"s3").".".$db->getResult($tmp, 0,"s4");
			$a_path[$pocet]["path"] = $db->getResult($tmp, 0,"path");
			$a_path[$pocet]["class_abbrev"] = $db->getResult($tmp, 0, "class_abbrev");
			$a_path[$pocet]["zobraz"] = $db->getResult($tmp, 0, "zobraz");
		}
   }
   
	return $a_path;
}
?>
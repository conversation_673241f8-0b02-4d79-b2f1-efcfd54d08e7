<?PHP
function go_email_user(&$db, $typ, $kraj, &$a_mag, $magazine=-1){
	$SEL="select * from m_mail_disable where item=$typ";
	$tmp1=$db->Query($SEL);
	$j=$db->getNumRows($tmp1);
	while ($j--){
		$a[$db->getResult($tmp1, $j, "id_m")]=1;
	}

	//jestlize typ=5, pak neposilat uzi<PERSON>lum, kteri maji typ=-5 (posilat v emailu)
	$b=array();
	if ($typ==5){
		$SEL="select * from m_mail_disable where item=-5";
		$tmp1=$db->Query($SEL);
		$j=$db->getNumRows($tmp1);
		while ($j--){
			$b[$db->getResult($tmp1, $j, "id_m")]=1;
		}
	}
	
	$end_date = date("Y",mktime(0,0,0,date("m")-2,1,date("Y")));
	  //vybere pouze uzivatele, jenz maji zaplacene clenske prispevky a dle kraje
	  if (strval($kraj) === "-1"){
		$SEL="select id_m, email FROM m_members WHERE owner<>0 AND disable='N' AND prizpevky>=$end_date";
	  }else{
		$SEL="select id_m, email from m_members where owner<>0 AND disable='N' AND prizpevky>=$end_date AND (kraj=$kraj OR kraj=15)";
	  }
	$tmp = $db->Query($SEL);
	$i=$db->getNumRows($tmp);
	$k=0;
	$k2=0;
	while($i--){
		// kontrola, zda uzivatel nechce dostavat emaily
		if(@$a[$db->getResult($tmp, $i, "id_m")]!=1){
			// kontrola, zda se jedna o email
			if(preg_match("/^.+@.+\\..+$/", $db->getResult($tmp, $i, "email"))){
				//jestlize je typ 5, pak kontrola na zasislani emailu s prilohou
				if($typ==5 AND $magazine!=-1){
					if($b[$db->getResult($tmp, $i, "id_m")]==1){
						$a_mag[$k2]["id_m"]=$db->getResult($tmp, $i, "id_m");
						$a_mag[$k2]["email"]=$db->getResult($tmp, $i, "email");
						$k2++;
					} else {
						$a_u[$k]["id_m"]=$db->getResult($tmp, $i, "id_m");
						$a_u[$k]["email"]=$db->getResult($tmp, $i, "email");
						$k++;
					}
				} else {
					$a_u[$k]["id_m"]=$db->getResult($tmp, $i, "id_m");
					$a_u[$k]["email"]=$db->getResult($tmp, $i, "email");
					$k++;
				}
			}
		}
	}
	
	return $a_u;
}
?>

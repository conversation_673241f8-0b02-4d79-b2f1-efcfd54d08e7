<?php
/*
Sean <PERSON> CURL library

This library is a basic implementation of CURL capabilities.
It works in most modern versions of IE and FF.

==================================== USAGE ====================================
It exports the CURL object globally, so set a callback with setCallback($func).
(Use setCallback(array('class_name', 'func_name')) to set a callback as a func
that lies within a different class)
Then use one of the CURL request methods:

get($url);
post($url, $vars); vars is a urlencoded string in query string format.

Your callback function will then be called with 1 argument, the response text.
If a callback is not defined, your request will return the response text.
*/

class CURL {
   var $callback = false;

function setCallback($func_name) {
   $this->callback = $func_name;
}

function doRequest($method, $url, $vars) {
	
   $ch = curl_init();

   curl_setopt($ch, CURLOPT_URL, $url);
   curl_setopt($ch, CURLOPT_HEADER, 1);
   curl_setopt($ch, CURLOPT_USERAGENT, $_SERVER['HTTP_USER_AGENT']);
   curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
   curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
   curl_setopt($ch, CURLOPT_COOKIEJAR, 'cookie.txt');
   curl_setopt($ch, CURLOPT_COOKIEFILE, 'cookie.txt');
   if ($method == 'POST') {
       curl_setopt($ch, CURLOPT_POST, 1);
       curl_setopt($ch, CURLOPT_POSTFIELDS, $vars);
   }
   $data = curl_exec($ch);

   curl_close($ch);

   if ($data) {
       if ($this->callback)
       {
           $callback = $this->callback;
           $this->callback = false;
           return call_user_func($callback, $data);
       } else {
           return $data;
       }
   } else {
       return curl_error($ch);
   }
}

function get($url) {
   return $this->doRequest('GET', $url, 'NULL');
}

function post($url, $vars) {
   return $this->doRequest('POST', $url, $vars);
}
}

/***************************
FUNKCE PRO OVLADANI
*******************************/
	function onLogin($data) {
		global $curlObject;
		global $action;
		
		$time = getTime($data);

		switch ($action){
			case "add":
				addUser($time);
			break;
			case "remove":
				removeUser($time);
			break;
			case "addfwd":
				addForward($time);
			break;
			case "delfwd":
				delForward($time);
			break;
			case "userfwd":
				addUserForward($time);
			break;
			
		}
    }

	function addUser($time) {
		global $domain;
		global $user;
		global $password2;
		global $curlObject;
		global $nforward;
		
		$data = "";
		$data .= "newu=".$user."&";
		$data .= "domain=".$domain."&";
		$data .= "password1=".$password2."&";
		$data .= "password2=".$password2."&";
		

		$curlObject->setCallback("onUserAdd");
		
		$curlObject->post("http://admin.rage.cz:81/cgi-bin/qmailadmin/com/addusernow?user=postmaster&dom=mensa.cz&time=".$time."&", $data);

	}
	
	function addUserForward($time) {
		global $user;
		global $curlObject;
		global $nforward;
		
		$curlObject->setCallback("onUserAddForward");

		$data = "";
		$data .= "modu=".$user."&";
		$data .= "nforward=".$nforward."&";
		$data .= "cforward=forward&";
		$data .= "fsaved=on&";
		
		$curlObject->post("http://admin.rage.cz:81/cgi-bin/qmailadmin/com/modusernow?user=postmaster&dom=mensa.cz&time=".$time."&", $data);

	}

	function addForward($time) {
		global $fw;
		global $fwto;
		global $curlObject;
		
		$data = "";
		$data .= "alias=".$fw."&";
		$data .= "newu=".$fwto."&";
		

		$curlObject->setCallback("onForwardAdd");
		
		$curlObject->post("http://admin.rage.cz:81/cgi-bin/qmailadmin/com/adddotqmailnow?user=postmaster&dom=mensa.cz&time=".$time."&", $data);
	}
	
	function delForward($time) {
		global $delfw;
		global $curlObject;
		
		$data = "";
		$data .= "modu=".$delfw."&";
		

		$curlObject->setCallback("onForwardDel");
		
		$curlObject->post("http://admin.rage.cz:81/cgi-bin/qmailadmin/com/deldotqmailnow?user=postmaster&dom=mensa.cz&time=".$time."&", $data);
	}

	function removeUser($time) {
		global $domain;
		global $user;
		global $forwardTo;
		global $curlObject;
		
		$data = "";
		$data .= "deluser=".$user."&";

		if ($forwardTo != "") {
			$data .= "forward=on&";
			$data .= "forwardto=".$forwardTo."&";
		}

		$curlObject->setCallback("onUserDel");
		
		$curlObject->post("http://admin.rage.cz:81/cgi-bin/qmailadmin/com/delusernow?user=postmaster&dom=mensa.cz&time=".$time."&", $data);
	}

	function getTime($data) {
		return get_string_between($data, "&time=", "&dom=");		
	}

	function onUserAdd($data) {
		echo getResultMessage($data);
	}

	function onUserDel($data) {
		echo getResultMessage($data);
	}
	
	function onForwardDel($data) {
		echo getResultMessage($data);
	}
	
	function onForwardAdd($data) {
		echo getResultMessage($data);
	}
	
	function onUserAddForward($data) {
		echo getResultMessage($data);
	}
	
	

	function getResultMessage($data) {
		return get_string_between($data, "<h2>", "</h2>");
	}
	

	function get_string_between($string, $start, $end){
        $string = " ".$string;
	    $ini = mb_strpos($string,$start);
	    if ($ini == 0) return "";
	    $ini += mb_strlen($start);
	    $len = mb_strpos($string,$end,$ini) - $ini;
        return mb_substr($string,$ini,$len);
	}

	function slogin($username, $domain, $password) {
		global $curlObject;

		$curlObject->setCallback("onLogin");

		$loginData = "";
		$loginData .= "username=".$username."&";
		$loginData .= "domain=".$domain."&";
		$loginData .= "password=".$password."&";

		$curlObject->post("http://admin.rage.cz:81/cgi-bin/qmailadmin", $loginData);

	}



?>

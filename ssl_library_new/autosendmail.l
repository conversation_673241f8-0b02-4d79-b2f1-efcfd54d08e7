<?php
// <meta http-equiv="Content-Type" content="text/html; charset=utf-8">

/**
  utf-8
  autosendemail.I

  Implementace rozesílání zpráv, které jsou zadány
  v rozhraní pro rozesílání zpráv.

  Rozesílání je asynchronní, webová stárnka zprávu
  pouze uloží, tento automat jí vybere z databáze
  a rozešle. Více viz logická strutura kódu.


  Původním autorem kódu je:
  <PERSON> <<EMAIL>>


  Princip:

 * v tabulce m_ae_mails jsou zprávy čekající na odeslání
  (a také odeslané)

 * skript pravidelně tabulku projde a pokusí se odeslat
  neodeslané zprávy

 * na každou zprávu v tabulce zavolá function mailsend($maildata)

 * zprávy se rozesílaj<PERSON> po skupinách

 * zprávy jsou objekty třídy definované v:
  class.phpmailer.php
  http://phpmailer.worxware.com/

  Pozor: je tam několik úprav kvůli zpětné adrese (return path)
  Při aktualizaci je třeba zkontrolovat a opravit.


  Logická struktura kódu:

  V úvodu souboru autosendmail.I je definována funkce
  "function getNewMail($user, $maildata)", která sestaví jednu emailovou
  zprávu se vším všudy na základě vstupních parametrů a dat na čerpaných
  z globálních proměnných (definice konferencí atd).
  Sestavuje zprávu po věcné stránce (obsah).

  Tato funkce využívá třídu phpmailer() definovanou v class.phpmailer.php,
  pro samotné technické řešení emailu (všechny drobnosti, kódování,
  hlavičky atd.). Tato třída je využita se základními parametry,
  používá se normální PHP send. V třídě je provedeno několik úprav s cílem
  dostat do return-<NAME_EMAIL>, ale žádný z nich zatím
  neměl vliv, jinak se jedná o generický OpenSource kód.

  Funkce getNewMail do třídy vloží jak specifické informace pro danou
  zprávu (tělo, odesílatel), tak hafo obecných standardizovaných informací
  pro všechny mensovní zprávy jako uživatelské patičky,
  konference a reply-to (v závislosti na typu zprávy) atd.

  Funkce mailsend($maildata) potom generuje výše zmíněné jednotlivé maily
  a posílá je zadaným příjemcům - přidává do vytvořeného emailu seznam
  adresátů a systemticky jí odesílá. Odesílání funguje v dávkách.
  Nyní nevím, jak velkou dávku požíváme, možná je dávka velikosti 1.
  Tato funkce se také stará o sestavení seznamu příjemců každé zprávy.

  autosendmail.I je volán periodicky. Při každém běhu projde seznam nových
  zpráv uložených do databáze (zde se pod pojemem zpráva myslí zpráva
  definovaná uživatelem, která může být rozeslána tisícovce adresátů)
  a pro každou zprávu definovanou v databázi () zavolá mailsend, který
  zajistí sehnání příjemců, emailů a rozeslání jednotlivých zpráv.


  Změnovník:
  2014-11-20, TK: Vypnuti jednoho echo ...
  2013-09-14, TK: Pridano precedence bulk pri vytvareni zpravy.
  2013-03-01, TK: Zprávu může rozeslat jen člen s povoleným profilem a maximálně 5 za den.
  2012-10-05, TK: Doplnění popisu logické struktury programu
  2012-07-29, TK: Oprava podmínek pro ověření členství
  2012-07-28, RE: Doplnění komentářů
  2012-07-27, TK: Nastavení returnpath na: <EMAIL>
 */

use \helpers\Mail;
use \mailer\MailerFactory;

/**
 * @param array $maildata
 * @param string $mail_prepend_html
 * @param string $mail_append_html
 * @return array
 */
function assembleMailData($maildata, $mail_prepend_html, $mail_append_html)
{
    if ($maildata['html_body']) {
        if (preg_match('~(.*?<body[^>]*>)(.*?)(</body.*)~', $maildata['html_body'], $bodyparts)) {
            $maildata['html_body'] = $bodyparts[1] . $mail_prepend_html . $bodyparts[2] . $mail_append_html . $bodyparts[3];
        }
        else {
            $maildata['html_body'] = $mail_prepend_html . $maildata['html_body'] . $mail_append_html;
        }
    }

    if ($maildata['text_body']) {
        $maildata['text_body'] = $mail_prepend_html . Mail::toPhpMailerAltBody($maildata['text_body']) . $mail_append_html;
    }

    return $maildata;
}

/**
 * Sestavi zpravu
 * objekt phpmailer();
 * definováno v: class.phpmailer.php
 *
 * @param array $user
 * @param array $maildata
 */
function getNewMail($user, $maildata)
{
    // většina proměnných jde z konfigurace
    // includovaná na začátku mainu
    global $db, $konference, $mail_append_html, $uzivatele;

    $mailer = MailerFactory::getMailer();

    // konference
    if ($maildata['konference']) {
        $mail = $mailer->getAutoSendMailer(
            $konference[$maildata['konference']]['alias'],
            $konference[$maildata['konference']]['name']
        );

        // uprav, pokud se jedná o speciální konferenci
        if ($konference[$maildata['konference']]['alias'] == '<EMAIL>') {
            // casopis, odpovidej do redakce
            $mail->AddReplyTo("<EMAIL>", "Redakce časopisu Mensa");
        }
        else {
            // 2022-08-23: defaultne se vzdy odpovida odesilateli.
            $mail->AddReplyTo($user['email'], "{$user['jmeno']} {$user['prijmeni']}");
        }

        // 2017-04-21, TK: po nekolika incidentech jsou odpovedi do ms praha odesilateli
        /*
        elseif ($konference[$maildata['konference']]['alias'] == '<EMAIL>'
            || $konference[$maildata['konference']]['alias'] == '<EMAIL>'
            || $konference[$maildata['konference']]['alias'] == '<EMAIL>') {
            // superkonference - odpovídej odesílateli
            $mail->AddReplyTo($user['email'], "{$user['jmeno']} {$user['prijmeni']}");
        }
        else {
            // normální konference, odpovídej do konference
            $mail->AddReplyTo(
                $konference[$maildata['konference']]['alias'],
                $konference[$maildata['konference']]['name']
            );
        } */

        $mail_prepend_html = "
            <strong>Typ:</strong> Zpráva z konference " . htmlspecialchars($konference[$maildata['konference']]['name']) . "<br />
            <strong>Odesílatel:</strong> " . htmlspecialchars($maildata['sender_name']) .
            " (<a href='mailto: " . htmlspecialchars($maildata['sender_email']) . "'>" . htmlspecialchars($maildata['sender_email']) . "</a>)
            <hr />
        ";

        $maildata = assembleMailData($maildata, $mail_prepend_html, $mail_append_html);
    }
    // jiné hromadné rozesílání
    else {
        $mail = $mailer->getAutoSendMailer($user['email'], "{$user['jmeno']} {$user['prijmeni']}");

        $kraj_list = "";
        $kraj_src = count($maildata['x_uziv']) > 2 ? "kraj_abbr" : "kraj_name";

        // TODO pdo or db_model
        $q_kraje = $db->query("SELECT id_kraj, $kraj_src FROM m_list_kraj WHERE kraj_name NOT LIKE '---%' ORDER BY id_kraj"); //podminkou "not like" se vyradi kraj "----neuveden----"
        $kraje = $db->FetchAssocKey($q_kraje, "id_kraj");

        foreach ($maildata['x_kraje'] as $kraj) {
            if ($kraj_list != "") {
                $kraj_list .= ", ";
            }

            $kraj_list .= $kraje[$kraj][$kraj_src];
        }

        $uziv_list = "";

        foreach ($maildata['x_uziv'] as $uziv) {
            if ($uziv_list != "") {
                $uziv_list .= ", ";
            }

            $uziv_list .= $uzivatele[$uziv];
        }

        $mail_prepend_html = "
            <strong>Typ:</strong> E-mailová zpráva z kategorie {$maildata['type_name']}<br />
            Rozesláno do krajů: {$kraj_list}<br />
            Rozesláno členům Mensy: {$uziv_list}<br />
            <hr />
        ";

        $maildata = assembleMailData($maildata, $mail_prepend_html, $mail_append_html);
    }

    $mail->Subject = SUBJECT_PREFIX . $maildata['subject'];

    if ($maildata['html_body']) {
        $mail->IsHTML(true);
        $mail->Body = $maildata['html_body'];

        if ($maildata['text_body']) {
            $mail->AltBody = $maildata['text_body'];
        }
    }
    else {
        $mail->Body = $maildata['text_body'];
    }

    // TODO pdo or db_model
    $result = $db->query("SELECT id_att, filename, contenttype FROM m_ae_atts WHERE id_mail='{$maildata['id_mail']}'");

    while ($att = $db->FetchAssoc($result)) {
        $mail->AddAttachment(
            "../files/auto_email2/{$att['id_att']}.att",
            $att['filename'],
            "base64",
            $att['contenttype']
        );
    }

    return $mail;
}

/**
 * Ziska seznam jmen a e-mailovych adres prijemcu dane -obecne- e-mailove zpravy
 *
 * @param array mail
 * @return mysqli_result
 */
function get_generic_recipients($mail)
{
    global $db, $uzivatele;


    // zjistit vychozi nastaveni zasilani zprav tohoto typu
    // default_send = 1 - posilat vsem korm odhlasenych
    // default_send = 0 - posilat jen prihlasenym - kariera/komercni nabidky
    $type_default_send = $db->query("SELECT default_send FROM m_ae_types WHERE id_type={$mail['typ']}");
    if (!$type_default_send || $db->getNumRows($type_default_send) == 0)
    {
        throw new Exception("Chybný typ zprávy");
    }
    // list() is used to assign a list of variables in one operation.
    // v tomto pripade do $type_default_send uzlozi vysledek provniho sloupce dotazu (ktery vraci jen jeden sloupec)
    list($type_default_send) = $db->FetchRow($type_default_send);

    //seznam cilovych kraju a uzivatelskych skupin
    // je ulozen v objektu zpravy a byl tam naplnen volajicim
    $kraje = $mail['x_kraje'];
    $uziv = $mail['x_uziv'];
    $l_kraje = implode(',', $kraje);

    // uzivatele dospeli, deti, ostatni typy - to jsou zahrnaicini clenove atd.
    //u uzivatelu je to slozitejsi, protoze 0 znamena "vsechny ostatni"
    $uziv_cond = '';
    if (in_array(0, $uziv))
    { //"other" group included
        $exclude = array(); //send to all groups except unselected
        foreach ($uzivatele as $u => $val)
        {
            if ($u == 0)
                continue;
            if (!in_array($u, $uziv))
                $exclude[] = $u;
        }
        if (count($exclude) == 0)
        {
            // posli vsem
            $uziv_cond = '1';
        }
        else
        {
            // pole m_members.clen znamena typ clena 1 - dospely, 2 dite
            $uziv_cond = 'm_members.clen NOT IN (' . implode(',', $exclude) . ')';
        }
    }
    else
    { //skupina "other" not included
        $include = array(); //send to selected groups only
        foreach ($uzivatele as $u => $val)
        {
            if ($u == 0)
                continue;
            if (in_array($u, $uziv))
                $include[] = $u;
        }
        if (count($include) == 0)
        {
            $uziv_cond = '0';
        }
        else
        {
        // pole m_members.clen znamena typ clena 1 - dospely, 2 dite
            $uziv_cond = 'm_members.clen IN (' . implode(',', $include) . ')';
        }
    }


    //vybrat jmena a emaily cilovych uzivatelu
    // $type_default_send = 1 - posilat vsem krom odhlasenych
    // $type_default_send = 0 - posilat jen prihlasenym - kariera/komercni nabidky
    // tj. pokud je default_send = 1, v selectu bude podminka, ze uzivatelske zaskrtnuti m_ae_memtcfg.checked = 0
    // pointa je, ze pro tyto konference to, ze je policko zaskrtnute NENI ULOZENO, uklada se jen pokud je nezaskrtnute
    // pouze pokud si vypnu odber, tak se ulozi, ze pro dany typ zprav a kraj mam 0
    // obracene pro $type_default_send = 0
    $checked_cmp_sign = $type_default_send ? '=' : '>';
    $recipients = $db->query(
        "SELECT
      CONCAT(m_members.jmeno, ' ', m_members.prijmeni) AS name,
      m_members.email AS email
    FROM
      m_members
      LEFT JOIN m_ae_memtcfg
        ON
          m_members.id_m=m_ae_memtcfg.id_m
          AND m_ae_memtcfg.id_kraj IN ($l_kraje)
          AND m_ae_memtcfg.id_type = ${mail['typ']}
          AND m_ae_memtcfg.checked $checked_cmp_sign 0
    WHERE
      m_members.prizpevky>= (year(now()) - 1)                   -- má zaplacené příspěvky
      AND (
            m_members.ukonceni_clenstvi_poplatky = 0            -- neukončil členství
            OR m_members.ukonceni_clenstvi_poplatky > year(now()) -- případně jej ukončí v budoucnu
            -- počítáme, že jakmile má někdo vyplněno ukenčení pro tento rok, již členem být nechce
            -- ač by samozřejmě mohlo dojít k výjimce, teoreticky by mohl někdo zaplatit
            -- a říci, že toto je poslední rok
          )
      " . ($type_default_send ? '' : 'AND m_ae_memtcfg.checked IS NOT NULL') . "
      AND m_members.disable='N'                                 -- nemá blokovaný profil (při zrušení členství se profil blokuje)
      AND m_members.email LIKE '%@%'                            -- má email
      AND $uziv_cond
    GROUP BY email" . ($type_default_send ? "
    HAVING
      COUNT(m_ae_memtcfg.id_m) < " . count($kraje) : '')
    );
    return $recipients;
}



/**
 * Ziska seznam jmen a e-mailovych adres prijemcu dane -konferencni- e-mailove zpravy
 *
 * @param array mail
 * @return mysqli_result
 */
function get_conf_recipients($maildata)
{

    // prispevky min jsou nastavene v mainu
    // aby se posilalo jen lidem co maji zaplaceno
    global $db, $konference;

    $is_null = $konference[$maildata['konference']]['type'] == KONF_MEN ? '' : 'NOT';
    //pozitivni radek u mensovni konference znamena "vypnuto", u MS a SIGU je to "zapnuto" (vzdy opak vychoziho)

    $recipients = $db->query(
        "SELECT
          CONCAT(m_members.jmeno, ' ', m_members.prijmeni) AS name,
          m_members.email AS email
        FROM
          m_members
          LEFT JOIN m_ae_konfcfg
            ON
              m_members.id_m=m_ae_konfcfg.id_m
              AND m_ae_konfcfg.id_konf={$maildata['konference']}
        WHERE
              prizpevky>=(year(now()) - 1)
          AND m_members.disable='N'
          AND (
            m_members.ukonceni_clenstvi_poplatky = 0            -- neukončil členství
            OR m_members.ukonceni_clenstvi_poplatky > year(now()) -- případně jej ukončí v budoucnu
          )

          AND m_members.email LIKE '%@%'
          AND m_ae_konfcfg.id_konf IS $is_null NULL
        GROUP BY email");

    return $recipients;
}





/**
 * Zpracuje jednu nerozeslanou zprávu pro vsechny adresaty.
 *
 * Mail data je naplnene touto radkou

  sender_name
  sender_email
  id_mail
  id_m
  cas
  typ
  pro_uziv
  kraje
  konference
  subject
  html_body
  text_body
  odeslano
  uid
  type_name
 *
 *
 * @global datab $db
 * @global array $konference
 * @param array $maildata
 * @return boolean
 */
function mailsend($maildata)
{
    global $db, $konference;

    //nacist seznam prijemcu
    if ($maildata['konference'] === null)
    {
        // preved binarni masky toho kam posilat na pole
        $kraje = array();
        for ($i = 0; $i < 64; $i++)
        {
            $kraj = pow(2, $i);
            if ($kraj & $maildata['kraje'])
            {
                $kraje[] = $i;
            }
        }
        $uziv = array();
        for ($i = 0; $i < 32; $i++)
        {
            $u = pow(2, $i);
            if ($u & $maildata['pro_uziv'])
            {
                $uziv[] = $i;
            }
        }
        $maildata['x_kraje'] = $kraje;
        $maildata['x_uziv'] = $uziv;
        $recipients = get_generic_recipients($maildata);
    }
    else
    {
        $recipients = get_conf_recipients($maildata);
    }

    // nacist data o odesilateli
    // musi mit povoleny profil a zaplacene prispevky
    $user = $db->query("SELECT * FROM m_members WHERE id_m='{$maildata['id_m']}' and m_members.disable='N' and prizpevky>=(year(now()) - 1)");
    if ($db->getNumRows($user) == 0)
        return false;


    // over, zda odesilatel neodeslal moc zprav
    // a zabij proces pokud ano
    $q_pocet_odeslanych = $db->query("SELECT count(*) FROM m_ae_mails WHERE cas > DATE_SUB(NOW(), INTERVAL 1 DAY) and id_m = {$maildata['id_m']}");
    $r_pocet_odeslanych = $db->FetchArray($q_pocet_odeslanych);
    if ($r_pocet_odeslanych[0] >= 7)
    {
        //echo '<p>Za den nemůžete zaslat více než 7 zpráv.</p>';
        return false;
    }

    $user = $db->FetchAssoc($user);

    /////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////
    //poslat

    $mail = getNewMail($user, $maildata);
    $i = 0;

    /* DEBUG:
      $mail->AddAddress('<EMAIL>', 'Mensa Test');
      $mail->Send();
      die();
     */

    // toto vytvari log o tom, komu bylo poslano
    $aslogfile = dirname(__FILE__) . "/../logs/autosendmail-{$maildata['id_mail']}.log";
    file_put_contents($aslogfile, date('Y-m-d H:m') . " MAIL ID {$maildata['id_mail']} SENDING STARTED\n", FILE_APPEND);

    //file_put_contents($aslogfile, "db = {$db}, konference  = {$konference}\n", FILE_APPEND);
    //file_put_contents($aslogfile, "konference = " . print_r($konference, TRUE) . "\n", FILE_APPEND);
    file_put_contents($aslogfile, "maildata = " . print_r($maildata, TRUE) . "\n", FILE_APPEND);
    file_put_contents($aslogfile, "user = " . print_r($user, TRUE) . "\n", FILE_APPEND);
    file_put_contents($aslogfile, "mail = " . print_r($mail, TRUE) . "\n", FILE_APPEND);
    //file_put_contents($aslogfile, "recipients = " . implode(", ", $recipients) . "\n", FILE_APPEND);




    while ($rcpt = $db->FetchAssoc($recipients)) {
        //ochrana proti zacykleni, kdyby si nejaky vtipalek nastavil adresu ve svem uctu na adresu konference
        if ($maildata['konference'] !== null &&
            strtolower($rcpt['email']) == strtolower($konference[$maildata['konference']]['alias']))
            continue;

        //echo "vkladam adresu {$rcpt['email']}, "; // toto zbytecne pise do logu
        //flush();


        $mail->AddAddress($rcpt['email'], $rcpt['name']);

        // maily se posílají po skupinách
        // nesmí být moc příjemců najednou
        if (++$i >= SEND_GROUP_SIZE)
        {
            if ($mail->Send())
            {
                file_put_contents($aslogfile, date('Y-m-d H:m') . " SENT: {$rcpt['email']}\n", FILE_APPEND);
            }
            else
            {
                file_put_contents($aslogfile, date('Y-m-d H:m') . " FAIL: {$rcpt['email']}\n", FILE_APPEND);
                echo "Odeslání mailu na {$rcpt['email']} selhalo: " . $mail->ErrorInfo;
            }
            $mail->ClearAddresses();
            $i = 0;
        }
    }


    // pokud bylo posilani po skupinach a skoncili jsme, doposli posledni skupinu
    if ($i > 0)
    {
        if ($mail->Send())
        {
            file_put_contents($aslogfile, date('Y-m-d H:m') . " SENT: {$rcpt['email']}\n", FILE_APPEND);
        }
        else
        {
            file_put_contents($aslogfile, date('Y-m-d H:m') . " FAIL: {$rcpt['email']}\n", FILE_APPEND);
            echo "Odeslání mailu na {$rcpt['email']} selhalo: " . $mail->ErrorInfo;
        }
    }

    file_put_contents($aslogfile, date('Y-m-d H:m') . " SENDING DONE\n\n", FILE_APPEND);
}


///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
// main
///////////////////////////////////////////////////////////////////////////////


require_once '../ssl_pages_new/auto_email2/config.i';
require '../ssl_pages_new/auto_email2/konference.config.i';


// nacti informace o dosud neodeslanych zpravach
$q_to_send = $db->query(
    "SELECT
    CONCAT(m_members.prijmeni, ' ', m_members.jmeno) AS sender_name,
    m_members.email AS sender_email,
    m_ae_mails.*,
    m_ae_types.type_name
  FROM
    m_ae_mails
    LEFT JOIN m_ae_types ON m_ae_mails.typ=m_ae_types.id_type
    LEFT JOIN m_members ON m_ae_mails.id_m=m_members.id_m
  WHERE odeslano=0");

// pro všechny dosud neodeslané zprávy zkus odeslání
// raději nejdříve zapiš do db. že se odesílalo, aby se zprávy neposílaly vícekrát, pokud odejdou jen částečně
while ($mail = $db->FetchAssoc($q_to_send)) {
    try {
        $db->query("UPDATE m_ae_mails SET odeslano=1 WHERE id_mail='{$mail['id_mail']}'");
        mailsend($mail);
    }
    catch (Exception $ex) {
        echo "Chyba při odesílání zprávy na adresu {$mail}, text chyby: " . $ex->getMessage();
    }
}

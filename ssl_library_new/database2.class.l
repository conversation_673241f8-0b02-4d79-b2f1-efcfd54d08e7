<?PHP

/* * ********************************************************************
  AUTHOR:    (c) <PERSON>, <EMAIL>
  CREATED:   2007
  PROJECT:   MENSA CR, database interface
  DESC
  Main database object
  Objekt pro přístup k centrální databázi včetně regulace přístupu

  Kódování: utf-8

  /DESC

  HISTORYLIST

  Změnovník:
  2013-Oct-28, TK: Pridano zpracovani testovani.

  2013-Apr-10, TK: <PERSON><PERSON><PERSON><PERSON>, provedena kontrola aktuálnosti povolení

  /HISTORYLIST
 * ******************************************************************** */
require_once "/home/<USER>/ssl_library_2013/datab.class.php";

class database2 extends datab
{
    /**
     * @var string
     */
    protected $path = "../logs/mensasec_long_database.log"; // nepouzito, prerazeno

    /**
     * @var mysqli
     */
    private $conn2;

    /**
     * @var string
     */
    public $error;

    /**
     * @return bool
     */
    function access()
    {
        if (defined('AUTORUN') && AUTORUN) {
            return true;
        }
        if (defined('CARDPAYAPI') && CARDPAYAPI) {
            return true;
        }
        switch ($_REQUEST['men']) {
            // overeni platnosti
            // centrální databáze >> správa
            case "men19.2.0.0": //sprava/statistika - ok
            case "men19.2.1.0": //sprava/nove otestovano - ok
            case "men19.2.2.0": //sprava/vyhledavani - ok
            case "men19.2.3.0": //sprava/kontrola zmenenych adress - ok
            case "men19.2.4.0": //sprava/zpracování - ok
            case "men19.2.5.0": //sprava/koncici clen dm - ok
            case "men19.2.6.0": //sprava/export všech platných členů - ok
            case "men19.2.7.0": //sprava/rozsirene vyhledavani - ok
            case "men19.2.8.0": //sprava/export pro postservis - ok
            case "men19.2.10.0": //sprava/neplatici - ok
            case "men19.2.11.1": //testy Points - ok
            case "men19.2.12.0": // Správa členských průkazů, TK 2012-Feb-08
            case "men19.2.14.0": // Diagnostika, TK 2012-Feb-14
            case "men19.2.15.0": // Členové – přihlášky, TK 2012-Feb-28
            case "men19.2.16.0": // Platby kartou, TK 2012-Oct-10
            case "men19.2.17.0": // Platby na účet, TK 2012-Oct-10
            case "men19.2.18.0": // Členové – problémy a certifikáty, TK 2014-Feb-19
            case "men19.2.19.0": // Přehled - otestovaní, TK 2015-Jan-09
            // centrální databáze >>  testující
            case "men19.1.0.0": //testujici/vyhledavani
            case "men19.1.1.0": //testujici/vyhledavani
            // MIMO
            // administrace
            case "men3.13.1.0": // administrace/udrzba/kontrola paruDB
            case "men3.10.0.0": //administrace/test
            // interní dokumenty
            case "men14.5.0.0": // interní dokumenty, statistika počtu členů
            case "men14.5.4.0": // Mensa ČR v číslech, TK 2012-Feb-08

                return true;
            default:
                $this->error = "Chyba: Nemáte oprávnění využívat objekt db2, kontaktujte administrátora.";
                return false;
        }
    }

    /**
     * @return bool
     */
    function open()
    {
        if ($this->access()) {
            // DB Open
            $host = "localhost";
            $database = "mensaweb";
            $user = "mensasec";
            $password = getenv("MENSASEC_MAGIC");
            $this->path = "/home/<USER>/logs/mensasec_queries_" . date('Y-m-d') . ".log";

            $this->conn = $this->createConnection($host, $user, $password);
            $this->SelectDatabase($database);

            // 2016-09-04
            // po migraci databaze na utf-8
            // $this->doQuery("SET character_set_connection = cp1250");
            // $this->doQuery("SET character_set_client = cp1250");
            // $this->doQuery("SET character_set_results = cp1250");

            // https://www.interval.cz/clanky/mysql-cestina-a-slovenstina/
            // Pokud například pracujete s mezinárodní databází používající UTF-8 a pro své klientské rozhraní
            // potřebujete, aby se s daty pracovalo ve Windows 1250, můžete použít následující příkaz
            // a vše se rázem samo vyřeší:
            $this->doQuery("SET NAMES utf8");

            return (bool) $this->conn;
        } else {
            echo "<p><font color=\"red\"><br>";
            echo "Chyba: Nemáte oprávnění využívat objekt db2, kontaktujte administrátora.";
            echo "<br></font></p>";
            return false;
        }
    }

    /**
     * @return void
     */
    function close()
    {
        $this->closeConnection($this->conn);
    }

    /**
     * @param $SQL
     * @return false|mysqli_result
     */
    function Query($SQL)
    {
        if ($this->access()) {
            $tmp = $this->doQuery($SQL);

            if ($tmp) {
                return $tmp;
            }
            else {
                $this->error = $this->getError();
                echo "<p><font color=\"red\"><br>" . $SQL . "<br>";
                echo $this->error;
                echo "<br></font></p>";

                return false;
            }
        }
        else {
            return false;
        }
    }

    /**
     * @param mysqli_result $result
     * @return array
     */
    public function FetchAssocAll($vysledek)
    {
        return parent::FetchAssocAll($vysledek);
    }
}

<?PHP
	/**
		Vykresli tabulky sestavenou na zaklade dotazu,
		zahlavi bude sestaveno ze jmen policek, tak jak je vratil dotaz.

		Tazbulka bude mit html class: draw_table_from_query
			vsechny sloupce maji tridu jejichz nazev odpovida zahlavi po nahrazeni specialnich znaku a mezer podtrzitky

		Liche radky budou mit trido odd, sude even pro pripadne obarveni.

		&$db			link na databazi
		$htmlid,		unikatni id tbulky pouzite v kodu (napr pro skinovani pomoci css)
		$sql_query	text dotazu
		$konverze		jmeno funkce, ktera se zavola pro kazdou datovou bunku pro upravu jeji hodnoty
							  vola se jako: $konverze($key, $value), musi vratit reteze, ktery sep ouzije jako hodnota bunky
		$filtr      pole nazvu bunek, ktere se maji zobrazit ( null = zobrazi se vsechny)
		$rozsireni  jmeno funkce, ktera se zavola pro kazdy radek za ucelem zobrazeni doplnkoveho radku
		            vola se jako: $rozsireni($asociativni_pole_radku)

		return:			vrati pocet vypsanych radku


		Proklad konverzni funkce
		function link_na_profil ($key, $value)
		{
			if ($key == 'Profil') return "<a target='_new' href='./index.php?men=men19.2.2.0&c_id_m=$value'>profil</a>";
			return $value;
		}


		Poslední úpravy:
		    2022-Jul-12, HK: Rrozříření parametrizovatelnosti pro platby kartou
		    2015-Jan-04, TK: Upravy po prechodu na novou verzi databazove tridy.
				2012-Feb-10, Tomáš Kubeš: doplněno čištění jmen tříd a opravena chyba v počítání
				2012-Feb-07, Tomáš Kubeš: založeno

        Seznam strasnek, kde se funkce pouziva k 2015-Jan-04:
            https://intranet.mensa.cz/index.php?men=men19.2.18.0
            https://intranet.mensa.cz/index.php?men=men19.2.15.0
            https://intranet.mensa.cz/index.php?men=men19.2.10.0
            https://intranet.mensa.cz/index.php?men=men19.2.5.0
            https://intranet.mensa.cz/index.php?men=men19.2.14.0
            https://intranet.mensa.cz/index.php?men=men19.2.16.0
	*/


	function draw_table_from_query (&$db, $htmlid, $sql_query, $konverze = '', $filtr = null, $rozsireni = '')
	{
		// vstupni kontrola
		if (strlen($sql_query) < 10) return 0;

		// proved dotaz
		$vysledek = $db->Query($sql_query);
		//$vysledek = $db->doQuery($sql_query);
     	if ($db->getNumRows($vysledek) === 0) return 0; // neni co vypisovat

		$prvni_rada = TRUE;
		$pocet = 0;
		$pocet_sloupcu = 0;
        //print_r($vysledek);
        //return 0;

		// priprav zahlavi
		echo "<table class='draw_table_from_query' id='$htmlid'>\n";

		// projed vsechna data (vrat pouze asociativni pole)
        //$seznam=$db->FetchAssocAll($vysledek);
        //print_r($seznam);
        //return 0;

		while ($r = $db->FetchAssoc($vysledek))
		{
			// pred prvnim radkem vytiskni zahlavi
			if ($prvni_rada)
			{
				echo "<thead>\n\t<tr class='draw_table_from_query-zahlavi'>\n";
				foreach (array_keys($r) as $zahlavi)
				{
				  if (is_null($filtr) or in_array($zahlavi, $filtr))
				  {
				    echo "\t\t<th class='".strtr($zahlavi, ".,?<>/ ", "_______")."'>$zahlavi</th>\n";
				    $pocet_sloupcu++; 
				  }
				}
				echo "\t</tr>\n</thead>\n\n\n<tbody>\n";
				$prvni_rada = FALSE;
			}

			// vypis data radku, davej stridavou tridu pro pripadne obarven
			echo "\t<tr class='".(($pocet % 2)?"even":"odd")."'>\n";
			foreach ($r as $key => $value)
			{
			  if (is_null($filtr) or in_array($key, $filtr))
			  {
          // pokud existuje konverzni funkce, aplikuj ji na vystupni hodnotu
          if ($konverze == '')
            echo "\t\t<td class='".strtr($key, ".,?<>/ ", "_______")."'>$value</td>\n";
          else
            echo "\t\t<td class='".strtr($key, ".,?<>/ ", "_______")."'>".$konverze($key, $value)."</td>\n";
				}
				else
				{
				  echo "<!-- $key -->";
				}
			}
			echo "\t</tr>\n\n";
			
			if (function_exists($rozsireni))
			{
			  $rozsireni_data = $rozsireni($r);
			  if (is_array($rozsireni_data))
			  {
			    foreach ($rozsireni_data as $rozsireni_radek)
			    {
            echo "\t<tr class='".(($pocet % 2)?"even":"odd")."'>\n";
            echo "\t\t<td colspan='" . $pocet_sloupcu . "' class='rozsireni'>" . $rozsireni_radek . "</td>\n";
            echo "\t</tr>\n\n";			      
			    }
			  }
			  else
			  {
          echo "\t<tr class='".(($pocet % 2)?"even":"odd")."'>\n";
          echo "\t\t<td colspan='" . $pocet_sloupcu . "' class='rozsireni'>" . $rozsireni_data . "</td>\n";
          echo "\t</tr>\n\n";
        }
			}

			$pocet ++;
		}

		echo "</tbody>\n</table>\n\n";
		return $pocet;
	}

?>

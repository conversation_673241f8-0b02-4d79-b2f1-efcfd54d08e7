<?PHP

/* * ********************************************************************
  AUTHOR:    (c) Roman Brzuska, <EMAIL>
  CREATED:   2007
  PROJECT:   MENSA CR, database interface
  DESC
  Main database object
  /DESC
  HISTORYLIST
  /HISTORYLIST
 * ******************************************************************** */

/* * ********************************************************************
  References:
  - utf8
 * ********************************************************************* */
require_once  __DIR__ . "/../ssl_library_2013/datab.class.php";

class database extends datab
{
    /**
     * @return bool
     */
    public function open()
    {
        date_default_timezone_set('Europe/Prague');

        $host = getenv("MENSAWEB_HOST") ?: "localhost";
        $database = "mensaweb";
        $user = getenv("MENSAWEB_USER") ?: "mensaweb";
        $password = getenv("MENSAWEB_MAGIC");
        $this->path = "/home/<USER>/logs/mensaweb_queries_" . date('Y-m-d') . ".log";

        $this->conn = $this->createConnection($host, $user, $password);
        setlocale(LC_CTYPE, 'cs_CZ');

        $this->SelectDatabase($database);

        // https://www.interval.cz/clanky/mysql-cestina-a-slovenstina/
        // Pokud například pracujete s mezinárodní databází používající UTF-8 a pro své klientské rozhraní
        // potřebujete, aby se s daty pracovalo ve Windows 1250, můžete použít následující příkaz
        // a vše se rázem samo vyřeší:
        $this->doQuery("SET NAMES utf8");

        return (bool) $this->conn;
    }

    /**
     * @return void
     */
    public function close()
    {
        $this->closeConnection($this->conn);
    }

    /**
     * @param string $query
     * @return mysqli_result|false
     */
    public function Query($query)
    {
        return $this->doQuery($query);
    }

    /**
     * @param mysqli_result $result
     * @return array
     */
    public function FetchAssocAll($result)
    {
        return parent::FetchAssocAll($result);
    }

    /**
     * @param string $input
     * @return string
     */
    public function escape($input)
    {
        return mysqli_real_escape_string($this->conn, $input);
    }
}

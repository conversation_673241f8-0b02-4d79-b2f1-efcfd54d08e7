<?PHP
Function downgrade_menu($men){
	preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts );
	$s1= $parts[1];
	$s2= $parts[2];
	$s3= $parts[3];
	$s4= $parts[4];

	if($s4==0){
		// pokracuj dale
		if($s3==0){
			// pokracuj dale
			if($s2==0){
				// pokracuj dale
				$s1=0;
			} else {
				$s2=0;
			}
		} else {
			$s3=0;
		}
	} else {
		$s4=0;
	}

	return "men".$s1.".".$s2.".".$s3.".".$s4;
}
?>
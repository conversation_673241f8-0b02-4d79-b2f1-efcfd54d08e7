<?PHP
/*
NUTNO NASTAVIT JAKO SKRIPT, KTERY TABULKU VYKONAVA
$replace_s_content

Posledni uprava 2016-02-12, TK: zmena vypisu roku v tom do kdy je dotycny clenem <PERSON>, preformatovani kodu.

*/

// nebyl nalezen zadny zaznam
if (count($a_users) == 0) {
    echo "<p align=\"center\"><PERSON>le zadaných kritérií nebyl nalezen žádný záznam.";
    return;
}



// mame vysledky ke zobrazeni
?>
<table border="0" cellspacing="0" style="margin: 2em 0 2em 0; width: 100%; padding: 0 15px">
    <tr style="padding:1px 1px 1px 3px;">
        <td align="left"><p><b>Jméno</b></td>
        <td align="left"><p><b>Příjmení</b></td>
        <td align="left"><p><b><PERSON><PERSON></b></td>
        <td align="left"><p><b>Příslušnost</b></td>
        <td align="center"><p>&nbsp;&nbsp;</td>
    </tr>
    <?PHP
    for ($j = 0; $j < count($a_users); $j++) {
        // barevne podlozeni radku
        IF ($j % 2 == 1) {
            $bgcolor = "";
        } else {
            $bgcolor = "bgcolor=\"#AEBFF2\"";
        }
        // 2016-02-12, TK: odstranen rok, protoze tady by bylo tezke ziskat ho spravne
        // if($a_users[$j]["prizpevky"]<date("Y")){
        //	$do="do r. ".$a_users[$j]["prizpevky"];
        //} else {
        //	$do="";
        //}

        ?>


        <tr <?PHP echo $bgcolor ?> style="padding:1px 1px 1px 3px;">
            <td style="padding-right: 3em;"><p><?PHP echo $a_users[$j]["jmeno"] ?>&nbsp;</td>
            <td style="padding-right: 3em;"><p><?PHP echo $a_users[$j]["prijmeni"] ?>&nbsp;</td>
            <td style="padding-right: 3em;"><p><?PHP echo $a_users[$j]["kraj"] ?></td>
            <td style="padding-right: 3em;"><p><?PHP echo $a_users[$j]["prislusnost"] ?></td>
            <td align="right"><p>&nbsp;<a target="_blank" href="index.php?men=<?PHP echo $men ?>&s_content=view.i&id_m=<?PHP echo $a_users[$j]["id_m"] ?>">více</a>&nbsp;
            </td>
        </tr>
        <?PHP
    }
    ?>
</table>


<?PHP
/*
 * // zobrazeni vice stranek bylo vypnuto
$celkem = $total;
if (!isset($start))
    $start = 0;
$step = 40;
$url = "index.php?men=".$men."&s_content=".$replace_s_content."&vyhledat=vyhledat";
echo strankovani($celkem, $start, $step, $url);
*/
if ($j > 39) echo "<p>Vyhledávání zobrazí maximálně 40 výsledků. Pokud mezi nimi hledaná osoba není, prosím, <a href='./index.php?men=men4.1.0.0'>upřesněte vyhledávací kritéria</a>.</p>";

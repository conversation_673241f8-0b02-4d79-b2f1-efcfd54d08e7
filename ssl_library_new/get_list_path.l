<?PHP
// funkce vraci umisteni v menu
function get_list_path($men, &$db){

	preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts );
	$s1= $parts[1];
	$s2= $parts[2];
	$s3= $parts[3];
	$s4= $parts[4];

	$SEL="SELECT class_descr, class_abbrev, path FROM m_acc_class WHERE s1<>0 AND s1=$s1 AND s2=$s2 AND s3=$s3 AND s4=$s4 ";
	$tmp=$db->query($SEL);

	if(@$db->getNumRows($tmp)==1){
		$a_path["class_descr"]=$db->getResult($tmp, 0, "class_descr");
		$a_path["class_abbrev"]=$db->getResult($tmp, 0, "class_abbrev");
		$a_path["path"]=$db->getResult($tmp, 0, "path");
		return $a_path;
	} else {
		return false;
	}
}
?>

<?PHP
function go_not_sale(&$db){

	$datum = (date("Y")-1);
	$SEL="select id_m, email from m_members WHERE disable='N' AND prizpevky = $datum AND clen=1";
	$tmp = $db->Query($SEL);
	$i=$db->getNumRows($tmp);
	$k=0;
	while($i--){
		// kontrola, zda se jedna o email
		if(preg_match("/^.+@.+\\..+$/", $db->getResult($tmp, $i, "email"))){
			$a_u[$k]["id_m"]=$db->getResult($tmp, $i, "id_m");
			$a_u[$k]["email"]=$db->getResult($tmp, $i, "email");
			$k++;
		}
	}
	return $a_u;
}
?>
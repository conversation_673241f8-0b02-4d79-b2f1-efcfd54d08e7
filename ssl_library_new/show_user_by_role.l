<?PHP

/**
 * V<PERSON>pise u<PERSON><PERSON><PERSON>, kteri maji prirazenu danou roli.
 * @param $role_id id role
 * @param $db databazovy objekt
 * @return array id_m, jmeno, prijmeni, osobni_cislo, prizpevky, disable
 */
function show_user_by_role($role_id, &$db)
{
    $a_users = array();

    $SEL = "SELECT 
			m.id_m, m.jmeno, m.prijmeni, m.clen_cislo, m.prizpevky, m.disable 
		FROM 
			m_members m,
			m_acc_uid2role u2r
		WHERE 
			u2r.role_id=$role_id AND
			u2r.id_m=m.id_m
		ORDER BY
			m.disable desc, prijmeni desc, jmeno desc";

    $tmp = $db->Query($SEL);
    $i = $db->getNumRows($tmp);
    while ($i--) {
        $a_users[$i]["id_m"] = $db->getResult($tmp, $i, "id_m");
        $a_users[$i]["jmeno"] = $db->getResult($tmp, $i, "jmeno");
        $a_users[$i]["prijmeni"] = $db->getResult($tmp, $i, "prijmeni");
        $a_users[$i]["osobni_cislo"] = $db->getResult($tmp, $i, "clen_cislo");
        $a_users[$i]["prizpevky"] = $db->getResult($tmp, $i, "prizpevky");
        $a_users[$i]["disable"] = $db->getResult($tmp, $i, "disable");
    }
    return $a_users;
}

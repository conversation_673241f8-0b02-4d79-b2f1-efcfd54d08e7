<?PHP

/**
 * @param int $rec_count
 * @param database2 $db
 * @return mixed
 */
function get_new_emails2($rec_count, &$db){
    require '../ssl_pages_new/auto_email2/konference.config.i';

    $tmp = $db->Query("
        SELECT
              m_ae_mails.id_mail,
              CONCAT(m_members.prijmeni, ' ', m_members.jmeno) AS jmeno,
              m_members.email,
              m_members.id_m,
              m_members.titul,
              DATE_FORMAT(m_ae_mails.cas, '%e.%c.%Y %k:%i') AS datum,
              m_ae_mails.typ,
              m_ae_mails.kraje,
              m_ae_mails.konference,
              m_ae_types.type_name,
              m_ae_mails.subject,
              m_ae_mails.text_body,
              m_ae_mails.html_body
            FROM
              m_ae_mails
              LEFT JOIN m_members ON m_ae_mails.id_m=m_members.id_m
              LEFT JOIN m_ae_types ON m_ae_mails.typ=m_ae_types.id_type
            ORDER BY cas DESC LIMIT $rec_count
        ");

    $q_kraje = $db->Query("SELECT id_kraj, kraj_abbr FROM m_list_kraj WHERE kraj_name NOT LIKE '---%' ORDER BY id_kraj"); //podminkou "not like" se vyradi kraj "----neuveden----"
    $akraje = array();

    while ($kraj = $db->FetchAssoc($q_kraje)) {
        $akraje[$kraj['id_kraj']] = $kraj['kraj_abbr'];
    }

    $row = $db->getNumRows($tmp);
    $new_emails = array();

    while ($row > 0) {
        $row--;

        $new_emails[$row]["id"] = $db->getResult($tmp, $row, "id_mail");
        $new_emails[$row]["body"] = $db->getResult($tmp, $row, "text_body");
        $new_emails[$row]["id_m"] = $db->getResult($tmp, $row, "id_m");
        $new_emails[$row]["datum"] = $db->getResult($tmp, $row, "datum");
        $new_emails[$row]["input_date2"] = $db->getResult($tmp, $row, "datum");
        $new_emails[$row]["autor"] = trim($db->getResult($tmp, $row, "titul")." ".$db->getResult($tmp, $row, "jmeno"));
        $new_emails[$row]["subject"] = trim($db->getResult($tmp, $row, "subject"));

        $meta = array();

        if ($db->getResult($tmp, $row, "typ") !== null) {
            $mail_kraje = $db->getResult($tmp, $row, "kraje");
            $kraje = array();

            for ($i = 0; $i < 64; $i++) {
                $kraj = pow(2, $i);
                if ($kraj & $mail_kraje) {
                    $kraje[] = $akraje[$i];
                }
            }

            if (count($kraje) < 5) {
                $k_list = implode(', ', $kraje);
            }
            elseif (count($kraje) == count($akraje)) {
                $k_list = 'všechny';
            }
            else {
                $k_list = 'různé';
            }

            $meta[] = htmlspecialchars('Kraje: ' . $k_list);
            $meta[] =  "<b>".htmlspecialchars($db->getResult($tmp, $row, "type_name"))."</b>";
        }
        else {
            $meta[] = $konference[$db->getResult($tmp, $row, "konference")]['name'];
        }

        $new_emails[$row]['meta'] = implode(', ', $meta);
    }

    return $new_emails;
}

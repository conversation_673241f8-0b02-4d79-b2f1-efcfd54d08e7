<?php
DEFINE('PAGE_ID_MARK', '%PGID%');
class pager{
  private $per_page;
  private $current_page;
  private $count;
  public function __construct($current_page, $count, $per_page = 50){
    $this->current_page = max($current_page, 1); //current page retrieved from _GET
    $this->count = $count;  //total count of elements
    $this->per_page = max($per_page, 1); //elements to display on one page
  }
  public function sql_limit(){
    return ($this->current_page-1)*$this->per_page.','.$this->per_page;
  }
  
  public function pager_html($url, $surround=3){
/*
pgurl: url to link with PAGE_ID_MARK replaced by page number
surround: how many numbers to print out around current page
*/
    $page_count = ceil($this->count / $this->per_page);
    if (($this->current_page < 1) || ($this->current_page > $page_count) || ($page_count <= 1)) return ''; /*no pages or bad values*/
    
    $vrat = '<div class="pgr_main">';
    
    //previous and first page link
    $vrat .= ($this->current_page > 1) ? 
      '<a href="'.str_replace(PAGE_ID_MARK, $this->current_page-1, $url).'">&lt;</a>&nbsp;' //previous
      .'<a href="'.str_replace(PAGE_ID_MARK, 1, $url).'">1</a>&nbsp;': //first
      '&lt;&nbsp;<span class="pgr_active">1</span>&nbsp;'; //inactive symbols
    
    
    if ($this->current_page-$surround > 2) $vrat .= "...&nbsp;"; //gap between first page and surrounding current
    
    //group of links surrounding current page (including current)
    for ($x = max($this->current_page-$surround, 2); $x <= min($this->current_page+$surround, $page_count-1); $x++){
      $vrat .= ($x == $this->current_page) ? 
        '<span class="pgr_active">'.$x.'</span>&nbsp;':
        '<a href="'.str_replace(PAGE_ID_MARK, $x, $url).'">'.$x.'</a>&nbsp;';
    }
    if ($this->current_page+$surround < $page_count-1) $vrat .= "...&nbsp;";
    
    //next and last page links
    $vrat .= ($this->current_page < $page_count) ? 
      '<a href="'.str_replace(PAGE_ID_MARK, $page_count, $url).'">'.$page_count.'</a>&nbsp;' //last
      .'<a href="'.str_replace(PAGE_ID_MARK, $this->current_page+1, $url).'">&gt;</a>&nbsp;': //next
      '<span class="pgr_active">'.$page_count.'</span>&nbsp;&gt;&nbsp;'; //inactive symbols
      
    $vrat .= '</div>';
    return $vrat;  
  }
}
?>

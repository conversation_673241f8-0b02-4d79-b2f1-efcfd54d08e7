<?PHP	
/*  funkce provede
utf8	
1 - ulozeni na server
2 - kontrolu pripony dle povolenych vzoru
3 - v pripade nepovolenych vzoru zazipuje soubor

potrebuje funkce
1 - kontrolu existence nazvu (check_content_path)
2 -  kontrolu pripony (suffix)

INPUTS:
1 - vkladane data
2 - j<PERSON><PERSON> (name), popis(descr), cesta (path), sek<PERSON> razeni (sect2),v<PERSON><PERSON><PERSON> (owner), 
	 datum prace (date), datum ke kteremu dokument patri (to_date), datum expirace (to_expir)
3 - pokud $extranet=1 tak dojde k duplikaci zaznamu a souboru i na EXTRANETu
*/

require_once("../ssl_library_new/put_content.l");
require_once("../ssl_library_new/check_content_path.l");
require_once("../ssl_library_new/get_content_file.l");


function upload_file($upload, &$a_part, &$db, $type="insert"){
$uploadDir = '../files/documents_content/'; 

//preg_match("/^(\w+)\..*?$/", StrRev($a_part["upload_name"]), $part);
	//preg_match("/\.(.*)\$/", $a_part["upload_name"], $part);
	$part = explode(".", $a_part["upload_name"]);

	$sufix = $part[count($part)-1];
//	echo $sufix;

            if ($upload != 'none'){ 
			$b_zip = false;
			// Pri aktualizaci nutno provest i v souboru update_upload_file.l
			Switch($sufix){
				case "htm":
					$s_suffix = ".htm";
					$b_zip = true;
				break;
				case "html":
					$s_suffix = ".htm";
					$b_zip = true;
				break;
				case "gif":
					$s_suffix = ".gif";
					$b_zip = true;
				break;
				case "jpg":
					$s_suffix = ".jpg";
					$b_zip = true;
				break;
				case "zip":
					$s_suffix = ".zip";
					$b_zip = true;
				break;
				default:
					//$s_suffix = ".zip";
					$s_suffix = ".".$sufix;
					$b_zip = true;
				break;
			}
				
				$name = UniqID("").$s_suffix;
				While(check_content_path($name, $db)){
					$name = UniqID("").$s_suffix;
				}

		If(!$b_zip){
		// ZIPOVAT....
				$upload_asci = $a_part["upload_name"];//odstrani cestinu
                $dest = $uploadDir . $upload_asci; 
				$upload_name = $a_part["upload_name"];
				$upload_name_asci = $upload_name;//odstrani cestinu
				
                if (@copy($upload["tmp_name"], $dest)){ 
                    echo "Soubor byl úspěšně vložen.<BR>\n"; 

					exec("cd ".$uploadDir."; zip -Dm '$name' '$upload_name_asci'");

					$a_part["path"] = $name;
					if (put_content($a_part, $id_mysql, $db)){
						echo "Zápis do DB proběhl v pořádku...";
					} else {
						echo "Zápis do DB neproběhl v pořádku, informujte administrátora...";
					}

                } else { 
                    echo "<font color=\"FF0000\"><b>File Upload Failed 1</b></font><br>\n"; 
                    $perms = @fileperms($uploadDir); 
                    $owner = @fileowner($uploadDir); 
                    if (!$perms){ 
                        echo "Directory does not exist: $uploadDir<BR>\n"; 
                    } else{ 
                        $myuid = getmyuid(); 
                        if (!($perms & 2) && !(($owner == $myuid) && ($perms & 128))){ 
                            echo  "Script doesn't have permission to write in $uploadDir<BR>\n"; 
                        } //if(!($prems & 2)
                    } //if (!$perms){ 
                } //If (@copy
			
		} else {
			// ZACATEK UPLOADU
                $dest = $uploadDir . $name; 
                if (@copy($upload["tmp_name"], $dest)){ 
                    echo "Soubor byl úspěšně vložen.<br>\n"; 
					$a_part["path"] = $name;
					if ($type=="insert") {
						if (put_content($a_part, $id_mysql, $db)){
							echo "Zápis do DB proběhl v pořádku...";
						} else {
							echo "Zápis do Db neproběhl v pořádku, informujte administrátora...";
						}
					} else {
						
						//smazani souboru
						$a_f = get_content_file($a_part["id_c"], $db);
						$s_file = $uploadDir.$a_f["path"];
		
						//exec("rm $s_file");
						unlink($s_file);
						
						$a_part["path"] = $name;													//novy nazev pro upload souboru
					
						if (update_content($a_part, $db)) {
							echo "Aktualizace proběhla v pořádku.";
						} else {
							echo "Aktualizace proběhla s CHYBOU.";
						}
					}					
                } else { 
                    echo "<font color=\"FF0000\"><b>File Upload Failed 2</b></font><br>\n"; 
                    $perms = @fileperms($uploadDir); 
                    $owner = @fileowner($uploadDir); 
                    if (!$perms){ 
                        echo "Directory does not exist: $uploadDir<BR>\n"; 
                    } else{ 
                        $myuid = getmyuid(); 
                        if (!($perms & 2) && !(($owner == $myuid) && ($perms & 128))){ 
                            echo "Script doesn't have permission to write in $uploadDir<BR>\n"; 
                        } //if(!($prems
                    } //if (!$perms){ 
                } //If (@copy
			}   // if($b_zip
			
		// KONEC UPLOADU
        } else{ 
           echo "<font color=\"FF0000\"><b>File Upload Failed</b></font><br>\n"; 
           echo "Filesize exceeds limit in FORM or php.ini<br>\n"; 
         } // if ($upload != 'none'){ 

}
?>
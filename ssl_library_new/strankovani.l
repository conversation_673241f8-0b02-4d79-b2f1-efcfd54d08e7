<?PHP 
/******************************************/
/* Funkce vypisuje strankovani		  */
/******************************************/
function strankovani($celkem, $page, $step, $url){
	$ret = "";
		if ($celkem > 0){
			$zb = $celkem % $step;
			if ($zb > 0) {
				$celkem_stranek = intval( $celkem / $step ) + 1;
			} else {
				$celkem_stranek = intval( $celkem / $step );
			}
			if ($celkem_stranek > 1){ 
				if ($page == 0){
					$ret .= " předchozí ";
				} else {
					$ret .= " <a href=\"$url&start=".($page - 1)."\">předcho<PERSON><PERSON></a> ";
				}
				if ($celkem_stranek <= 10) {
				 	for ($i = 0; $i < $celkem_stranek; $i ++){  
						if($i == $page ){
							$ret .= "<strong>".($i + 1)."</strong>, ";
						} else {
							$ret .= "<a href=\"$url&start=".$i."\">".($i + 1)."</a>, ";
						}
					}	
				} else {
					for ($i = 0; $i < 4; $i ++){  
						if($i == $page ){
							$ret .= "<strong>".($i + 1)."</strong>, ";
						} else {
							$ret .= "<a href=\"$url&start=".$i."\">".($i + 1)."</a>, ";
						}
					}	
					$vypsane_konec = $celkem_stranek - 4;
					$stred = "";
					$start_tecky = "... ";
					$konec_tecky = "... ";
					for ($i = ($page - 3); $i < ($page + 4); $i ++){  
						if ($i > 3 AND $i < $vypsane_konec){
							if($i == $page ){
								$stred .= "<strong>".($i + 1)."</strong>, ";
							} else {
								$stred .= "<a href=\"$url&start=".$i."\">".($i + 1)."</a>, ";
							}
						}
						if( $i >= ($vypsane_konec - 1)) {
							$konec_tecky = "";
						}
						if( $i <= 4) {
							$start_tecky = "";
						}
					}	
					$ret = $ret.$start_tecky . $stred . $konec_tecky;
					for ($i = $vypsane_konec ; $i < $celkem_stranek; $i ++){  
						if($i == $page ){
							$ret .= "<strong>".($i + 1)."</strong>, ";
						} else {
							$ret .= "<a href=\"$url&start=".$i."\">".($i + 1)."</a>, ";
						}
					}	
				}
				$ret = substr($ret, 0, strlen($ret ) - 2);
				if ($page == ($celkem_stranek -1)){
					$ret .= " následující ";
				} else {
					$ret .= " <a href=\"$url&start=".($page + 1)."\">následující</a> ";
				}
			} elseif ($celkem_stranek == 1 ) {
				$ret = " stránka 1/1 ";
			}
		}	
	return $ret;
}

?>
<?PHP
/*********************************************************************
Simulation REGISTER GLOBALS ON
*********************************************************************/

// 2017: nastav locale, nutne kvuli casove zone a prechodu na PHP 5.6
setlocale(LC_TIME, 'cs_CZ.utf-8');


// V podstate dela register_globals = 1, nahraje vse do hlavni symbol table
// 2019-05-28, TK: nekdy se stane, ze zminene promenne nejsou nasavene.
// Zatim neni jasne proc, to je bud nejaka chyba PHP nebo nejaky problem, kdy se toto vola opakovane nebo duplicitne.
@extract($_REQUEST);
@extract($_FILES);
@extract($_SERVER);

// toto nechapu ...
// $img_go_next="<img src=\"images/template_go_next.gif\" width=\"56\" height=\"14\" border=\"0\" alt=\"Další\">";
// $img_go_back="<img src=\"images/template_go_front.gif\" width=\"56\" height=\"14\" border=\"0\" alt=\"Předchozí\">";


<?PHP
// 2013-08-16, TK: pro pouziti ve vyvojarskem koutku
if (!isset($DATABASE_NAME)) $DATABASE_NAME = "mensaweb";




function show_user_zajmy($db, $id_m, $switch=0){
    global $DATABASE_NAME;

	$SEL="select * from {$DATABASE_NAME}.m_zajmy z, m_list_zajmy l where z.id_m='{$id_m}' AND z.zajem=l.id_l_z";
	$tmp=$db->Query($SEL);
	$a_zajmy = array();

switch ($switch){
	case 1:
		$i=$db->getNumRows($tmp);
		while($i--){
			$a_zajmy[$i]["pozn"]=$db->getResult($tmp, $i, "pozn");
			$a_zajmy[$i]["zajem"]=$db->getResult($tmp, $i, "name");
		}
		return $a_zajmy;
	break;
	default:
		$i=$db->getNumRows($tmp);
		while($i--){
			$j=$db->getResult($tmp, $i, "zajem");
			$a_zajmy[$j]["pozn"]=$db->getResult($tmp, $i, "pozn");
			$a_zajmy[$j]["zajem"]=$db->getResult($tmp, $i, "name");
		}
		return $a_zajmy;
}
}
?>
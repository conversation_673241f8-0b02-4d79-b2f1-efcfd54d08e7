<?PHP
function sum_vote($db, $id_ank, $akce=0){
    $a_sum = [];

	switch ($akce) {
	case 0:			//vrati dvojice jmeno polozky - pocet bodu
		$SEL="SELECT v.id_volby AS id_volby, v.value AS nazev, SUM(h.vaha) AS body 
			  FROM m_ank_volby AS v 
			    LEFT JOIN m_ank_hlas_volba AS hv USING (id_volby)
			    LEFT JOIN m_ank_hlasujici AS h USING (id_hlas)
			  WHERE v.id_ank=$id_ank
			  GROUP BY v.id_volby
			  ORDER BY body DESC
		";

		$tmp = $db->Query($SEL);

		$i=$db->getNumRows($tmp);
		while ($i--){
//			$a_sum[$i]["id_volby"]=$db->getResult($tmp, $i, "id_volby");
			$a_sum[$i]["nazev"]=$db->getResult($tmp, $i, "nazev");
			$a_sum[$i]["body"]=$db->getResult($tmp, $i, "body");
		}
	break;
	case 2:					// Kolik lidi hlasovalo
		$hl='Y';
	case 3:					// Kolik lidi nehlasovalo
		if ($akce==3) $hl='N';
		$SEL="	SELECT COUNT(id_hlas) AS hlasovalo 
				FROM m_ank_hlasujici
				WHERE id_ank=$id_ank AND hlas='$hl'
				GROUP BY id_ank
			";
		$tmp = $db->Query($SEL);

		$i=$db->getNumRows($tmp);
		if ($i==0) $a_sum=0; else $a_sum=$db->getResult($tmp, 0, "hlasovalo");
	break;
	}
	return $a_sum;
}
?>

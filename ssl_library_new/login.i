<?PHP
/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (a evidence statistik)
 *
 * Změnovník
 *  2013-Mar-10, TK: Pretty-print, oprava identifikace clenstvi pro cisla bez cz
 *
 * @param string $login
 * @param string $heslo
 * @param string $err_string  VÝSTUPNÍ parametr - chybová hl<PERSON>
 * @param object $db          připojení k db.
 * @param bool $brute_force_prevention provede overeni proti brute force utoku, default true
 * @return boolean  true, pokud se uživatel smí přihlásit
 */
function login($login, $heslo, &$err_string, $db, $brute_force_prevention = true)
{
    $login = $db->getEscapedString(trim($login));
    $heslo = $db->getEscapedString(trim($heslo));
    $clen = false;


    // nastav identifikátor člena
    if (substr($login, 0, 2) == "cz")
    {
        $login = substr($login, 2);
        $clen = true;
    }
    // pokud je to jen ciste cislo, je to take clen
    elseif (preg_match ("/^\d{1,5}$/", $login) === 1)
    {
        $clen = true;
    }
    elseif (substr($login, 0, 2) == "dm")
    {
        $clen = true;
    }
    // jeste existuji jine loginy jako deXXXX a podobne
    // identifikujici cleny jinych Mens
    // ty pak maji jine pravidlo na vypocet data pro prihlaseni



    // skonči, pokud jsou údaje nesmyslné
    if (strlen($login) == 0 or strlen($heslo) == 0)
        return false;



    // vypocti rok vuci, kteremu se porovnava zaplaceni
    date_default_timezone_set('Europe/Prague');
    if ($clen)
    {
        // clenove, staci, aby zaplatili na minuly rok
        $end_date = (date("Y") - 1);
    }
    else
    {
        $end_date = date("Y", mktime(0, 0, 0, date("m") - 2, 10, date("Y")));
    }



    $tmp = $db->Query("SELECT COUNT(id) AS pocet FROM m_bad_login
        WHERE identifikace='" . $_SERVER['REMOTE_ADDR'] . "' AND cas > ADDTIME(NOW(), '-0:15:0') ");
    $x = $db->getResult($tmp, 0, "pocet");

    // nedovol dalsi pokus o test na prhilaseni z dane IP pri chybnem prihlaseni
    // vyjimaka je, pokud je to volano skrze API, pak je limit vyssi
	if(($brute_force_prevention && $x>=3) || ($x>=100)){
		$err_string = "Opakovaně zadáno chybné heslo. Přístup byl na 15 minut zablokován.";
		return false;
	}



    $SEL = "SELECT id_m, clen_cislo
        FROM m_members
        WHERE
            disable='N'
            AND clen_cislo='{$login}'
            AND heslo=old_password('{$heslo}')
            AND prizpevky>={$end_date}
            AND (ukonceni_clenstvi_poplatky=0 OR ukonceni_clenstvi_poplatky>=year(now())) ";


    $tmp = $db->Query($SEL);
    $i = @$db->getNumRows($tmp);
    if ($i == 1)
    {
        $id_m = $db->getResult($tmp, 0, "id_m");

        // úspěšné přihlášení, zapiš statistiku o prihlaseni
        $SEL = "DELETE from m_members_login WHERE id_m=$id_m";
        @$db->Query($SEL);
        $SEL = "INSERT INTO m_members_login (id_m, cas) VALUES($id_m, now())";
        @$db->Query($SEL);

        return true;
    }




    // pokud jsme se dostali sem, vime, ze prihlaseni se nezdarilo
    // diagnostikuj chybu
    $tmp = $db->Query("SELECT clen_cislo, disable FROM m_members WHERE clen_cislo='$login'");
    if (intval($db->getNumRows($tmp)) === 0)
    {
        $err_string = "Toto členské číslo v intranetu neexistuje!";
    }
    else
    {

        if ($db->getResult($tmp, 0, "disable") == "Y")
        {
            $err_string = "Účet {$login} byl zablokován, prosím, kontaktujte adminstrátora.";
        }
        else
        {
            $tmp = $db->Query("SELECT clen_cislo FROM m_members WHERE clen_cislo='$login' AND heslo=old_password('$heslo')");
            if (intval($db->getNumRows($tmp)) === 0)
            {
                $err_string = "Zadali jste špatné heslo pro účet {$login}!";
            }
            else
            {
                $err_string = "Nemáte zaplaceny členské příspěvky, k účtu se nelze přihlásit!";
            }
        }
    }

    // test unit does not provide the user agent data and shown warning breaks the response code.
    $obsah  = "HTTP_USER_AGENT: " . @$_SERVER['HTTP_USER_AGENT'] . "<br>";
    $obsah .= "REMOTE_ADDR: " . $_SERVER['REMOTE_ADDR'] . "<br>";
    $obsah .= "REQUEST_METHOD: " . $_SERVER['REQUEST_METHOD'] . "<br>";
    $obsah .= "REQUEST_URI: " . $_SERVER['REQUEST_URI'];


    // zaloguj špatný pokus
    $SQL = "INSERT INTO m_bad_login (cas, email, identifikace, result, obsah)
        VALUES ( now(), '$login', '" . $_SERVER['REMOTE_ADDR'] . "', '$err_string', '" . addslashes(str_replace(';', '|', $obsah)) . "'    )";
    $db->Query($SQL);

    return false;
}

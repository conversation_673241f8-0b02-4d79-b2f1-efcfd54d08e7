<?PHP
require_once("../ssl_library_new/inquiry_sel.l");    //ziska informace z tabulky hlasovani dle uid
require_once("../ssl_library_new/inquiry.l");        //ziska informace z tabulky ankety dle id_inq
?>



<table width="230" border="0" cellspacing="2" cellpadding="7">
    <?PHP

    // **********************************************************
    //                VYPIS NEJNOVEJSICH 5ti AKCI Z KALENDARE
    // **********************************************************

    require_once("../ssl_library_new/calendar.class.l");
    $calendar = new calendar($db);
    if (!isset($mesic)) {
        $mesic = date("m");
    }
    if (!isset($rok)) {
        $rok = date("Y");
    }
    ?>


    <tr>
        <td><p style="font-size: 10px;">
                <?PHP
                //echo $radek["d"]." <b><a href=\"index.php?men=men15.0.0.0&id_a=".$radek["id_a"]."\" style=\"color: Black;\">".$radek["nazev"]."</a></b>";
                $calendar->get_calendar_table($rok, $mesic, "czech", true, @$s_day, @$s_week, @$s_month, "?men=men15.0.0.0&", "blue_new", "230");
                ?>
            </p></td>
    </tr>





    <?PHP
    // **********************************************************
    //                TABULKA S PLATNYMI ANKETAMI
    // **********************************************************
    $tmp = inquiry_sel($db, $a_user["id_m"]);
    $poc = count($tmp);
    if ($poc > 0) {
        ?>
        <tr>
            <td bgcolor="#3e7db0">
                <b><a href="index.php?men=men2.0.0.0" style="color:white;">Ankety</a></b>
            </td>
        </tr>
        <?PHP
        for ($i = 0; $i < $poc; $i++) {
            $tmp2 = inquiry($db, $tmp[$i]["id_ank"]);
            ?>
            <tr>
                <td><p style="font-size: 10px;">
                        <b><a href="index.php?men=men2.2.0.0" style="color: Black;" title="<?PHP echo $tmp2["popis"] ?>"><?PHP echo $tmp2["name"] ?></a></b>
                    </p>
                </td>
            </tr>
            <?PHP
        }
    }






    // **********************************************************
    //                Poslední dokumenty mensy
    // **********************************************************
    if (access("read", "men14.0.0.0", $a_user["id_m"], $db)) {
        ?>
        <tr>
            <td bgcolor="#3e7db0">
                <b><a href="index.php?men=men14.0.0.0" style="color:white;">Nové dokumenty</a></b>
            </td>
        </tr>
        <tr>
            <td>
                <table border="0">
                    <tr>
                        <td>
                            <ul>
                                <?PHP
                                $d_porovnani = mktime(0, 0, 0, date("m"), date("d") - 7, date("Y"));
                                $d_sql = date("Y/m/d", $d_porovnani);
                                $start = 0;
                                $pocet = 0;

                                while ($pocet < 5) {
                                    $tmp = $db->Query("SELECT id, DATE_FORMAT(date_edit, '%e.%c.%Y %k:%i') as date_edit2, id, sect2, name FROM m_content ORDER BY date_edit DESC LIMIT $start, 5");
                                    while ($row = $db->FetchArray($tmp) AND $pocet < 5) {
                                        if (access("read", "men" . $row['sect2'], $a_user["id_m"], $db)) {
                                            echo "<li><a style=\"color:#000;padding-top:5px;\" href=\"document.php?men=men" . $row['sect2'] . "&id_c=" . $row["id"] . "\">" . $row["name"] . "</a></li>";
                                            $pocet++;
                                        }
                                    }
                                    $start = $start + 5;
                                }
                                ?>
                            </ul>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <?PHP

    }





    // **********************************************************
    //                TABULKA VYSLEDKY POSLEDNI ANKETY
    // **********************************************************
    $date = Date("Y-m-d", MKTIME(0, 0, 0, Date("m"), (Date("d") - 3), Date("Y")));
    $tmp = $db->Query("SELECT * FROM m_ank_ankety WHERE end<='$date' order by end desc LIMIT 0,3");
    $radek = $db->FetchArray($tmp);
    $i = intval(rand(0, 2));
    $id = $db->getResult($tmp, $i, "id_ank");
    ?>
    <tr>
        <td bgcolor="#3e7db0">
            <b><a href="index.php?men=men2.3.0.0" style="color:white;">Anketa: <?PHP echo $db->getResult($tmp, $i, "name"); ?></a></b>
        </td>
    </tr>
    <tr>
        <td>
            <table border="0">
                <?PHP
                $tmp = $db->Query("SELECT * FROM m_ank_volby WHERE id_ank=$id");
                while ($radek = $db->FetchArray($tmp)) {
                    $vol[$radek["id_volby"]] = $radek["value"];
                }
                $tmp = $db->Query("SELECT COUNT(id_volby)as hodnota, id_volby FROM m_ank_hlas_volba WHERE id_ank=$id GROUP BY id_volby ORDER BY hodnota desc");
                $rate = 0;
                while ($radek = $db->FetchArray($tmp)) {
                    if ($rate == 0) {
                        $rate = 220 / $radek["hodnota"];
                    }
                    $delka = intval($rate * $radek["hodnota"]);
                    echo "<tr><td><p style=\"font-size: 10px; margin-top: 0px; margin-bottom: 0px\">" . $vol[$radek["id_volby"]] . "<br><img src=\"images/okr.gif\" width=\"$delka\" height=\"5\"></p></td></tr>";
                }
                ?>
            </table>
        </td>
    </tr>
    <?PHP




    // **********************************************************
    //                VYPIS AKTUALNE PRIHLASENYCH MENSANU
    // **********************************************************
    ?>
    <tr>
        <td bgcolor="#3e7db0">
            <p><b>
                    <font color="#FFFFFF">Aktu&aacute;ln&#283; p&#345;ihl&aacute;&scaron;eni</font>
                </b></p>
        </td>
    </tr>
    <tr>
        <td><p style="font-size: 11px;">
                <?PHP

                $SQL = "select log.id_m, m.jmeno, m.prijmeni, ADDTIME(log.cas, '00:10:00.000000') as x, IF(ADDTIME(log.cas, '00:3:10.000000')>now(),'yes','no') as xx FROM m_members_login AS log INNER JOIN m_members m ON log.id_m=m.id_m WHERE ADDTIME(log.cas, '00:10:00.000000')>now() ORDER BY log.cas desc";
                $tmp = $db->Query($SQL);
                $poc = $db->getNumRows($tmp);
                $poprve = true;
                while ($radek = $db->FetchArray($tmp)) {
                    if ($poprve AND $radek["xx"] == 'no') {
                        echo "<br>židle je teplá, ale už nikdo na ní ...<br>";
                        $poprve = false;
                    }
                    echo "- <a style=\"color:#000;padding-top:5px;padding-bottom:5px;\" href=\"index.php?men=men4.1.0.0&s_content=view.i&id_m=" . $radek["id_m"] . "\">" . $radek["jmeno"] . " " . $radek["prijmeni"] . "</a></br>";
                }
                ?>
            </p>
        </td>
    </tr>
</table>

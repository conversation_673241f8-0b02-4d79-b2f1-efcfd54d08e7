<?PHP
function actualize_prispevky(&$db, $body_mail, $err=""){

	$a_users = explode(",",$body_mail);
	$i = count($a_users);
	$STR = " clen_cislo = '". trim($a_users[$i-1])."'";
//	$i--; // ne, aby tam byla odstranena i uvodni nula
	
	while ($i--) {
		$cl = trim($a_users[$i]);
		$STR .= " OR clen_cislo = '".$cl."'";
		if (substr($cl, 0,1)=="0"){
			// odstranění uvodních 0
			$cl = substr($cl, 1);
			$STR .= " OR clen_cislo = '".$cl."'";
			if (substr($cl, 0,1)=="0"){
				// odstranění druhe 0
				$cl = substr($cl, 1);
				$STR .= " OR clen_cislo = '".$cl."'";
			}
		}
	}

	$datum = date("Y");
	$SEL = "UPDATE m_members SET prizpevky = $datum, disable='N' WHERE $STR";
	$ret =  $db->Query($SEL);
    
	return $db->error;
}

function actualize_prispevky_deti(&$db, $body_mail, $err=""){

	$a_users = explode(",",$body_mail);
	$i = count($a_users);
	$STR = " clen_cislo = 'dm". trim($a_users[$i-1])."'";
//	$i--;
	
	while ($i--) {
		$cl = trim($a_users[$i]);
		$STR .= " OR clen_cislo = 'dm".$cl."'";
		if (substr($cl, 0,1)=="0"){
			// odstranění uvodních 0
			$cl = substr($cl, 1);
			$STR .= " OR clen_cislo = 'dm".$cl."'";
			if (substr($cl, 0,1)=="0"){
				// odstranění druhe 0
				$cl = substr($cl, 1);
				$STR .= " OR clen_cislo = 'dm".$cl."'";
			}
		}
	}

	$datum = date("Y");
	$SEL = "UPDATE m_members SET prizpevky = $datum, disable='N' WHERE $STR";
	$ret =  $db->Query($SEL);

	return $db->error;
}
?>
<?PHP
function get_pwd($cnt = 8){
	$a_pwd = array("2", "3", "4", "5", "6", "7", "8", "9", "q", "Q", "w", "W", "e", "E", "r", "R", "t", "T", "u", "U", "i", "p", "P", "a", "A", "s", "S", "d", "D", "f", "F", "g", "G", "h", "H", "j", "J", "k", "K", "x", "X", "c", "C", "v", "V", "b", "B", "n", "N", "m", "M");
	
	$heslo="";
	$pocet = count($a_pwd)-1;
	for ($i=1; $i<=$cnt; $i++){
		$j = rand(0,$pocet);
		$heslo = $heslo . $a_pwd[$j];
	}
	
	return $heslo;
}
?>
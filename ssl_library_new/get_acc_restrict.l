<?PHP
/*
utf8
funkce vrati hodnotu pristupu na danou sekci, pro daneho uzivatele.
Inputs: l - u<PERSON><PERSON>l, jinak prava skupiny
		sect - císlo skce 
Ouputs:	true/false
*/
function get_acc_restrict($l, $men, $role=0, &$db){

	preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts );
 	$s2= $parts[1];
	$s3= $parts[2];
	$s4= $parts[3];
	$s5= $parts[4];

If($role==1){
//echo "Prava role $l na $s2.$s3.$s4.$s5";
	$SEL = "SELECT
				 BIT_OR(p.value) as ROLE_PERMISSION
			FROM 
				m_acc_permission p,
				m_acc_class c
			WHERE
				p.role_id=$l AND
				c.class_id=p.class_id AND
			(
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=$s5 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=0 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=0 and c.s3=0 and c.s4=0 ) OR
				(c.s1=0 and c.s2=0 and c.s3=0 and c.s4=0 )
			)
		GROUP BY p.role_id
		ORDER BY
			c.s1 desc, c.s2 desc, c.s3 desc, c.s4 desc, 
			p.value asc";
//	echo $SEL;
		$tmp = @$db->Query($SEL);
		$i =  @$db->getNumRows($tmp);
		
} else {
//echo "Prava uzivatele $l na $s2.$s3.$s4.$s5";
	
	$SEL = "select DISTINCT l.id_m, 
			CONCAT(c.s1, '.', c.s2, '.', c.s3, '.', c.s4) as level,
			p.value, 
			BIT_OR(p.value) as  ROLE_PERMISSION,
			p.role_id
		from m_members l, m_acc_permission p,
			m_acc_uid2role u2r, m_acc_class c
		where
			p.role_id=-1 AND
			l.id_m = $l AND 
			(
				p.id_m=l.id_m
				OR
				(
					u2r.id_m=l.id_m AND
					p.role_id=u2r.role_id
				)
			) AND
			c.class_id=p.class_id AND
			(
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=$s5 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=0 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=0 and c.s3=0 and c.s4=0 ) OR
				(c.s1=0 and c.s2=0 and c.s3=0 and c.s4=0 )
			)
		group by  p.id_m, p.class_id
		order by
			c.s4 desc, c.s3 desc, c.s2 desc, c.s1 desc,
			p.id_m desc, p.role_id asc";

			
		$tmp = @$db->Query($SEL);
		
		$i =  @$db->getNumRows($tmp);
		if($i==0){
		//echo "Prava uzivatele $l ve skupine na $s2.$s3.$s4.$s5";
		$SEL = "select DISTINCT l.id_m, 
			CONCAT(c.s1, '.', c.s2, '.', c.s3, '.', c.s4) as level,
			p.value, 
			BIT_OR(p.value) as  ROLE_PERMISSION
		from m_members l, m_acc_role ro, m_acc_permission p,
			m_acc_uid2role u2r, m_acc_class c
		where
			p.role_id<>-1 AND
			l.id_m = $l AND 
			(
					p.id_m=l.id_m
				 OR
				(
					u2r.id_m=l.id_m AND
					p.role_id=u2r.role_id
				)
			) AND
			c.class_id=p.class_id AND
			ro.role_id=p.role_id AND
			(
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=$s5 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=0 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=0 and c.s3=0 and c.s4=0 ) OR
				(c.s1=0 and c.s2=0 and c.s3=0 and c.s4=0 )
			)
		group by p.id_m, p.class_id
		order by
			c.s4 desc, c.s3 desc, c.s2 desc, c.s1 desc,
			p.id_m desc, p.role_id desc"; 
		$tmp = $db->Query($SEL);
		
		$i =  @$db->getNumRows($tmp);
		}
}

		if($i==0){
			return 0;
		} else {
			//echo @$db->getResult($tmp, 0,"ROLE_PERMISSION");
			return @$db->getResult($tmp, 0,"ROLE_PERMISSION");
		}
}
?>
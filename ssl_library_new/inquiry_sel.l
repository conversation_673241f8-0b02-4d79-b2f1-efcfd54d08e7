<?PHP
function inquiry_sel(&$db, $id_m, $akce=0){

	$a_inqsel = array();
	
	switch($akce){
	case 0:  //aktivni pro hlasovani
		$date=Date("Y-m-d");
		$where="h.id_m=$id_m AND h.hlas='N' AND a.start<='$date' AND a.end>='$date' AND h.id_ank=a.id_ank";
		$SEL="SELECT a.id_ank FROM m_ank_ankety AS a, m_ank_hlasujici AS h WHERE $where GROUP BY a.id_ank" ;
	break;
	case 1:  //statistiky pro normalni uzivatele
		$date=Date("Y-m-d", MKTIME(0,0,0,Date("m"),(Date("d")-3), Date("Y")));
		$where="(h.id_m=$id_m AND a.end<='$date')";
		$SEL="SELECT a.id_ank FROM m_ank_ankety AS a LEFT JOIN m_ank_hlasujici AS h USING (id_ank) WHERE $where GROUP BY a.id_ank ORDER BY a.end desc LIMIT 0,30" ;
	break;
	case 2:  //statistiky pro majitele anket
		$where="WHERE owner=$id_m";
	case 3:  //statistiky pro admina
		$SEL="SELECT * FROM m_ank_ankety ".@$where;
	break;
	}

	$tmp = $db->Query($SEL);
	$i=$db->getNumRows($tmp);
	while ($i--){
		$a_inqsel[$i]=$db->FetchAssoc($tmp);
	}
	return $a_inqsel;
}
?>
<?PHP

// 2013-08-16, TK: pro pouziti ve vyvojarskem koutku
// rozdeleno pro tabulky ktere jsou i v testu
if (!isset($DATABASE_NAME)) $DATABASE_NAME = "mensaweb";
// a tabulky ktere jsou jen na mensa web
$DATABASE_LIB = "mensaweb";



function find_user(&$db, $switch, $l_start, $search=0){

    global $DATABASE_NAME, $DATABASE_LIB;



    // Rok vuci kteremu se kontroluje zaplaceni prispevku (lide mohou byt cleny i pokud zpalatili predchozi rok).
    // Spravne by bylo predelat dotaz, aby pouzil pohled paltnych clenu ...
    $rok = ((int) date("Y")) -1;


	switch ($switch){
	case 0:
		$SEL="select *
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			m_members.public='Y' AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani
													 AND m_list_kraj.id_kraj=m_members.kraj
													 AND prizpevky>='".$rok."'
		order by m_members.prijmeni asc
		limit $l_start,20";



		$SEL_COUNT="select m_members.id_m AS soucet
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			m_members.public='Y' AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani
													 AND m_list_kraj.id_kraj=m_members.kraj
													 AND prizpevky>='".$rok."'
		order by m_members.prijmeni asc";
	break;


	case 1:
		$SEL="select *
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			$search AND m_members.public='Y' AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani
																			 AND m_list_kraj.id_kraj=m_members.kraj
																			 AND prizpevky>='".$rok."'
		group by
			m_members.id_m
		order by m_members.prijmeni asc
		limit $l_start, 20";




		$SEL_COUNT="select m_members.id_m as soucet
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			$search AND m_members.public='Y' AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani
																			 AND m_list_kraj.id_kraj=m_members.kraj
																			 AND prizpevky>='".$rok."'
		group by
			m_members.id_m
		order by m_members.prijmeni asc";
	break;




	case 11:
		$SEL="select *
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			$search AND m_list_zamestnani.id_l_w=m_members.zamestnani
  						AND m_members.disable='N' AND m_list_kraj.id_kraj=m_members.kraj
		group by
			m_members.id_m
		order by m_members.prijmeni asc
		limit $l_start, 20";



		$SEL_COUNT="select m_members.id_m as soucet
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			$search AND m_list_zamestnani.id_l_w=m_members.zamestnani
  						AND m_members.disable='N' AND m_list_kraj.id_kraj=m_members.kraj
		group by
			m_members.id_m
		order by m_members.prijmeni asc";
	break;





	case 2:
		$SEL="select *
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			m_members.id_m=$search AND m_members.public='Y'
														 AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani
														 AND m_list_kraj.id_kraj=m_members.kraj
								 						 AND prizpevky>='".$rok."'";

		$SEL_COUNT="select m_members.id_m AS soucet
		from
			{$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		where
			m_members.id_m=$search AND m_members.public='Y'
														 AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani
														 AND m_list_kraj.id_kraj=m_members.kraj
								 						 AND prizpevky>='".$rok."'";
	break;



	case 3://upraveno
		$SEL="select
			clen, jmeno, prijmeni, disable, sigh, m_members.zamestnani, m_members.id_m as id_m,
			prezdivka, titul, titul_za_jmenem, tel_d, tel_p, fax_d, fax_p, email, mobil, www, adresa,
			ulice, psc, mesto, clen_cislo, m_list_zamestnani.name as name, zamestnani_pozn,
			public, prizpevky, m_list_kraj.kraj_name as kraj_name, m_members.kraj,
			date, rok_narozeni
		FROM
			{$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_kraj LEFT OUTER JOIN m_zajmy ON m_zajmy.id_m=id_m
		WHERE
			$search AND m_members.public='Y' AND  m_list_zamestnani.id_l_w=m_members.zamestnani
															AND m_members.disable='N' AND  m_list_kraj.id_kraj=m_members.kraj
															 AND prizpevky>='".$rok."'
		group by m_members.id_m
		order by m_members.prijmeni asc
		limit $l_start, 20";



		$SEL_COUNT="select
			m_members.id_m AS soucet
		FROM
			{$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_kraj LEFT OUTER JOIN m_zajmy ON m_zajmy.id_m=m_members.id_m
		WHERE
			$search AND m_members.public='Y' AND  m_list_zamestnani.id_l_w=m_members.zamestnani
															AND m_members.disable='N' AND  m_list_kraj.id_kraj=m_members.kraj
															AND prizpevky>='".$rok."'
		group by m_members.id_m";
	break;



	case 33: // pro administraci - vsichni uzivatele
		$SEL="select
			 	clen, sigh, disable, jmeno, prijmeni, m_members.zamestnani, m_members.id_m as id_m, prezdivka, titul, titul_za_jmenem, tel_d, tel_p, fax_d, fax_p, email, mobil, www, adresa, ulice, psc, mesto, clen_cislo, m_list_zamestnani.name as name, zamestnani_pozn, public, prizpevky, kraj_name, kraj
			FROM
			 	{$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_kraj LEFT OUTER JOIN {$DATABASE_NAME}.m_zajmy ON m_zajmy.id_m=id_m
			WHERE
				$search AND  m_list_zamestnani.id_l_w=m_members.zamestnani
								AND m_members.disable='N' AND  m_list_kraj.id_kraj=m_members.kraj
			group by m_members.id_m
			order by m_members.prijmeni asc
			limit $l_start, 20";


		$SEL_COUNT="select
			 	m_members.id_m AS soucet
			FROM
			 	{$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_kraj LEFT OUTER JOIN {$DATABASE_NAME}.m_zajmy ON m_zajmy.id_m=m_members.id_m
			WHERE
				$search AND  m_list_zamestnani.id_l_w=m_members.zamestnani
								AND m_members.disable='N' AND  m_list_kraj.id_kraj=m_members.kraj
			group by m_members.id_m
			order by m_members.prijmeni asc";
	break;


	case 4:
		$SEL="select * from {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_zajmy, {$DATABASE_LIB}.m_list_kraj WHERE $search AND m_members.disable='N' AND m_members.public='Y' AND m_zajmy.id_m=m_members.id_m AND m_list_zamestnani.id_l_w=m_members.zamestnani AND m_list_kraj.id_kraj=m_members.kraj group by m_members.id_m order by m_members.prijmeni asc limit $l_start, 20";



		$SEL_COUNT="select m_members.id_m AS soucet from {$DATABASE_NAME}.m_members, m_list_zamestnani, {$DATABASE_NAME}.m_zajmy, m_list_kraj WHERE $search AND m_members.disable='N' AND m_members.public='Y' AND m_zajmy.id_m=m_members.id_m AND m_list_zamestnani.id_l_w=m_members.zamestnani AND m_list_kraj.id_kraj=m_members.kraj group by m_members.id_m order by m_members.prijmeni asc";
	break;


	case 44:
		$SEL="select
				clen, sigh, jmeno, prijmeni, m_members.zamestnani, m_members.id_m as id_m, prezdivka, titul, titul_za_jmenem, tel_d, tel_p, fax_d, fax_p, email, mobil, www, adresa, ulice, psc, mesto, clen_cislo, m_list_zamestnani.name as name, zamestnani_pozn, public, prizpevky, date, rok_narozeni, kraj, kraj_name
			FROM
			 	{$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_kraj LEFT OUTER JOIN {$DATABASE_NAME}.m_zajmy ON m_zajmy.id_m=id_m
			WHERE
				$search AND m_list_zamestnani.id_l_w=m_members.zamestnani
								AND m_members.disable='N' AND  m_list_kraj.id_kraj=m_members.kraj
			group by m_members.id_m
			order by m_members.prijmeni asc
			limit $l_start, 20";

		$SEL_COUNT="SELECT
				m_members.id_m AS soucet
			FROM
			 	{$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_kraj LEFT OUTER JOIN {$DATABASE_NAME}.m_zajmy ON m_zajmy.id_m=id_m
			WHERE
				$search AND m_list_zamestnani.id_l_w=m_members.zamestnani
								AND m_members.disable='N' AND  m_list_kraj.id_kraj=m_members.kraj
			group by m_members.id_m";

	break;



	case 5: //pouze pro daneho clena, kde public muze byt i N
		$SEL="SELECT *, DATE_FORMAT(date, '%e.%c.%Y') AS date2

		FROM 
		    {$DATABASE_NAME}.m_members, 
		    {$DATABASE_LIB}.m_list_zamestnani,
		    {$DATABASE_LIB}.m_list_kraj

		WHERE $search
		    AND m_members.disable='N'
		    AND m_list_zamestnani.id_l_w=m_members.zamestnani
		    AND m_list_kraj.id_kraj=m_members.kraj

		ORDER BY m_members.prijmeni ASC LIMIT $l_start, 20";


		$SEL_COUNT="SELECT m_members.id_m AS soucet FROM {$DATABASE_NAME}.m_members, {$DATABASE_LIB}.m_list_zamestnani, {$DATABASE_LIB}.m_list_kraj
		WHERE $search AND m_members.disable='N' AND m_list_zamestnani.id_l_w=m_members.zamestnani AND m_list_kraj.id_kraj=m_members.kraj ORDER BY m_members.prijmeni ASC";

	break;
	}



	$tmp = $db->Query($SEL);
	$i = $db->getNumRows($tmp);
	while ($i--){
		$a_user[$i]["jmeno"]=$db->getResult($tmp, $i, "jmeno");
		$a_user[$i]["prijmeni"]=$db->getResult($tmp, $i, "prijmeni");
		$a_user[$i]["id_m"]=$db->getResult($tmp, $i, "id_m");
		$a_user[$i]["prezdivka"]=$db->getResult($tmp, $i, "prezdivka");
		$a_user[$i]["titul"]=$db->getResult($tmp, $i, "titul");
        $a_user[$i]["titul_za_jmenem"]=$db->getResult($tmp,$i, "titul_za_jmenem");
		$a_user[$i]["tel_d"]=$db->getResult($tmp, $i, "tel_d");
		$a_user[$i]["tel_p"]=$db->getResult($tmp, $i, "tel_p");
		$a_user[$i]["fax_d"]=$db->getResult($tmp, $i, "fax_d");
		$a_user[$i]["fax_p"]=$db->getResult($tmp, $i, "fax_p");
		$a_user[$i]["email"]=$db->getResult($tmp, $i, "email");
		$a_user[$i]["mobil"]=$db->getResult($tmp, $i, "mobil");
		$a_user[$i]["www"]=$db->getResult($tmp, $i, "www");
		$a_user[$i]["adresa"]=$db->getResult($tmp, $i, "adresa");
		$a_user[$i]["ulice"]=$db->getResult($tmp, $i, "ulice");
		$a_user[$i]["psc"]=$db->getResult($tmp, $i, "psc");
		$a_user[$i]["mesto"]=$db->getResult($tmp, $i, "mesto");
		$a_user[$i]["zamestnani_cislo"]=$db->getResult($tmp, $i, "zamestnani");
		$a_user[$i]["clen_cislo"]=$db->getResult($tmp, $i, "clen_cislo");
		$a_user[$i]["zamestnani"]=$db->getResult($tmp, $i, "name");
		$a_user[$i]["zamestnani_pozn"]=$db->getResult($tmp, $i, "zamestnani_pozn");
		$a_user[$i]["public"]=$db->getResult($tmp, $i, "public");
		$a_user[$i]["prizpevky"]=$db->getResult($tmp, $i, "prizpevky");
		$a_user[$i]["sigh"]=$db->getResult($tmp, $i, "sigh");
		$a_user[$i]["clen"]=$db->getResult($tmp, $i, "clen");
		$a_user[$i]["date"]=$db->getResult($tmp, $i, "date");
		$a_user[$i]["date2"]=@$db->getResult($tmp, $i, "date2");
		$a_user[$i]["rok_narozeni"]=$db->getResult($tmp, $i, "rok_narozeni");
		$a_user[$i]["kraj_cislo"]=$db->getResult($tmp, $i, "kraj");
		$a_user[$i]["kraj"]=$db->getResult($tmp, $i, "kraj_name");
	}

	$tmp2 = $db->Query($SEL_COUNT);
	$a_user[0]["soucet"]=$db->getNumRows($tmp2);
	return $a_user;
}

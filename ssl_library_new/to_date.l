<?PHP
function to_date($sqldate){

$ar=explode('-',$sqldate);

$p=$ar[2];
$ar[2]=$ar[0];
$ar[0]=ltrim($p,"0");
$ar[1]=ltrim($ar[1],"0");

$date=implode('.',$ar);
return $date;
}

function write_date_sql($datum, $format = "sql_result"){ //01.01.2005 12:21:20
		$tmp1 = explode(" ", $datum);
		$datum1 = $tmp1[0];
		@$cas = $tmp1[1];
		$tmp = explode(".", $datum1);
		$d = (int) $tmp[0];
		@$m = (int) $tmp[1];
		@$y = (int) $tmp[2];
		
		$tmp = explode(":", $cas);
		@$h = (int) $tmp[0];
		@$min = (int) $tmp[1];
		@$sec = (int) $tmp[2];
	
//		if (intval($y) <2005){
//			return "";
//		} else {
			$mtime = mktime ($h, $min, $sec, $m, $d, $y ) ;
//		}
	
		switch ($format){
			case "date":
				$ret =  date ("j.n.Y", $mtime);
			break;
			case "time":
				$ret =  date ("H:i:s", $mtime);
			break;
			case "hhmm":
				$ret =  date ("H:i", $mtime);
			break;
			case "full":
				$ret =  date ("j.n.Y H:i:s", $mtime);
			break;
			case "sql_result":
				$ret =  date ("Y-m-d H:i:s", $mtime);
			break;
			case "timestamp":
				$ret =  $mtime;
			break;
			default:
				$ret =  date ("j.n.Y H:i:s", $mtime);
			break;
		}
		return $ret;
	}
?>
<?PHP
// Vystupem funkce je seznam
// Funkce vraci pocet zaznamu
// utf8

function get_list($men, &$db, $pocitat=false){
		preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts );
		$s1= $parts[1];
		$s2= $parts[2];
		$s3= $parts[3];
		$s4= $parts[4];
		$posledni = false;
		If ($s4<>0){
		  	$SEL = "SELECT * FROM m_acc_class WHERE s1=$s1 AND s2=$s2 AND s3=$s3 AND s4=$s4 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
			$posledni = true;
		} elseif ($s3<>0){
			$SEL = "SELECT * FROM m_acc_class WHERE s1=$s1 AND s2=$s2 AND s3=$s3 AND s4<>$s4 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
		} elseif ($s2<>0){
			$SEL="SELECT * FROM m_acc_class WHERE s1=$s1 AND s2=$s2 AND s3<>$s3 AND s4=0 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
		} elseif ($s1<>0){
			$SEL="SELECT * FROM m_acc_class WHERE s1=$s1 AND s2<>$s2 AND s3=0 AND s4=0 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
		} elseif ($s1==0){
			$SEL="SELECT * FROM m_acc_class WHERE s1<>0 AND s2=0 AND s3=0 AND s4=0 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
		}
			$tmp = $db->Query($SEL);
			$i =  $db->getNumRows($tmp);
	
			while($i--){
			
			  	 $a_vypis[$i]["class_descr"] =  $db->getResult($tmp, $i,"class_descr");
				 $a_vypis[$i]["men"]	= "men".$db->getResult($tmp, $i,"s1").".".$db->getResult($tmp, $i,"s2").".".$db->getResult($tmp, $i,"s3").".".$db->getResult($tmp, $i,"s4");
				 $a_vypis[$i]["path"] = $db->getResult($tmp,$i,"path");
				 $a_vypis[$i]["file"] = $db->getResult($tmp,$i,"class_abbrev");
				 $a_vypis[$i]["box"] = $db->getResult($tmp,$i,"box");
				 $a_vypis[$i]["posledni"] = $posledni;
				 if ($pocitat){
					 if ($posledni) {
						$a_vypis[$i]["submenu_count"] = 0;
					 } else {
						$t1 = $db->getResult($tmp, $i, "s1");
						$t2 = $db->getResult($tmp, $i, "s2");
						$t3 = $db->getResult($tmp, $i, "s3");
						$t4 = $db->getResult($tmp, $i, "s4");
						if ($t3<>0){
							$SEL2 = "SELECT COUNT(class_id) AS pocet FROM m_acc_class WHERE s1=$t1 AND s2=$t2 AND s3<>$t3 AND s4<>0 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
						} elseif ($t2<>0){
							$SEL2 = "SELECT COUNT(class_id) AS pocet FROM m_acc_class WHERE s1=$t1 AND s2=$t2 AND s3<>0 AND s4=0 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
						} elseif ($t1<>0){
							$SEL2 = "SELECT COUNT(class_id) AS pocet FROM m_acc_class WHERE s1=$t1 AND s2<>0 AND s3=0 AND s4=0 AND zobraz='Y' ORDER BY orde desc, class_descr desc";
						}
						 
						 $tmp2 = $db->Query($SEL2);
						 $a_vypis[$i]["submenu_count"] = $db->getResult($tmp2, 0, "pocet");
					}
				} else {
					$a_vypis[$i]["submenu_count"] = -1;
				}
				 
			}
			return @$a_vypis;
}
?>
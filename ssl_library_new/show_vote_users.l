<?PHP
function show_vote_users(&$db, $id_ank){

	$SEL="SELECT m_ank_hlasujici.id_m, m_ank_hlasujici.vaha, m_members.jmeno, m_members.prijmeni, m_members.clen_cislo
		FROM m_ank_hlasujici
		INNER JOIN m_members ON m_members.id_m=m_ank_hlasujici.id_m
	 WHERE m_ank_hlasujici.id_ank=$id_ank";

	$tmp=$db->Query($SEL);

	$i=$db->getNumRows($tmp);
	while ($i--){
		$a_users[$i]["id_m"]=$db->getResult($tmp, $i, "id_m");
		$a_users[$i]["vaha"]=$db->getResult($tmp, $i, "vaha");
		$a_users[$i]["jmeno"]=$db->getResult($tmp, $i, "jmeno");
		$a_users[$i]["prijmeni"]=$db->getResult($tmp, $i, "prijmeni");
		$a_users[$i]["clen_cislo"]=$db->getResult($tmp, $i, "clen_cislo");
	}
	return $a_users;
}
?>
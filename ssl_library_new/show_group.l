<?PHP
function show_group($db, $switch=0, $id_g=0){

	switch ($switch) {
	case 0:
		$SEL="select * from m_group order by name desc";
	break;
	case 1:
		$SEL="select * from m_group where id_g=$id_g";
	break;
	}
	$tmp=$db->Query($SEL);
	$i=$db->getNumRows($tmp);
	while($i--){
		$a_group[$i]["id_g"]= $db->getResult($tmp, $i, "id_g");
		$a_group[$i]["name"]= $db->getResult($tmp, $i, "name");
		$a_group[$i]["def_role"]= $db->getResult($tmp, $i, "def_role");
	}
	return $a_group;
}
?>
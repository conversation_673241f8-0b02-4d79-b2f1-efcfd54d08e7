<?PHP
/*
Kontrola vyskytu nazvu souboru v databazi (tedy i na disku)
Inputs: $s_name - nazev souboru
		i_sw - 1, ... zatim nepouzito
		default , kontroluje v journalist_files
Ouputs: true/false
*/
function check_content_path($s_name, &$db, $i_sw=0){
		switch($i_sw){
		case 1:
			$SEL = "";
			return $false;
		break;
		default:
			$SEL = "SELECT id FROM m_content WHERE path='$s_name'";
			$tmp = $db->Query($SEL);
			$i =  $db->getNumRows($tmp);
			
			If($i==0){
				return false;
			} else {
				return true;
			}
		}


}
?>
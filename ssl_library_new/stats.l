<?PHP
/*
    Funkce delajici statisticke informace dle toho jaky je dan typ.
    1 - pocet clenu v intranetu
    2 - pocet clenu jenz dali souhlas ke zverejneni svych udaju
*/
function stats(&$db, $type){

    // Rok vuci kteremu se kontroluje zaplaceni prispevku (lide mohou byt cleny i pokud zpalatili predchozi rok).
    // Spravne by bylo predelat dotaz, aby pouzil pohled paltnych clenu ...
    $rok = ((int) date("Y")) -1;
	switch($type){
		case 0:
			$SEL="SELECT count(id_m) as pocet FROM m_members WHERE disable='N' AND prizpevky>='".$rok."'";
		break;
		case 1:
			$SEL="SELECT count(id_m) as pocet FROM m_members WHERE disable='N' AND public='Y' AND prizpevky>='".$rok."'";
		break;
	}
	$tmp=$db->Query($SEL);
	return $db->getResult($tmp,0,"pocet");
}

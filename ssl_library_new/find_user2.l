<?PHP
/*
SWITCH - 0 vsichni uzivatele
		 1 uzivatele jenz dali souhlas
*/
function find_user2(&$db, $switch, $l_start, $where, $where_zajmy, &$total){

    // Rok vuci kteremu se kontroluje zaplaceni prispevku (lide mohou byt cleny i pokud zpalatili predchozi rok).
    // Spravne by bylo predelat dotaz, aby pouzil pohled paltnych clenu ...
    $rok = ((int) date("Y")) -1;


	$pocet_na_radek=40;
	$a_user = array();
	$sel_limit=" LIMIT ".$l_start*$pocet_na_radek.", $pocet_na_radek";
	$sel_order=" ORDER BY m.prijmeni, m.jmeno";
	$where=trim($where);

	// vytvoreni podminky pro vyhledavani v zajmech
	$i = 0;
	if (is_countable($where_zajmy)) {
    	$i=count($where_zajmy);
	}
	$search_zajmy="";
	if(is_array($where_zajmy)&&($i>0)){
		while($i--){
			$search_zajmy.=" ,".$where_zajmy[$i];
		} 
		$search_zajmy=substr($search_zajmy,2);
		
		//vytvoreni id uzivatelu, jenz vyhovuji danym zajmum
		$SEL="SELECT id_m FROM m_zajmy WHERE zajem IN($search_zajmy)";
		$tmp = $db->Query($SEL);
		$j=$db->getNumRows($tmp);

		$search_zajmy="";
		while($j--){
			$search_zajmy.=" ,".$db->getResult($tmp,$j,"id_m");
		} 
		if(strlen($search_zajmy)>0){
			$search_zajmy=" AND m.id_m IN (".substr($search_zajmy,2).")";
		}
	
	} elseif(strlen($where_zajmy)>0) {
		// zajmy neni pole ale string pro zajmy
		$SEL="SELECT id_m FROM m_zajmy WHERE pozn like '%".addslashes($where_zajmy)."%'";
		$tmp = $db->query($SEL);
		$j=$db->getNumRows($tmp);

		$search_zajmy="";
		while($j--){
			$search_zajmy.=" ,".$db->getResult($tmp,$j,"id_m");
		} 
		if(strlen($search_zajmy)>0){
			$search_zajmy=" AND m.id_m IN (".substr($search_zajmy,2).")";
		}
	}
	
	switch ($switch){
		case 0: // pro vsechny uzivatele
			if (strlen($where)!=0){
				$where = " WHERE m.prizpevky>='".$rok."' AND ".$where.$search_zajmy;
			} elseif(strlen($search_zajmy)>0) {
				$where = " WHERE m.prizpevky>='".$rok."' AND ".substr($search_zajmy,4);
			}
		break;
		case 1: //POUZE cleny se souhlasem
			if (strlen($where)!=0){
				$where = " WHERE m.disable='N' AND m.prizpevky>='".$rok."' AND m.public='Y' AND ".$where.$search_zajmy;
			} else {
				$where = " WHERE m.disable='N' AND m.prizpevky>='".$rok."' AND m.public='Y' ".$search_zajmy;
			}
		break;
	}

	$SEL="
		SELECT 
			m.*, 
			z.name AS zamestnani_str, 
			k.kraj_name,
			g.name 
		FROM 
			m_members m
		INNER JOIN m_list_zamestnani AS z ON z.id_l_w=m.zamestnani
		INNER JOIN m_list_kraj AS k ON k.id_kraj=m.kraj
		INNER JOIN m_group AS g ON g.id_g=m.clen
		$where 
		$sel_order 
		$sel_limit";
	$SEL_COUNT="
		SELECT 
			COUNT(m.id_m) AS soucet 
		FROM 
			m_members m
		INNER JOIN m_list_zamestnani AS z ON z.id_l_w=m.zamestnani
		INNER JOIN m_list_kraj AS k ON k.id_kraj=m.kraj
		$where";

	$tmp = $db->Query($SEL);
//echo $SEL;
	$i = $db->getNumRows($tmp);
	while ($i--){
		$a_user[$i]["jmeno"]=$db->getResult($tmp, $i, "jmeno");
		$a_user[$i]["prijmeni"]=$db->getResult($tmp, $i, "prijmeni");
		$a_user[$i]["id_m"]=$db->getResult($tmp, $i, "id_m");
		$a_user[$i]["prezdivka"]=$db->getResult($tmp, $i, "prezdivka");
		$a_user[$i]["titul"]=$db->getResult($tmp, $i, "titul");
    $a_user[$i]["titul_za_jmenem"]=$db->getResult($tmp,$i, "titul_za_jmenem");
		$a_user[$i]["tel_d"]=$db->getResult($tmp, $i, "tel_d");
		$a_user[$i]["tel_p"]=$db->getResult($tmp, $i, "tel_p");
		$a_user[$i]["fax_d"]=$db->getResult($tmp, $i, "fax_d");
		$a_user[$i]["fax_p"]=$db->getResult($tmp, $i, "fax_p");
		$a_user[$i]["email"]=$db->getResult($tmp, $i, "email");
		$a_user[$i]["mobil"]=$db->getResult($tmp, $i, "mobil");
		$a_user[$i]["www"]=$db->getResult($tmp, $i, "www");
		$a_user[$i]["adresa"]=$db->getResult($tmp, $i, "adresa");
		$a_user[$i]["ulice"]=$db->getResult($tmp, $i, "ulice");
		$a_user[$i]["psc"]=$db->getResult($tmp, $i, "psc");
		$a_user[$i]["mesto"]=$db->getResult($tmp, $i, "mesto");
		$a_user[$i]["zamestnani_cislo"]=$db->getResult($tmp, $i, "zamestnani");
		$a_user[$i]["clen_cislo"]=$db->getResult($tmp, $i, "clen_cislo");
		$a_user[$i]["zamestnani"]=$db->getResult($tmp, $i, "zamestnani_str");
		$a_user[$i]["zamestnani_pozn"]=$db->getResult($tmp, $i, "zamestnani_pozn");
		$a_user[$i]["public"]=$db->getResult($tmp, $i, "public");
		$a_user[$i]["prizpevky"]=$db->getResult($tmp, $i, "prizpevky");
		$a_user[$i]["sigh"]=$db->getResult($tmp, $i, "sigh");
		$a_user[$i]["clen"]=$db->getResult($tmp, $i, "clen");
		$a_user[$i]["date"]=$db->getResult($tmp, $i, "date");
		$a_user[$i]["rok_narozeni"]=$db->getResult($tmp, $i, "rok_narozeni");
		$a_user[$i]["kraj_cislo"]=$db->getResult($tmp, $i, "kraj");
		$a_user[$i]["kraj"]=$db->getResult($tmp, $i, "kraj_name");
		$a_user[$i]["prislusnost"]=$db->getResult($tmp, $i, "name");
	}

	$tmp2 = $db->Query($SEL_COUNT);
	$total=$db->getResult($tmp2,0,"soucet");
	return $a_user;
}
?>
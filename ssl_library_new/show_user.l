<?PHP

// 2014-04-06 - PM - <PERSON><PERSON> změna rok na rok - 1, hlas<PERSON>t mohou i ti, co letos nemaj<PERSON> z<PERSON>laceno a platili loni

   
function show_user(&$db, $all=false){


	$rok = intval(date("Y")) - 1;
	if($all) {
		$SEL="SELECT id_m, jmeno, prijmeni, clen_cislo, prizpevky FROM m_members WHERE disable='N' ORDER by prijmeni desc";
	} else {
		$SEL="SELECT id_m, jmeno, prijmeni, clen_cislo FROM m_members WHERE disable='N' AND prizpevky>='".$rok."' ORDER by prijmeni desc";
	}
	
	$tmp=$db->Query($SEL);

	$i=$db->getNumRows($tmp);
	while ($i--){
		$a_users[$i]["id_m"]=$db->getResult($tmp, $i, "id_m");
		$a_users[$i]["jmeno"]=$db->getResult($tmp, $i, "jmeno");
		$a_users[$i]["prijmeni"]=$db->getResult($tmp, $i, "prijmeni");
		$a_users[$i]["clen_cislo"]=$db->getResult($tmp, $i, "clen_cislo");

		if($all AND (intval($db->getResult($tmp, $i, "prizpevky"))<$rok)){
			$a_users[$i]["prijmeni"] = "NENI ".$a_users[$i]["prijmeni"];
		}
	}
	return $a_users;
}

function get_user($db, $nId_m) {
	$sSQL = "SELECT * " .
		"FROM m_members " .
		"WHERE id_m = " . $nId_m;
	$vysledek = $db->Query($sSQL);
	$a_user = $db->FetchArray($vysledek);
	return $a_user;
}
?>

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<!--

    <PERSON><PERSON><PERSON><PERSON> rozepsaného příspěvku

    Princip
    Tato strán<PERSON> se zavola, pokud se při odhlá<PERSON>,
    že byla odeslána POST data s příspěvkem. Stránka
    by měla umožnit příspěvek zachytit a uložit.
    
    Autoři:
    <PERSON><PERSON><PERSON>
    2012-08-17, TK: Stránka převlečena do nové podoby


-->
<html>
<head>
  <title>Přihlášení do intranetu Mensy</title>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
  <meta http-equiv="content-language" content="cs-CZ">
  <meta name="Author" content="<PERSON><PERSON><PERSON>">
  <meta name="ROBOTS" content="NONE">
  <meta name="Copyright" content="Mensa <PERSON>">
  <meta name="Description" content="Fórum pro členy <PERSON>, ve kterém mohou diskutovat a sdílet digitální obsah.">  
  <link rel="shortcut icon" href="/favicon.ico" type="image/x-icon">
  
  <style type="text/css">
    body
    {
        width:              100%;
        margin:             0; 
        padding:            0; 
        font-family:        Georgia, Times, serif;
        font-size:          90%;        
    }
 
    div#hlavicka
    {
        width:              100%;
        background-image:   url(images-new/bg-menu.jpg); 
        height:             167px; 
        vertical-align:     top; 
        text-align:         center;
    }
 
    td#horni_odkaz
    {
        text-align:         center; 
        color:              white; 
        padding-top:        8px;    
    }
    
    td#horni_odkaz a, td#horni_odkaz span
    {
        margin-left:        20px; 
        margin-right:       20px; 
        color:              white; 
        text-decoration:    none;
        font-size:          14px;
    }
    
    
 
    div.okno
    {
        width:              340px;
        margin:             60px auto;
        border:             1px solid rgb(2, 105, 158);
        background-color:   white;    
    }
 
    div.hlavicka_okna
    {
        background-color:   rgb(2, 105, 158); 
        color:              white; 
        height:             1.9em;
        width:              100%;
        margin-bottom:      1em;
        text-align:         center;
        padding-top:        8px;
        font-weight:        bold;    
    }
 
 
    div.okno a
    {
        color: rgb(2, 105, 158); 
        text-decoration: none;    
    }

    div.okno p
    {
        text-align:         left;
        padding:            1em;    
        padding-top:        0;
    }
 
    div.okno .chyba
    {
        text-align: center;
        color:      red;
        padding:    1em;    
    }
 
    div#paticka
    {
        border-top: solid #505050 1px;
        text-align: center;
        color:      #505050;
        font-size:  90%;
        padding:    0.8em;    
    }
 
  </style>
</head>




<body>
<div id="hlavicka">
    <table border=0 width="802" cellpadding="0" cellspacing="0" style="margin: 0 auto;">
    <tr>
        <td style="text-align: right; padding-top: 33px;">
            <img class="logo-mensa" src="images-new/logo-mensa.png" width="158" height="78" alt="Logo Mensy">
        </td>
    </tr>
    
    <tr>
        <td id="horni_odkaz">
            <a href="http://www.mensa.cz/">Webové stránky Mensy</a>
            <span style="font-weight: bold;">Intranet Mensy</span>
            <a href="http://casopis.mensa.cz/">Webový časopis Mensy</a>
        </td>    
    </tr>
    </table>    
</div>


<?php
echo "<div class='okno'>
    <div class='hlavicka_okna'>Záchrana rozepsaného příspěvku</div>
    <p class='chyba'>Omlouváme se, byl jste odhlášen z důvodu nečinosti.
    Prosím, zkopírujte si rozepsaný příspěvek.</p>
    <p>{$_POST['message']}</p>
</div>";	
?>	
   

<div id="paticka">&copy; 2002-2012 Mensa Česko</div>



<script src="https://ssl.google-analytics.com/urchin.js" type="text/javascript"></script>
<script type="text/javascript">
//<![CDATA[
  _uacct = "UA-2154302-5";
  urchinTracker();
//]]>
</script>

</body>
</html>

<?PHP 
    /*  Nic dalšího nedělej.
        Nutny exit, protože jinak dojde k přesměrování. */
    exit(0);
<?PHP
/* 
//utf8
	Funkce vytváøí novou skupinu (roli)
	INPUTS:
		$s_role_name - nazev skupiny
	OUTPUTS:
		$role_id - cislo vytvorene skupiny
		true/false - informace o vytvoreni zaznamu
*/
function put_role($s_role_name, &$role_id, $created_by, &$db){

		global $REMOTE_USER;
		$tmp = $db->Query("SELECT MAX(role_id) AS 'role_id' FROM m_acc_role");
		$role_id=$db->getResult($tmp, 0, "role_id");
		$role_id++;
		
		$SEL="INSERT INTO m_acc_role (role_id, role_descr, created, created_by, deleted, deleted_by) VALUES ($role_id, '$s_role_name', Now(), '$created_by', Null, Null)";

		If ($db->Query($SEL)){
			return true;
		} else {
			return false;
		}

}
?>
<?PHP
function vote($db, $a_vote, $akce, $votech=""){

	switch($akce){
		case 1: //hlasovani pres radiobutton
			$votech[]=$a_vote["id_volby"];
		case 2: //hlasovani pres zaskrtavatka	
			$id_m = $a_vote["id_m"];								// uid uzivatele
		 	$id_ank = $a_vote["id_ank"]; 							// id ankety
			$text = $a_vote["text"];								// text

			$SEL="SELECT id_hlas FROM m_ank_hlasujici WHERE id_ank=$id_ank AND id_m=$id_m";
			$tmp=$db->Query($SEL);
			if($db->getNumRows($tmp)==1) $id_hlas=$db->getResult($tmp, 0, "id_hlas");

			$SEL="UPDATE m_ank_hlasujici SET text='$text', hlas='Y' WHERE id_ank=$id_ank AND id_m=$id_m";
			$tmp=$db->Query($SEL);

			$i=count($votech);
			while ($i--){
				$id_volby=$votech[$i];
				$SEL="INSERT INTO m_ank_hlas_volba VALUES ($id_hlas, $id_volby, $id_ank)";
				$tmp=$db->Query($SEL);

			}
		break;

		case 3: //update vahy hlasu
			$vaha=$a_vote["vaha"];
			$id_ank=$a_vote["id_ank"];
			$id_m=$a_vote["id_m"];
			$SEL="UPDATE m_ank_hlasujici SET vaha=$vaha WHERE id_ank=$id_ank AND id_m=$id_m";
			$tmp=$db->Query($SEL);

		break;
	}
	return true;
}
?>
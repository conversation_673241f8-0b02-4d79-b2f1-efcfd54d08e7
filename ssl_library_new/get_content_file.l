<?PHP
/* 
Vraci odkaz na konkretni soubor na disku, ktery se ma zobrazit
Inputs: id_c - id souboru
Outputs: <PERSON><PERSON><PERSON> pole se zaznamy 
*/
function get_content_file($id_c, &$db){
		$SEL = "SELECT * FROM m_content WHERE id=$id_c";
		$tmp = $db->Query($SEL);
		$a_vypis["path"] = $db->getResult($tmp, 0, "path");
		$a_vypis["name"] = $db->getResult($tmp, 0, "name");
		preg_match("/\.(.*)\$/", $db->getResult($tmp, 0, "path"), $part);
		$a_vypis["suffix"] = $part[count($part)-1];
		$a_vypis["descr"] = $db->getResult($tmp,0,"descr");
		$a_vypis["id_c"] = $db->getResult($tmp, 0, "id");
		
		return $a_vypis;
}
?>
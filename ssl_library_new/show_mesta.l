<?php
function show_mesta(&$db, $bNeuvedeno = true){
	$sSEL = ($bNeuvedeno) ?
				"select * from m_list_mesta order by mesto desc" :
				"select * from m_list_mesta where mesto not like '%neuvedeno%' order by mesto desc";
	$tmp = $db->Query($sSEL);

	$i = $db->getNumRows($tmp);
	while($i--){
		$a_mesta[$i]["id"] = $db->getResult($tmp, $i, "id_mesta");
		$a_mesta[$i]["mesto"] = $db->getResult($tmp, $i, "mesto");
	}

	return $a_mesta;
}
?>

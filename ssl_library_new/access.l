<?PHP
/* 
funkce overuje opraveni uzivatelu pomoci access_module
INPUTS: $c_right - <PERSON><PERSON><PERSON><PERSON><PERSON>, ktera jsou potreba pro danou sekci, 
		$s_sect - cislo sekce, 
		$login - login uzivatele
OUTPUTS: true/false
*/

function access($c_right, $s_sect, $login, &$db) {



if(substr($login,0,2)=="cz"){
	$login=substr($login,2);
}

	switch($c_right){
		case "read":
			$i_rights = 4;
		break;
		case "update":
			$i_rights = 1;
		break;
		case "create":
			$i_rights = 2;
		break;
		case "free_1":
			$i_rights = 16;
		break;
		case "class":
			$i_rights = 32;
		break;
		case "delete":
			$i_rights = 64;
		break;
		default:
			$i_rights = 0 ;
		break;
	}


	require_once("../ssl_library_new/get_acc_restrict.l");
		
//		preg_match( "/^men(\w+)\.(\d+)\.(\d+)\.(\d+)$/", $s_sect, $parts );
//		$sect=$parts[1].".".$parts[2].".".$parts[3].".".$parts[4];

		$val=get_acc_restrict($login, $s_sect, 0, $db);
		//echo $val."-".$i_rights;
		If(($i_rights & $val)>0){
			return true;
		} else {
			return false;
		}


//	return true;
}
?>
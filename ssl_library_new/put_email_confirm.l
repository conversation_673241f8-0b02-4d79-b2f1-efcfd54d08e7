<?PHP
function put_email_confirm(&$db, $new_email, $EmailKey, $id_m){
	
	$now = date("YmdHi");
	$delete_time = date("YmdHi",mktime(date("G")-12, date("i"),0, date("m"), date("d"), date("Y")));
	
	$SEL= "DELETE FROM change_email WHERE id_member=$id_m OR Date<$delete_time";
	$tmp = $db->Query($SEL);
	
	$SEL="INSERT INTO change_email (email, EmailKey, Date, id_member) VALUES('$new_email', '$EmailKey', $now, $id_m)";
	$tmp = $db->Query($SEL);

}
?>
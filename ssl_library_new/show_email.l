<?PHP
function show_email(&$db, $type=0, $id_email = 0){
	switch ($type) {
		case 0:
			$SEL="select * from m_mail";
			break;
		case 1:
			$SEL="select * from m_mail WHERE id=".$id_email;
			break;
	}

	$tmp=$db->Query($SEL);
	$i=@$db->getNumRows($tmp);
	while ($i--){
		$a_a[$i]["id"]=@$db->getResult($tmp, $i, "id");
		$a_a[$i]["name"]=@$db->getResult($tmp, $i, "name");
		$a_a[$i]["descr"]=@$db->getResult($tmp, $i, "descr");
	}
	return $a_a;
}

// kodovani, ve kterem jsou stringy
$rfc2047encode_charset = "utf-8";

// zakoduj jmeno v emailove adrese
function rfc2047encode_address($str)
{
    return rfc2047encode($str, "@.,:;<>[]\\\"()");
}

// zakoduj string (subject atd.)
function rfc2047encode_string($str)
{
    return rfc2047encode($str, null);
}

// zakoduj string s pouzitim specialnich symbolu
function rfc2047encode($str, $specials)
{
    global $rfc2047encode_charset;

    $len = strlen($str);

    // najdi pozici, od ktere a do ktere encodovat
    $p0 = $p1 = null;
    for ($p = 0; $p < $len; $p++) {
        if (ord($str[$p]) < 0x20
            || ord($str[$p]) >= 0x80
            || ($str[$p] == '=' && $str[$p + 1] == '?')
            || ($specials && strchr($specials, $str[$p])))
        {
            if (is_null($p0))
                $p0 = $p;
            $p1 = $p;
        }
    }

    if (is_null($p0)) {
        // neni co encodovat
        return $str;
    }

    // encoduj cele slova
    while ($p0 > 0) {
        if ($str[$p0 - 1] == ' ' || $str[$p0 - 1] == '\t')
            break;
        $p0--;
    }
    while ($p1 < $len - 1) {
        if ($str[$p1 + 1] == ' ' || $str[$p1 + 1] == '\t')
            break;
        $p1++;
    }

    // spocitej maximalni pocet znaku, ktere se daji zakodovat najednou
    // (maximalni delka zakodovaneho retezce je 76 znaku)
    $max_len = floor((76 - 7 - strlen($rfc2047encode_charset)) / 4.0) * 3;

    // zkopiruj zacatek
    $result = substr($str, 0, $p0);

    // zakoduj jednotlive bloky
    while ($p0 <= $p1) {
        $block_len = $p1 - $p0 + 1;
        if ($block_len > $max_len)
            $block_len = $max_len;

        $block = substr($str, $p0, $block_len);
        $p0 += $block_len;

        $result .= "=?" . $rfc2047encode_charset . "?B?" . base64_encode($block) . "?=";
    }

    // zkopiruj konec
    $result .= substr($str, $p1 + 1);

    return $result;
}

?>

<?php

/**
 * @deprecated use directly database\model\mensaweb\Members::getUserByCredentials or getUserByLogin
 */
function user($login, $heslo, $switch = 0, &$db)
{
    if (substr($login, 0, 2) == "cz") {
        $login = substr($login, 2);
    }

    switch ($switch) {
        case 0:
            $SEL = "select * from m_members WHERE disable='N' AND clen_cislo LIKE '$login' AND heslo=old_password('$heslo')";
            break;
        case 1:
            $SEL = "select * from m_members WHERE disable='N' AND clen_cislo LIKE '$login'";
            break;
    }

    $tmp = $db->Query($SEL);

    if (intval($db->getNumRows($tmp)) === 1) {
        $user = $db->FetchArray($tmp);

        return $user;
    }
    else {
        return false;
    }
}

<?PHP
/* 
utf8
	Funkce aktualizuje skupinu (roli)
	INPUTS:
		$s_role_name - nazev skupiny
		$role_id - cislo role k update
	OUTPUTS:
		$role_id - cislo vytvorene skupiny
		true/false - informace o vytvoreni zaznamu
*/
function update_role($s_role_name, $role_id, $created_by, &$db){
		$SEL="UPDATE m_acc_role SET role_descr='$s_role_name', created=Now(), created_by='$created_by' WHERE role_id=$role_id";

		If ($db->Query($SEL)){
			return true;
		} else {
			return false;
		}

}
?>
<?PHP
function activate_email($db, $EmailKey, &$strError, &$id_m){
	$strError = "";
	$delete_time = date("YmdHi",mktime(date("G")-12, date("i"),0, date("m"), date("d"), date("Y")));
	
	$SEL= "DELETE FROM change_email WHERE Date<$delete_time";
	$tmp = $db->Query($SEL);

	$SEL="SELECT * FROM change_email WHERE EmailKey='$EmailKey'";
	$tmp = $db->Query($SEL);
	if ($db->getNumRows($tmp)==1){
		$id_m = $db->getResult($tmp, 0, "id_member");
		$email = $db->getResult($tmp, 0, "email");
		$SEL = "UPDATE m_members SET email='$email' WHERE id_m=$id_m";
		$tmp = $db->Query($SEL);
		
		$SEL="DELETE FROM change_email WHERE EmailKey='$EmailKey'";
		$tmp=$db->Query($SEL);
		$strError=$email;
		return true;
	} else {
		$strError = "Zadaný klíč neexistuje nebo mu již skončila 12hodinová expirační doba.";
		return false;
	}
}

<?PHP		
/*  funkce provede
1 - ulozeni na server

INPUTS:
1 - vkladane data
$upload_name, nazev ciloveho souboru
*/

function upload_magazine($upload, $upload_name)
{

    echo "<p>Zpracovávám.</p>";

    // kam se casopis bude nahravat
    // odtamtud pak soubor uvidi dalsi stranky
    // neni potreba zadny zapis do databaze nebo neco takoveho
	$uploadDir = '/home/<USER>/files/magazine/';

    // over nahrana data
    if ($upload != 'none')
    {            
        //print_r($upload);
        // Array ( [name] => c11-1011.pdf [type] => application/pdf [tmp_name] => /tmp/phpbuc5O5 [error] => 0 [size] => 42032 ) 

        echo "<p>Server obdržel soubor o velikosti $upload[size] byte.</p>";

        // dekoduj puvodni jmeno soubru
        if ($upload_name == '') $upload_name = $upload[name]; //odstrani cestinu
        echo "<p><PERSON><PERSON><PERSON><PERSON> jméno: $upload_name.</p>";

        // sestav cilovou cestu
        $dest = $uploadDir . $upload_name; 

        // v soucasne dobe nefunguje kvuli omezeni prav
        if (move_uploaded_file($upload["tmp_name"], $dest))
        //if (@copy($upload["tmp_name"], $dest))
        { 
            echo "<p>Soubor byl úspěšně vložen.</p>\n"; 

        } else { 
            echo "<font color=\"FF0000\"><b>Soubor nemuze byt nahran do ciloveho adresare, funkce @copy('$upload[tmp_name]', '$uploadDir') selhala.</b></font><br>\n"; 
            
            // zjisteni elementarnich pricin
            $perms = @fileperms($uploadDir); 
            $owner = @fileowner($uploadDir); 
            if (!$perms){ 
                echo "Directory does not exist: $uploadDir<BR>\n"; 
            } else{ 
                $myuid = getmyuid(); 
                if (!($perms & 2) && !(($owner == $myuid) && ($perms & 128))){ 
                    echo  "Script doesn't have permission to write in $uploadDir<br>\n"; 
                } //if(!($prems & 2)
            } //if (!$perms){ 
        } //If (@copy
        
    // KONEC UPLOADU
    } else { 
       echo "<font color=\"FF0000\"><b>Soubor se vubec nepodarilo nahrat na server.</b></font><br>\n"; 
       echo "Filesize exceeds limit in FORM or php.ini<br>\n"; 
    } 
    
}
?>

<?PHP
function inquiry_del(&$db, $id_ank){

	$SEL="SELECT owner FROM m_ank_ankety WHERE id_ank=$id_ank";
	$tmp=$db->Query($SEL);
	if ($db->getNumRows($tmp)!=1) return 5; //anketa nenalezena

	global $login;
	global $heslo;

	if ($_REQUEST['men']=="men2.1.1.0") {
		$a_user=user($login,$heslo, 0, $db);
		$user_id=$a_user["id_m"];
		$owner=$db->getResult($tmp,0,"owner");
		if ($owner!=$user_id) return 4; //pokus o smazani cizi ankety
	}
	
	$DEL="DELETE FROM m_ank_hlas_volba WHERE id_ank=$id_ank";
	$tmp=$db->Query($DEL);
	$DEL="DELETE FROM m_ank_ankety WHERE id_ank=$id_ank";
	$tmp=$db->Query($DEL);
	$DEL="DELETE FROM m_ank_volby WHERE id_ank=$id_ank";
	$tmp=$db->Query($DEL);
	$DEL="DELETE FROM m_ank_hlasujici WHERE id_ank=$id_ank";
	$tmp=$db->Query($DEL);
}
?>
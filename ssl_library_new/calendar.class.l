<?PHP
/**********************************************************************
AUTHOR:    (c) <PERSON>, <EMAIL>
CREATED:   2007
PROJECT:   Mensaweb, calendar
**********************************************************************/

// requires mcaptcha.php, problem is, that ssl_library_new/calendar.class.l can
// be included in different contexts on different places,
// so relative path would not work.
require_once LIB_2013_DIR . "/mcaptcha.php";

class calendar{
    var $actions = array();
    var $db;
    var $linux;
    var $param = array();

    function calendar($db, $linux=false){
        $this->db=$db;
        $this->linux = $linux;
        if ($linux) {
            $this->param["%B"] = "%B";
            $this->param["%u"] = "%u";
            $this->param["%e"] = "%e";
            $this->param["%B %Y"] = "%B %Y";
            $this->param["%a: %e. %B %Y"] = "%a: %e. %B %Y";
            $this->param["%V"] = "%V";
        } else {
            $this->param["%B"] = "%B";
            $this->param["%u"] = "%w";
            $this->param["%e"] = "%#d";
            $this->param["%B %Y"] = "%B %Y";
            $this->param["%a: %e. %B %Y"] = "%a: %#d. %B %Y";
            $this->param["%V"] = "%W";
        }
    }

function mesic_1p($a) {
    $mesice = ['Ledna', 'Února', 'Března', 'Dubna', 'Května', 'Června', 'Července', 'Srpna', 'Září', 'Října', 'Listopadu', 'Prosince'];
    $mesice_1p = ['Leden', 'Února', 'Březen', 'Duben', 'Květen', 'Červen', 'Červenec', 'Srpen', 'Září', 'Říjen', 'Listopad', 'Prosinec'];
    return str_replace($mesice, $mesice_1p, $a);
}


    function get_action_list($first_date, $last_date) {
        $ar = array();

        $strSql = "SELECT DATE_FORMAT(date_start, '%e.%c.') as d, DATE_FORMAT(date_start, '%e.%c.%Y %k:%i') as ddd, DATE_FORMAT(date_end, '%e.%c.%Y') as dd, nazev, date_pre, id_a FROM mc_akce WHERE date_start<='$last_date' AND date_end>='$first_date' ORDER BY date_start DESC, date_end";
        $act = $this->db->Query($strSql);
        $poc = $this->db->getNumRows($act);
        while($poc--){
            $ar[$poc]["date_start"] = $this->db->getResult($act,$poc,"d");
            $ar[$poc]["date_start_full"] = $this->db->getResult($act,$poc,"ddd");
            $ar[$poc]["date_end"] = $this->db->getResult($act,$poc,"dd");
            $ar[$poc]["date_pre"] = $this->db->getResult($act,$poc,"date_pre");
            $ar[$poc]["nazev"] = $this->db->getResult($act,$poc,"nazev");
            $ar[$poc]["id_a"] = $this->db->getResult($act,$poc,"id_a");
        }

        return $ar;
    }

    /**
     * @param int $pocet
     * @return string
     */
    function get_past_event_attendees_html($pocet)
    {
        switch ($pocet) {
            case 1:
                return "<br />Na akci byl přihlášen <b>1 účastník</b>.<br /><br />";
            case 2:
            case 3:
            case 4:
                return "<br />Na akci byli přihlášeni <b>{$pocet}&nbsp;účastníci</b>.<br /><br />";
            default:
                return "<br />Na akci bylo přihlášeno <b>{$pocet}&nbsp;účastníků</b>.<br /><br />";
        }
    }

    /**
     * @param int $pocet
     * @return string
     */
    function get_actual_event_attendees_html($pocet)
    {
        switch ($pocet) {
            case 1:
                return "<br />K dnešnímu dni je přihlášen <b>1 účastník</b>.<br /><br />";
            case 2:
            case 3:
            case 4:
                return "<br />K dnešnímu dni jsou přihlášeni <b>{$pocet}&nbsp;účastníci</b>.<br /><br />";
            default:
                return "<br />K dnešnímu dni je přihlášeno <b>{$pocet}&nbsp;účastníků</b>.<br /><br />";
        }
    }

    /**
     * @param int $id_akce
     * @param int $kapacita
     * @param bool $is_past_event
     * @return string
     */
    function get_event_attendee_list_html($id_akce, $kapacita, $is_past_event)
    {
        $strSql = "SELECT jmeno, datum FROM www_form_1 WHERE id_a = {$id_akce} ORDER BY datum DESC ";

        $ucastnici = $this->db->Query($strSql);
        $pocet = $this->db->getNumRows($ucastnici);

        $poc_u = $is_past_event
            ? $this->get_past_event_attendees_html($pocet)
            : $this->get_actual_event_attendees_html($pocet);

        $ret = "";
        $poradi = 1;

        while ($pocet--) {
            $ret .= "<tr><td align=right><b>".$poradi.".</b><td>&nbsp;".$this->db->getResult($ucastnici, $pocet, "jmeno")."</td>";
            $ret .= "<td>".$this->db->getResult($ucastnici, $pocet, "datum")."</td></tr>";

            if ($poradi == $kapacita) {
                $ret .= "<tr><td colspan=3><hr></td></tr>";
            }

            $poradi++;
        }

        if (strlen($ret) > 0) {
            $ret = "<table border='0'>".$ret."</table>";
            $ret = $poc_u.$ret;
        }
        else {
            $ret = "Prozatím se nikdo nepřihlásil.";
        }

        return $ret;
    }


    function get_action(int $id_a=0, int $pravidelna = 0, string $typ="", string $ms="", int $aktualni=0, bool $zobraz_ikonky = false, int $id_m=0): string
    {
        $mesto = "";

        // vypíše úplně všechno, pokud dostane prázdné parametry.
        // proto ukonči volání, pokud se tak stane
        if ($id_a==0 and $pravidelna == 0 and $typ=="" and $ms=="" and
           $aktualni==0 and $id_m==0 and $mesto=="")
               return "Prosím, vyberte konkrétní akci ze seznamu vpravo.";




        $WHERE = "";
        if ($id_a <> 0){
            $WHERE .= " AND id_a = $id_a";
        }
        if ($pravidelna == 1){
            $WHERE .= " AND a.id_parent > 0 ";
        } elseif ($pravidelna == -1) {
            $WHERE .= " AND a.id_parent = -1 ";
        }
        if ($typ == "netest"){
            $WHERE .= " AND (typ NOT LIKE 'test') AND typ NOT LIKE 'diag' ";
        } elseif ($typ == "test"){
            $WHERE .= " AND (typ LIKE 'test' OR typ LIKE 'diag' ) ";
        } elseif (strlen($typ)>0){
            $WHERE .= " AND typ LIKE $typ";
        }
        if ($aktualni == 1) { // aktualni
            $WHERE .= " AND a.date_start> Now() ";
        } elseif ($aktualni == -1) { // jen starsi
            $WHERE .= " AND a.date_start< Now() ";
        }
        if (strlen($WHERE)> 0 ){
            $WHERE = " WHERE ".substr($WHERE, 4, strlen($WHERE));
        }
        $strSql = "SELECT a.*, m.jmeno, m.prijmeni FROM mc_akce AS a INNER JOIN m_members AS m ON a.id_owner=m.id_m AND m.disable='N' ".$WHERE." ORDER BY a.date_start ";

        $act = $this->db->Query($strSql);
        $str = "";
        $i = 0;
        if ($this->db->getNumRows($act) > 0 ) {
            $seznam=$this->db->FetchAssocAll($act);
            foreach ($seznam as $radek) {
                $this->actions[$i]["id_a"] = $radek["id_a"];
                $this->actions[$i]["id_parent"] = $radek["id_parent"];
                $this->actions[$i]["date_start"] = $radek["date_start"];
                $this->actions[$i]["date_end"] = $radek["date_end"];
                $this->actions[$i]["date_pre"] = $radek["date_pre"];
                $this->actions[$i]["nazev"] = $radek["nazev"];
                $this->actions[$i]["perex"] = $radek["perex"];
                $this->actions[$i]["nazev_en"] = $radek["nazev_en"];
                $this->actions[$i]["popis"] = $radek["popis"];
                $this->actions[$i]["popis_en"] = $radek["popis_en"];
                $this->actions[$i]["place"] = $radek["place"];
                $this->actions[$i]["city"] = $radek["city"];
                $this->actions[$i]["id_area"] = $radek["id_area"];
                $this->actions[$i]["area_name"] = $radek["area_name"];
                $this->actions[$i]["url"] = $radek["url"];
                $this->actions[$i]["id_owner"] = $radek["id_owner"];
                $this->actions[$i]["id_editor"] = $radek["id_editor"];
                $this->actions[$i]["telefon"] = $radek["telefon"];
                $this->actions[$i]["email"] = $radek["email"];
                $this->actions[$i]["typ"] = $radek["typ"];
                $this->actions[$i]["ms"] = $radek["ms"];
                $this->actions[$i]["sig"] = $radek["sig"];
                $this->actions[$i]["dm"] = $radek["dm"];
                $this->actions[$i]["mensa"] = $radek["mensa"];
                $this->actions[$i]["test"] = $radek["test"];
                $this->actions[$i]["date_input"] = $radek["date_input"];
                $this->actions[$i]["date_edit"] = $radek["date_edit"];
                $this->actions[$i]["report"] = $radek["report"];
                $this->actions[$i]["report_en"] = $radek["report_en"];
                $this->actions[$i]["fotogallery_url"] = $radek["fotogallery_url"];
                $this->actions[$i]["jmeno"] = $radek["jmeno"];
                $this->actions[$i]["prijmeni"] = $radek["prijmeni"];
                $this->actions[$i]["den"] = $radek["den"];
                $this->actions[$i]["perioda"] = $radek["perioda"];
                $this->actions[$i]["hodina"] = $radek["hodina"];
                $this->actions[$i]["prihlaska"] = $radek["prihlaska"];
                $i ++;
                // vypis
                if ( $radek["id_owner"] == $id_m) {
                    $zobraz_ikonky = true;
                }
                if (strlen($radek["date_pre"]) > 0){
                    $datum = "předběžné datum: ".$radek["date_pre"];
                } else {
                    $datum = $this->write_date($radek["date_start"], $radek["date_end"]);
                }
                $url = $radek["url"];
                
                /////////////////////////////
                /////////////////////////////
                //
                // template begining
                //
                /////////////////////////////
                /////////////////////////////
                if (isset($_GET['status']) && $_GET['status'] === 'success') {
                    echo "<p style='margin-bottom: 60px; color: #66bc29;'>Přihláška byla uložena.</p>";
                }
                $str .= "<h1>".$radek["nazev"]."</h1>";
                $str .= "<form action='reg_akce.php?&id_a={$radek['id_a']}' name='my_form' method='post'>";

                $str .= '<div class="flex">';
                $str .= '<aside>';
                $str .= "<h2>".$datum.", ".$radek["city"]." ".$radek["place"]."</h2>";
                if ($id_a <> 0){
                    $str .= nl2br($radek["perex"])."<br><hr>";
                    $str .= nl2br($radek["popis"])."<br>";
                } else {
                    $str .= nl2br($radek["perex"])."<br>";

                }

                if($radek["typ"] == "test"){
                    $str .= "Více informací na <a href='http://mensa.cz/testovani-iq/'>mensa.cz/testovani-iq/</a><br>";
                } else {
                    if (strlen($url)>0){
                        if ((substr($url, 0, 7) <> "http://")
                            and (substr($url, 0, 8) <> "https://")){
                            $url = "http://".$url;
                        }
                        $tmp_str = StrStr($url, "www.mensa.cz");
                        if ($tmp_str == false){
                            $target = " target=_blank";
                        } else {
                            $target = "";
                        }
                        $str .= "Více informací na <a href=".$url.$target.">{$url}</a><br>";
                    }
                }

                // jmeno organizatora
                $str .= "<br>".$radek["jmeno"]." ".$radek["prijmeni"];
                $email = $radek["email"];
                if (strlen($email)>6){
                    $str .= ", <a href=mailto:".$email.">".$email."</a>";
                }
                $tel = $radek["telefon"];
                if (strlen($tel)>6){
                    $str .= ", tel. ".$tel;
                }
                $str.= "<br><br>";



                if ($id_a == 0){
                    $str .= "<br><a href='https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a=".$radek["id_a"]."'>Zobrazit celou akci</a>";
                } else {
                    $str.= "<br>";
                }

                $str .= '</aside>';

                ////////////////////////////////
                ////////////////////////////////
                // vypis formular prihlasky
                ////////////////////////////////
                if ($id_a <> 0){
                    $SQL = "SELECT *, if (date_end > NOW(), 1, 0) AS enabled, if (date_view > NOW(), 1, 0) AS view_list FROM mc_akce_prihlasky WHERE id_a = ".$radek["id_a"];
                    $prih = $this->db->Query($SQL);
                    if ($this->db->getNumRows($prih) > 0){
                        $p = $this->db->FetchArray($prih);
                        $kapacita = $p["kapacita"];


                        GLOBAL $men;
                        if ($men <> "" && $p["enabled"] == 1 ){ // jsme v intranetu (promenna men je nastavena)
                            if($radek["typ"] == "test"){
                                $str .= "<br><strong><a href='https://intranet.mensa.cz/prihlaska/test_iq.php?&id_a=".$radek["id_a"]."' target='_blank'>Přihláška</a></strong><br>";
                            } else {
                                $str .= "<br><strong><a href='https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a=".$radek["id_a"]."' target='_blank'>Přihláška</a></strong><br>";
                            }
                        }
                        elseif ($p["enabled"] == 1){
                            $str .= '<main>';

                            // policko comment je trivialni captcha.
                            $captcha = new MCaptcha($radek["id_a"]);

                            $str .= "<input type='hidden' name='men' value='{$men}'>
                                    <input type='hidden' name='odeslat_m' value='odeslat'>
                                    {$captcha->print_label()}
                                    {$captcha->print_input()}";

                            if (strlen(trim($p["jmeno"]))>0){
                                $str .= "<label for='jmeno'>{$p["jmeno"]}:</label>";
                                $str .= "<input type='text' id='jmeno' name='jmeno' size='30' value='' required>";
                            }
                            if (strlen(trim($p["email"]))>0){
                                $str .= "<label for='email'>{$p["email"]}:</label>";
                                $str .= "<input type='text' id='email' name='email' size='30' value=''>";
                            }
                            if (strlen(trim($p["telefon"]))>0){
                                $str .= "<label for='telefon'>{$p["telefon"]}:</label>";
                                $str .= "<input type='text' id='telefon' name='telefon' size='30' value=''>";
                            }
                            if (strlen(trim($p["vek"]))>0){
                                $str .= "<label for='vek'>{$p["vek"]}:</label>";
                                $str .= "<input type='text' id='vek' name='vek' size='30' value=''>";
                            }
                            if (strlen(trim($p["datumnar"]))>0){
                                $str .= "<label for='datumnar'>{$p["datumnar"]}:</label>";
                                $str .= "<input type='text' id='datumnar' name='datumnar' size='30' value=''>";
                            }
                            if (strlen(trim($p["rc"]))>0){
                                $str .= "<label for='rc'>{$p["rc"]}:</label>";
                                $str .= "<input type='text' id='rc' name='rc' size='30' value=''>";
                            }
                            if (strlen(trim($p["cc"]))>0){
                                $str .= "<label for='cc'>{$p["cc"]}:</label>";
                                $str .= "<input type='text' id='cc' name='cc' size='30' value=''>";
                            }
                            if (strlen(trim($p["adresa"]))>0){
                                $str .= "<label for='adresa'>{$p["adresa"]}:</label>";
                                $str .= "<input type='text' id='adresa' name='adresa' size='30' value=''>";
                            }
                            if (strlen(trim($p["mesto"]))>0){
                                $str .= "<label for='mesto'>{$p["mesto"]}:</label>";
                                $str .= "<input type='text' id='mesto' name='mesto' size='30' value=''>";
                            }
                            if (strlen(trim($p["psc"]))>0){
                                $str .= "<label for='psc'>{$p["psc"]}:</label>";
                                $str .= "<input type='text' id='psc' name='psc' size='30' value=''>";
                            }
                            if (strlen(trim($p["variabilni"]))>0){
                                $str .= "<label for='variabilni'>{$p["variabilni"]}:</label>";
                                $str .= "<input type='text' id='variabilni' name='variabilni' size='30' value=''>";
                            }
                            if (strlen(trim($p["vol1"]))>0){
                                $str .= "<label for='vol1'>{$p["vol1"]}:</label>";
                                $str .= "<input type='text' id='vol1' name='vol1' size='30' value=''>";
                            }
                            if (strlen(trim($p["vol2"]))>0){
                                $str .= "<label for='vol2'>{$p["vol2"]}:</label>";
                                $str .= "<select id='vol2' name='vol2'><option value='Ano'>Ano</option><option value='Ne'>Ne</option></select>";
                            }
                            if (strlen(trim($p["vol3"]))>0){
                                $str .= "<label for='vol3'>{$p["vol3"]}:</label>";
                                $str .= "<select id='vol3' name='vol3'><option value='Ano'>Ano</option><option value='Ne'>Ne</option></select>";
                            }
                            if (strlen(trim($p["poznamka"]))>0){
                                $str .= "<label for='poznamka'>{$p["poznamka"]}:</label>";
                                $str .= "<textarea id='poznamka' name='poznamka' cols='30' rows='3'></textarea>";
                            }

                            $str .= "<p class='termsBox'>Přihlášením vstupujete do smluvního vztahu s Mensou Česko. Provedení služby s sebou nese nutnost zpracování a uchování výše uvedených osobních údajů. Zpracovatelem těchto údajů je Mensa Česko, IČ 45248591, Španielova 1111/19, 16300 Praha 6 - Řepy. ";
                            $str .= "Podrobné informace o ochraně a zpracování Vašich osobních údajů i o Vašich právech naleznete na adrese <a href='http://www.mensa.cz/gdpr'>www.mensa.cz/gdpr</a>. ";
                            $str .= "Vezměte prosím na vědomí, že na akcích je obvyklé pořizování fotografií, ty bývají umístěny ve veřejné fotogalerii Mensy Česko, Vaše jméno a příjmení může být rovněž zveřejněno ve veřejném seznamu přihlášených. ";
                            $str .= "Pokud nechcete uvést své jméno ve veřejném seznamu přihlášených, uveďte do jména a příjmení: 'účastník' a své jméno a příjmení uveďte do poznámky. ";
                            $str .= "Dle stanov Mensy Česko musí účastníci akcí respektovat pravidla stanovená organizátorem.</p>";
                            // GDPR - zakonny zastupce
                            $str .= "<p>Pro účely zpracování osobních údajů:</p>";

                            $str .= "<label class='flex'>
                                <input type='checkbox' id='ucastnik_nad16' name='ucastnik_nad16' value='ucastnik-je16' checked>
                                Je mi více než 15 let (již jsem oslavil/a 15. narozeniny)
                            </label>";

                            $str .= "<div class='zz-form-fields'>";
                            $str .= "<label for='jmeno_zz'>Jméno a příjmení zákonného zástupce:</label>";
                            $str .= "<input type='text' id='jmeno_zz' name='jmeno_zz' size='30' value=''>";
                            $str .= "</div>";

                            $str .= "<div class='zz-form-fields'>";
                            $str .= "<label for='email2'>E-mail zákonného zástupce:</label>";
                            $str .= "<input type='email' id='email2' name='email2' size='30' value=''>";
                            $str .= "<p style='text-align: right'><em>Tímto přihlašuji výše uvedenou osobu.</em></p>";
                            $str .= "</div>";
                            
                            // END GDPR
                            $str .= "<p class='submitBox'>
                                <input type='submit' value='Odeslat přihlášku' name='odeslat'>
                                </p>";

                            if ($p["view_list"]) {
                                // if we are logged in, show registered users also for past events, otherwise only new events.
                                $str .= "<br><br><br>";
                                $str .=  $this->get_event_attendee_list_html($radek['id_a'], $kapacita, ($_SESSION['login'] ?? false));
                            }

                            $str .= '</main>';
                        } else {
                            $str .= '<main>';
                            $str .='<strong>Přihlašování na tuto akci bylo již ukončeno.</strong>';
                            if ($p["view_list"]) {
                                // if we are logged in, show registered users also for past events, otherwise only new events.
                                $str .= "<br><br><br>";
                                $str .=  $this->get_event_attendee_list_html($radek['id_a'], $kapacita, ($_SESSION['login'] ?? false));
                            }
                            $str .= '</main>';

                        }
                    }
                }




                if ($zobraz_ikonky){
                    $str .= "<br>";
                    $str .= "<div align='right'>";
                    $str .= "<a href='index.php?men=men15.1.0.0&pg=upd&id_a=".$radek["id_a"]."'><img src='images/cav_ico_edit.gif' alt='Editovat akci' title='Editovat akci' width='18' height='18' border='0'></a>";
                    $str .= "<a href='index.php?men=men15.1.0.0&pg=del&id_a=".$radek["id_a"]."'><img src='images/cav_ico_delete.gif' width='18' height='18' alt='Smazat akci' title='Smazat  akci' border='0'></a>&nbsp;";
                    $str .= "</div>";
                }

                $str .= '</main>';
                $str .= '</div></form>';


                /////////////////////////////
                /////////////////////////////
                //
                // template get_action end
                //
                /////////////////////////////
                /////////////////////////////
            }
        }
        return $str;
    }

    function get_testovani($typ = ""){ // typ = test, diag, "" - oboje
        $WHERE = " WHERE date_start>=Now() AND ";
        if (strlen($typ)>0){
            $WHERE .= " typ LIKE $typ";
        } else {
            $WHERE .= " typ LIKE 'test' OR typ LIKE 'diag'";
        }

        $strSql = "SELECT a.*, m.jmeno, m.prijmeni FROM mc_akce AS a INNER JOIN m_members AS m ON a.id_owner=m.id_m AND m.disable='N' $WHERE ORDER BY a.date_start";

        $act = $this->db->Query($strSql);
        $str = "";
        if ($this->db->getNumRows($act) > 0 ) {

            $seznam=$db->FetchAssocAll($act);
            foreach ($seznam as $radek) {
                $datum = $this->write_date($radek["date_start"], $radek["date_end"]);
                $url = $radek["url"];
                $str .= "<h4>".$radek["nazev"]."</h4>
                        <b>".$datum.", ".$radek["city"]."</b><em> - ".$radek["place"]."</em><br>";
                $str .= $radek["popis"]."<br>";
                if (strlen($url)>0){
                    if ((substr($url, 0, 7) <> "http://")
                    and (substr($url, 0, 8) <> "https://")){
                        $url = "http://".$url;
                    }
                    $tmp_str = StrStr($url, "www.mensa.cz");
                    if ($tmp_str == false){
                        $target = " target=_blank";
                    } else {
                        $target = "";
                    }
                    $str .= "Více informací na <a href=".$url.$target.">{$url}</a><br>";
                }
                $str .= "Testující: ".$radek["jmeno"]." ".$radek["prijmeni"];
                $email = $radek["email"];
                if (strlen($email)>6){
                    $str .= ", <a href=mailto:".$email.">".$email."</a>";
                }
                $tel = $radek["telefon"];
                if (strlen($tel)>6){
                    $str .= ", tel. ".$tel;
                }
            }
        }
        return $str;
    }

    function get_action_day($month, $year){
        $ar = array();
        $start_date = date("Y-m-d 00:00:00" ,mktime(0,0,0, $month, -9, $year));
        $end_date = date("Y-m-d 00:00:00" ,mktime(0,0,0, $month, 40, $year));
        $strSql = "SELECT date_start, date_end, DATE_FORMAT(date_start, '%Y%m%d') as d, DATE_FORMAT(date_end, '%Y%m%d') as dd FROM mc_akce WHERE date_start>'$start_date' AND date_end<'$end_date' GROUP BY date_start, date_end ";
        $act = $this->db->Query($strSql);
        $poc = $this->db->getNumRows($act);
        while($poc--){
            $start = $this->db->getResult($act,$poc,"d");
            $end = $this->db->getResult($act,$poc,"dd");
            while($start<=$end){
                $ar[substr($start,0,4)."-".substr($start,4,2)."-".substr($start,6,2)]=true;
                $start++;
            }
        }

        return $ar;
    }


    function get_calendar_table($rok, $mesic, $locale="czech", $list=true, $s_day="", $s_week="", $s_month="", $url_action_view="", $template="blue", $table_width="172", $url_action=""){
        $rok = intval($rok);
        $mesic = intval($mesic);

        if ($url_action==""){
            $url_action = $url_action_view;
        }

        switch ($template){
            case "blue":
                $color1 = "#0075C0";	//border of table
                $color2 = "#C7DFF0";	//text background of table
                $color3 = "#0075C0";	//background of header
                $color4 = "#77B9E8";	//background of weekends
                $year_color = "#000000"; //color of header text
            break;
            case "blue_new":
                $color1 = "#185284";    //border of table
                $color2 = "#C7DFF0";	//text background of table
                $color3 = "#185284";    //background of header
                $color4 = "#77B9E8";	//background of weekends
                $year_color = "#ffffff"; //color of header text
            break;
            case "orange":
                $color1 = "#f7b44f";	//border of table
                $color2 = "#f4ffb9";	//text background of table
                $color3 = "#f7b44f";	//background of header
                $color4 = "#fde6c4";	//background of weekends
                $year_color = "#000000"; //color of header text
            break;
            case "caves":
                $color1 = "#6d1001";	//border of table
                $color2 = "#f9c450";	//text background of table
                $color3 = "#b26d28";	//background of header
                $color4 = "#f9c450";	//background of weekends
                $year_color = "#000000"; //color of header text
            break;
            default:
                $color1 = "#6600ff";	//border of table
                $color2 = "#e0d9fd";	//text background of table
                $color3 = "#6600ff";	//background of header
                $color4 = "#ccccff";	//background of weekends
                $year_color = "#000000"; //color of header text
        }

        $previous_month = $this->get_previous_month($mesic);
        $previous_year = $this->get_previous_year($rok, $previous_month);

        $next_month = $this->get_next_month($mesic);
        $next_year = $this->get_next_year($rok, $next_month);

        if ($mesic<1) {
            $rok = $rok - intval($mesic/12) - 1;
            $mesic = abs($mesic%12) +12;
        } else {
            $rok = $rok + intval($mesic/12);
            $mesic = abs($mesic%12);
        }
        $mesic_tmp = $mesic;

        if ($locale=="czech"){
            setlocale(LC_TIME, 'cs_CZ');
        } else {
            setlocale(LC_TIME, $locale);
        }

        $start_date = mktime(0,0,0, $mesic, 1, $rok);

        $ar = $this->get_action_day($mesic,$rok);

        ?>
    <table width="<?PHP echo $table_width ?>" bgcolor="<?PHP echo $color1;?>" border="0">
    <tr><td>
        <table width="<?PHP echo $table_width ?>" bgcolor="<?PHP echo $color2;?>" border="0" cellpadding="3" cellspacing="0">
            <tr bgcolor="<?PHP echo $color3;?>">
                <th align="right" class="SIPKA"><a href="<?PHP echo $url_action_view; ?>mesic=<?PHP echo $previous_month; ?>&rok=<?PHP echo $previous_year; ?>" class="SIPKA">&lt;</a></th>
                <th colspan="5" align="center" class="td_calendar"><center><a href="<?PHP echo $url_action_view; ?>mesic=<?PHP echo $mesic; ?>&rok=<?PHP echo $rok; ?>&s_month=<?PHP echo date("m", $start_date); ?>" class="SIPKA"><?PHP echo $this->mesic_1p(mb_convert_case(strftime($this->param["%B"], $start_date), MB_CASE_TITLE)); ?></a></center></th>
                <th align="right" class="SIPKA"><div align="right"><a href="<?PHP echo $url_action_view; ?>mesic=<?PHP echo $next_month; ?>&rok=<?PHP echo $next_year; ?>" class="SIPKA">&gt;</a></div></th>
                <th align="center"><center><font color="<?PHP echo $year_color; ?>"><?PHP echo date("Y", $start_date); ?></font></center></th>
            </tr>
        <?PHP
        $tmp1 = 1-(strftime($this->param["%u"],$start_date));
        $dni_v_mesici = strftime($this->param["%e"], mktime(0,0,0, $mesic+1,0,$rok));
        $first_date=date("Y-m-d 00:00:00", mktime(0,0,0,$mesic, $tmp1+1, $rok));

          if ((strlen($s_month)>0)) {  
            $s_header = $this->mesic_1p(mb_convert_case(strftime($this->param["%B %Y"], $start_date), MB_CASE_TITLE));
            $list_first_date = date("Y-m-d 00:00:00", mktime(0,0,0, date("m", $start_date), 1, date("Y", $start_date)) );
            $list_last_date = date("Y-m-d 23:59:00", mktime(0,0,0, date("m", $start_date)+1, 0, date("Y", $start_date)) );
          }

        while($mesic==$mesic_tmp) {
            echo"<tr>";
                $i=1;
                $tmp0 = mktime(0,0,0,$mesic, $tmp1+1, $rok);
                // day of the week
                while($i<8){
                  $tmp2 = mktime(0,0,0,$mesic, $tmp1+1, $rok);
                  //$tmp3 = strftime($this->param["%u"],$tmp2);
                  $tmp3 = strftime($this->param["%u"],$tmp2);
                  ($tmp1<0||$tmp1>=$dni_v_mesici)?($font="<Font color='#BBBBBB'>"):($font="");
                  ($tmp3==6||$tmp3==7)?($bgcolor=" bgcolor='$color4'>"):($bgcolor=">");
                  (isset($ar[date("Y-m-d",$tmp2)]))?$b=" style='font-weight: bold;'":$b="";
                  $last_date=date("Y-m-d 00:00:00", $tmp2);
                  if ((strlen($s_day)>0)&&($s_day==($tmp1+1))) {
                        $s_header =  iconv('ISO-8859-2', 'CP1250', strftime($this->param["%a: %e. %B %Y"], $tmp2));
                        $list_first_date = date("Y-m-d 00:00:00", $tmp2);
                        $list_last_date = date("Y-m-d 23:59:00", $tmp2);
                  }
            ?>
            <td class="td_calendar" align="right" <?PHP echo $b.$bgcolor."<a href='".$url_action_view."mesic=".$mesic."&rok=".$rok."&s_day=".($tmp1+1)."' class='DATE_CALENDAR'>".$font.intval(date("d",$tmp2)); if (strlen($font)>0) { echo "</font>";}  ; ?></a></td>
            <?PHP
                $tmp1++;
                $i++;
                }
                    // week of the year
                    $tmp4 = intval(strftime($this->param["%V"],$tmp0));
                    if (strlen($s_week)>0) {
                        if ($tmp4==$s_week){
                            $s_header = $tmp4.". týden ".date("Y", $tmp0);
                            $list_first_date=date("Y-m-d 00:00:00", mktime(0,0,0, date("m", $tmp2), date("d", $tmp2)-6, date("Y", $tmp2)));
                            $list_last_date=date("Y-m-d 23:59:00", $tmp2);
                        }
                    }

            ?>
            <td class="td_calendar" align="right"><a href="<?PHP echo $url_action_view; ?>mesic=<?PHP echo $mesic?>&rok=<?PHP echo $rok ?>&s_week=<?PHP echo $tmp4 ?>" class="DATE_CALENDAR"><small><?PHP echo $tmp4 ?>.týden</small></a></td>
        </tr>
        <?PHP
            $mesic_tmp = date("m", mktime(0,0,0, $mesic, $tmp1, $rok));
            if ($mesic>12) {
                $mesic_tmp = (intval($mesic/12)*12)+$mesic_tmp;
            } elseif($mesic<1) {
                $mesic_tmp = (intval($mesic/12)*12)-(12-$mesic_tmp);
            }
        }

        if ((strlen($s_day)==0)&&(strlen($s_week)==0)&&(strlen($s_month)==0) ){
            if( (date("Y")==date("Y", $start_date)) && (date("m")==date("m", $start_date)) ){
                // actual week
                                $s_header = intval(strftime($this->param["%V"],time())).". týden ".date("Y");
                $day_of_week =  iconv('ISO-8859-2', 'CP1250', strftime($this->param["%u"]));
                $list_first_date = date("Y-m-d 00:00:00", mktime(0,0,0, date("m"), date("d")-$day_of_week+1, date("Y")));
                $list_last_date = date("Y-m-d 23:59:00", mktime(0,0,0, date("m"), date("d")-$day_of_week+7, date("Y")));
            } else {
                // first week of month
                $s_header = intval(strftime($this->param["%V"],$start_date)).". týden ".date("Y",$start_date);
                $day_of_week =  iconv('ISO-8859-2', 'CP1250', strftime($this->param["%u"],$start_date));
                $list_first_date=date("Y-m-d 00:00:00", mktime(0,0,0, date("m", $start_date), date("d", $start_date)-$day_of_week+1, date("Y", $start_date)));
                $list_last_date=date("Y-m-d 23:59:00", mktime(0,0,0, date("m", $start_date), date("d", $start_date)-$day_of_week+7, date("Y", $start_date)));
            }
        }


        if ($list){
        ?>
            <tr bgcolor="<?PHP echo $color3; ?>">
                <th colspan="8" align="middle" class="td_calendar"><font color="<?PHP echo $year_color; ?>"><?PHP echo $s_header ?></font></th>
            </tr>
            </table><table width="<?PHP echo $table_width ?>" bgcolor="<?PHP echo $color2;?>" border="0" cellpadding="3" cellspacing="0" class="calendar">
            <?PHP
            $list = $this->get_action_list($list_first_date, $list_last_date);
            $poc = count($list);
            //print_r($list);
            while($poc--){
                if ($list[$poc]["date_start"]==$list[$poc]["date_end"]){
                    $text = $list[$poc]["date_start_full"].": ".$list[$poc]["nazev"];
                } else {
                    $text = $list[$poc]["date_start"]." - ".$list[$poc]["date_end"].": ".$list[$poc]["nazev"];
                }
                if (strlen($url_action_view)>0){
                    $a_start = "<a href=$url_action_view&id_a=".$list[$poc]["id_a"]." alt='$text' class='calendar'>";
                    $a_end = "</a>";
                }
            ?>
            <tr>
                <td valign="top" class="TEXT_CALENDAR" title="<?PHP echo $text ?>"><?PHP echo $list[$poc]["date_start"] ?></td>
                <td class="td_calendar" style="padding-left: 3px;" title="<?PHP echo $text ?>"><?PHP echo $a_start.$list[$poc]["nazev"].$a_end ?></td>
            </tr>
            <?PHP
            }
            ?>
        <?PHP
        }

        ?>
        </table>
    </td></tr>
    </table>
        <?PHP

    }

    /**
     * @param int $month
     * @return int
     */
    protected function get_previous_month($month)
    {
        $month = $this->normalize_month_1_to_12($month);
        $month = ($month - 1) % 12;

        return $this->normalize_month_1_to_12($month);
    }

    /**
     * @param $month
     * @return int
     */
    protected function get_next_month($month)
    {
        $month = $this->normalize_month_1_to_12($month);
        $month = ($month + 1) % 12;

        return $this->normalize_month_1_to_12($month);
    }

    /**
     * @param int $month
     * @return int
     */
    protected function normalize_month_1_to_12($month)
    {
        if ($month == 0) {
            $month = 12;
        }

        return $month;
    }

    /**
     * @param int $year
     * @param int $month
     * @return int
     */
    protected function get_previous_year($year, $month)
    {
        return $month == 12 ? $year - 1 : $year;
    }

    /**
     * @param int $year
     * @param int $month
     * @return int
     */
    protected function get_next_year($year, $month)
    {
        return $month == 1 ? $year + 1 : $year;
    }

    function generate_date($day_num, $week, $generovat_do, $hodina){ //day_num - den v tydnu kdy se kona akce
        //week / kodove oznaceni periody jak casto se akce kona
        // generovat_do  / kolik mesicu dopredu se ma akce generovat
        // hodina / v kolik hodin se akce kona
        if (strlen($hodina) == 0){
            $hodina = "00:00";
        }

        $dat = array();
        $month = date("n");
        $year = date("Y");
        if (substr($week, 0, 1)=="m"){ //jednou mesicne, x/ty tyden (1-4-ty, 5-posledni)
            for ($i = 0; $i<$generovat_do; $i++){
                $month = $month + 1;
                $week_num = intval(substr($week, 1, 2));
                $mesic_mtime = mktime(0,0,0, $month, 1, $year);
                if ($week_num < 5){ //1-4.ty tyden
                    $prvniho = date("w", $mesic_mtime);
                        if($prvniho  == 0){ //nedele
                            $prvniho == 7;
                        }
                    if ($day_num < $prvniho){
                        $day_month = $week_num * 7 - $prvniho + $day_num + 1;
                    } elseif($day_num >= $prvniho){
                        $day_month = $week_num * 7 - $prvniho + $day_num - 6;
                    }
                } else { // posledni tyden
                    $posledniho_den = date("t", $mesic_mtime);
                    $posledniho = date("w", mktime(0,0,0,$month, $posledniho_den, $year));
                    if($posledniho  == 0){ //nedele
                        $posledniho == 7;
                    }
                    if ($day_num <= $posledniho){
                        $day_month = $posledniho_den - $posledniho + $day_num;
                    } elseif($day_num > $posledniho){
                        $day_month = $posledniho_den - $posledniho + $day_num - 7;
                    }
                }
                if ($day_month < date("d") && $i == 0 && $month==date("m")){ // den, ktery generuju uz byl - posunu mesic o jedno dal a jedu cyklus znovu od  nuly
                    $month = $month + 1;
                    $i = -1;
                } else {
                    $dat[$i] = date("Y-m-d", mktime(0,0,0,$month, $day_month, $year))." ".$hodina.":00";
                }
            }
        } elseif (substr($week, 0, 1)=="w"){ //jednou mesicne, x/ty tyden (1-4-ty, 5-posledni)
            $mesic_mtime = mktime(0,0,0, $month, 1, $year);
            $prvniho = date("w", $mesic_mtime);
                if($prvniho  == 0){ //nedele
                    $prvniho == 7;
                }
            if ($day_num < $prvniho){
                $day_month = 7 - $prvniho + $day_num + 1;
            } elseif($day_num >= $prvniho){
                $day_month =  1 - $prvniho + $day_num ;
            }
            while($day_month < date("d")){ // den, ktery generuju uz byl
                $day_month = $day_month + 7;
            }

            // day_month / den v mesici, kdy se kona prvni akce - resp. prvni tyden v mesici, kdy se muze konat a ktery jeste nebyl
            $perioda = substr($week, 1, 2);
            if ($perioda == "0"){
                $t = 7;
                $do = 4*$generovat_do;
            } else { // 1, 2 - kazdy sudy nebo lichy tyden
                $t = 14;
                $do = 2*$generovat_do;
                $day_mtime = mktime(0,0,0,$month, $day_month, $year);
                $W = date("W", $day_mtime);
                $lichy = $W % 2;
                if ($lichy != intval($perioda % 2)){ // lichy =1, $perioda  = 2,NEBO  lichy =0, $perioda=1
                    $day_month += 7;
                }

            }

            for ($i = 0; $i<$do; $i++){
                $mtime = mktime(0,0,0,$month, $day_month, $year);
                $dat[$i] = date("Y-m-d", $mtime)." ".$hodina.":00";
                $day_month += $t;
            }
        }
        return $dat;
    }

    function edit_item($arr_item){
        $ret = "";
        $id_a 		= intval($arr_item["id_a"]);  // vrátí 0 pokud se hodnota nedá naprsovat
        $typ 		= intval($arr_item["typ"]);
        $date_start = trim($arr_item["date_start"]);
        $date_end 	= trim($arr_item["date_end"]);
        $date_pre 	= trim($arr_item["date_pre"]);
        $nazev 		= trim($arr_item["nazev"]);
        $perex 		= trim($arr_item["perex"]);
        $popis 		= trim($arr_item["popis"]);
        $nazev_en 	= trim($arr_item["nazev_en"]);
        $popis_en 	= trim($arr_item["popis_en"]);
        $place 		= trim($arr_item["place"]);
        $city 		= trim($arr_item["city"]);
        $id_area 	= $arr_item["id_area"];
        $area_name 	= trim($arr_item["area_name"]);
        $url 		= trim($arr_item["url"]);
        $id_owner 	= $arr_item["id_owner"];
        $id_editor 	= $arr_item["id_editor"];
        $telefon 	= $arr_item["telefon"];
        $email	 	= $arr_item["email"];
        $typ 		= $arr_item["typ"];
        $ms 		= $arr_item["ms"];
        $sig	 	= $arr_item["sig"];
        $dm 		= $arr_item["dm"];
        $mensa	 	= $arr_item["mensa"];
        $test	 	= $arr_item["test"];
        $termin	 	= intval($arr_item["termin"]);
        $den	 	= $arr_item["den"];
        $perioda	= $arr_item["perioda"];
        $hodina	 	= $arr_item["hodina"];
        $generovat_do = $arr_item["generovat_do"];

        // pošli notifikaci člověku zodpovědnému za aktivity
        $mailer = new mailer\Mailer();
        $a = $mailer->sendGlobalMail(
            __FILE__ . ':' . __LINE__,
            "<EMAIL>",
            "Aktualizace mensovního kalendáře: {$arr_item['nazev']}",
            nl2br("Název: " . $arr_item["nazev"] . "\nMěsto: " . $arr_item["city"] . "\nZačátek: " . $arr_item["date_start"] .
             "\nPerex: " . $arr_item["perex"] . "\n\n\n" . print_r($arr_item, 1)),
            '', "-r <EMAIL>"
        );
        // nezobrazi se - protoze dojde k presmerovani
        //print("Odeslani oznameni probehlo s vysledkem '{$a}'.");

        // 0 - jednorazova akce, 1-pravidelna akce jen aktualni termin, 2-pravidelna akce vsechny terminy
        if ($termin == 1 || $termin == 0){

            if ($id_a == 0)
            {   // nová akce, id není zadáno - viz výše, funkce pro konverzi zkonvertuje chybějící pole na 0
                $SQL = "INSERT INTO mc_akce(date_start, date_end, date_pre,  nazev, perex,  popis, nazev_en, popis_en, place, city, area_name, url, id_owner, id_editor, telefon, email, date_input, typ, ms, sig, dm, mensa, test)
                        VALUES ('$date_start', '$date_end','$date_pre',  '$nazev', '$perex', '$popis', '$nazev_en', '$popis_en', '$place', '$city', '$area_name', '$url', '$id_owner','$id_editor', '$telefon', '$email', Now(), '$typ', '$ms', '$sig', '$dm', '$mensa', '$test')";
                $this->db->Query($SQL);
                $id_a = $this->db->getInsertId();
                if (is_array($id_area)){
                    foreach ($id_area as $key => $val) {
                        $SQL = "INSERT INTO mc_akce2kraje (id_a, id_area) VALUES ('$id_a', '$val')";
                        $this->db->Query($SQL);
                    }
                }

            } else
            {       // tady se meni existujici akce
                    // 2017-02-03, TK: doplnena aktualizace sloupce $test.
                    $SQL = "UPDATE mc_akce SET
                            date_start = '$date_start',
                            date_end = '$date_end',
                            date_pre = '$date_pre',
                            nazev = '$nazev',
                            perex = '$perex',
                            popis = '$popis',
                            nazev_en = '$nazev_en',
                            popis_en = '$popis_en',
                            place = '$place',
                            city = '$city',
                            telefon = '$telefon',
                            email = '$email',
                            area_name = '$area_name',
                            url = '$url',
                            id_editor = '$id_editor',
                            typ = '$typ',
                            ms = '$ms',
                            sig = '$sig',
                            dm = '$dm',
                            mensa = '$mensa',
                            date_edit = NOW(),
                            test = '{$test}'
                      WHERE id_a = {$id_a}";
                    $this->db->Query($SQL);
                    print_r($SQL);


                    $SQL = "DELETE FROM mc_akce2kraje WHERE id_a = $id_a";
                    $this->db->Query($SQL);
                    if (is_array($id_area)){
                        foreach ($id_area as $key => $val) {
                            $SQL = "INSERT INTO mc_akce2kraje (id_a, id_area) VALUES ('$id_a', '$val')";
                            $this->db->Query($SQL);
                        }
                    }

            }


        } elseif($termin == 2) { //pravidelna

            // nove zdani pravidelne akce, vsechny terminy
            if ($id_a == 0){
                    $date_arr = $this->generate_date($den, $perioda, $generovat_do, $hodina);
                    $id_parent = -1;
                    if (is_array($date_arr)){
                        foreach ($date_arr as $key => $val) {
                            $date_start = $val;
                            $date_end = $val;
                            $SQL = "INSERT INTO mc_akce(id_parent, date_start, date_end, date_pre,   nazev,perex,  popis, nazev_en, popis_en, place, city, area_name, url, email, telefon, id_owner, id_editor,date_input, typ, ms, sig, dm, mensa, test)
                                    VALUES ($id_parent, '$date_start', '$date_end', '$date_pre', '$nazev', '$perex', '$popis', '$nazev_en', '$popis_en', '$place', '$city', '$area_name', '$url', '$email', '$telefon', '$id_owner', '$id_editor', Now(), '$typ', '$ms', '$sig', '$dm','$mensa', '$test')";
                            $this->db->Query($SQL);
                            $id_a = $this->db->getInsertId();
                            if ($id_parent == -1){ //provede se pouze u prvniho zapisu pravidelne akce, ostatni pak uz na ni odkazuji
                                $id_parent = $id_a;
                                $SQL = "UPDATE mc_akce SET id_parent = $id_parent WHERE id_a = $id_a";
                                $this->db->Query($SQL);
                            }
                            if (is_array($id_area)){
                                foreach ($id_area as $key => $val) {
                                    $SQL = "INSERT INTO mc_akce2kraje (id_a, id_area) VALUES ('$id_a', '$val')";
                                    $this->db->Query($SQL);
                                }
                            }
                        } // foreach
                    } //if (is_array($date_arr)){
            } else {
                // edit pravidelne akce vsechny terminy
                // $date_start = $this->write_date_sql($date_start);
                // $date_end = $this->write_date_sql($date_end);
                $SQL = "UPDATE mc_akce SET
                            date_start = '$date_start',
                            date_end = '$date_end',
                            date_pre = '$date_pre',
                            nazev = '$nazev',
                            perex = '$perex',
                            popis = '$popis',
                            nazev_en = '$nazev_en',
                            popis_en = '$popis_en',
                            place = '$place',
                            city = '$city',
                            telefon = '$telefon',
                            email = '$email',
                            area_name = '$area_name',
                            url = '$url',
                            id_editor = '$id_editor',
                            typ = '$typ',
                            ms = '$ms',
                            sig = '$sig',
                            dm = '$dm',
                            mensa = '$mensa',
                            date_edit = Now()
                  WHERE id_a =".$id_a;
                $this->db->Query($SQL);
                $SQL = "DELETE FROM mc_akce2kraje WHERE id_a = $id_a";
                $this->db->Query($SQL);
                if (is_array($id_area)){
                    foreach ($id_area as $key => $val) {
                        $SQL = "INSERT INTO mc_akce2kraje (id_a, id_area) VALUES ('$id_a', '$val')";
                        $this->db->Query($SQL);
                    }
                }
            }//if ($id_a == 0){
        }
        return $id_a;
    }


    function del_item($id_a, $id_parent){
        if ($id_parent > 0){ //pravidelna akce
            $SQL = "DELETE FROM mc_akce WHERE id_parent = $id_parent AND date_start > Now();";
            $this->db->Query($SQL);
        } elseif ($id_a > 0){ //jednorazova akce
            $SQL = "DELETE FROM mc_akce WHERE id_a = $id_a";
            $this->db->Query($SQL);
        }
    }



    function write_date($date_start, $date_end, $format=""){ //2005-09-28 12:21:20
        $y1 = substr($date_start, 0, 4);
        $m1 = substr($date_start, 5, 2);
        $d1 = substr($date_start, 8, 2);
        $h1 = substr($date_start, 11, 2);
        $min1 = substr($date_start, 14, 2);
        $sec1 = substr($date_start, 17, 2);

        $y2 = substr($date_end, 0, 4);
        $m2 = substr($date_end, 5, 2);
        $d2 = substr($date_end, 8, 2);
        $h2 = substr($date_end, 11, 2);
        $min2 = substr($date_end, 14, 2);
        $sec2 = substr($date_end, 17, 2);

        switch ($format){
            case "full":
                $ret = $d1.".".$m1.".".$y1." ".$h1.":".$min1.":".$sec1;
            break;
            case "date":
                $ret = $d1.".".$m1.".".$y1;
            break;
            case "ddmm":
                $ret = $d1.".".$m1.".";
            break;
            case "time":
                $ret = $h1.":".$min1;
            break;
            case "hhmm":
                $ret = $h1.":".$min1;
            break;
            case "sql":
                $ret = $m1."/".$d1."/".$y1." ".$h1.":".$min1.":".$sec1;
            break;
            default:	//case "":
                if ($y1 == $y2){
                    if ($m1 == $m2){
                        if ($d1 == $d2){
                            if ($h1=="0" && $min1 =="0" && $h2=="0" && $min2 =="0"){
                                $ret = $d1.".".$m1.".".$y1;
                            } else {
                                if ($h1 == $h2 && $min1 == $min2){
                                    $ret = $d1.".".$m1.".".$y1." ".$h1.":".$min1;
                                } else {
                                    $ret = $d1.".".$m1.".".$y1." ".$h1.":".$min1." - ".$h2.":".$min2;
                                }
                            }
                        } else { // dva ruzne dny
                            if ($h1=="0" && $min1 =="0" && $h2=="0" && $min2 =="0"){
                                $ret = $d1.".-".$d2.". ".$m1.".".$y1;
                            } else {
                                $ret = $d1.".".$m1.".".$y1." ".$h1.":".$min1." - ".$d2.".".$m2.".".$y2." ".$h2.":".$min2;
                            }
                        }
                    } else { //dva ruzne mesice
                        if ($h1=="0" && $min1 =="0" && $h2=="0" && $min2 =="0"){
                            $ret = $d1.".".$m1.".-".$d2.". ".$m2.".".$y1;
                        } else {
                            $ret = $d1.".".$m1.".".$y1." ".$h1.":".$min1." - ".$d2.".".$m2.".".$y2." ".$h2.":".$min2;
                        }
                    }
                } else { //dva ruzne roky
                    if ($h1=="0" && $min1 =="0" && $h2=="0" && $min2 =="0"){
                        $ret = $d1.".".$m1.".".$y1."-".$d2.". ".$m2.".".$y2;
                    } else {
                        $ret = $d1.".".$m1.".".$y1." ".$h1.":".$min1." - ".$d2.".".$m2.".".$y2." ".$h2.":".$min2;
                    }
                }
            break;
        }
        return $ret;
    }

    // **********************************************************************************************
    //funkce vrati string zformatovaneho datumu z input boxu do SQL formatu   v nekolika typech formatu
    function write_date_sql($datum, $format = "sql_result"){ //01.01.2005 12:21:20
        $tmp1 = explode(" ", $datum);
        $datum1 = $tmp1[0];
        @$cas = $tmp1[1];
        $tmp = explode(".", $datum1);
        $d = (int) $tmp[0];
        @$m = (int) $tmp[1];
        @$y = (int) $tmp[2];

        $tmp = explode(":", $cas);
        @$h = (int) $tmp[0];
        @$min = (int) $tmp[1];
        @$sec = (int) $tmp[2];

        if (intval($y) <2005){
            return "";
        } else {
            $mtime = mktime ($h, $min, $sec, $m, $d, $y ) ;
        }

        switch ($format){
            case "date":
                $ret =  date ("j.n.Y", $mtime);
            break;
            case "time":
                $ret =  date ("H:i:s", $mtime);
            break;
            case "hhmm":
                $ret =  date ("H:i", $mtime);
            break;
            case "full":
                $ret =  date ("j.n.Y H:i:s", $mtime);
            break;
            case "sql_result":
                $ret =  date ("Y-m-d H:i:s", $mtime);
            break;
            case "timestamp":
                $ret =  $mtime;
            break;
            default:
                $ret =  date ("j.n.Y H:i:s", $mtime);
            break;
        }
        return $ret;
    }
}

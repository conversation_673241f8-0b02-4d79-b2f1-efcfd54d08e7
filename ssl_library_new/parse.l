<?PHP
function change_month($number){
	switch ($number){
		case "01":
			return "leden";
		case "02":
			return "únor";
		case "03":
			return "březen";
		case "04":
			return "duben";
		case "05":
			return "kv<PERSON>ten";
		case "06":
			return "červen";
		case "07":
			return "červenec";
		case "08":
			return "srpen";
		case "09":
			return "zář<PERSON>";
		case "10":
			return "říjen";
		case "11":
			return "listopad";
		case "12":
			return "prosinec";
		default:
			return "nesprávný mě<PERSON>íc";
	}
}

function parse($file){

// i10-01.zip

	$rok = " 19";
	//if (substr($file,1,1)=="0"){
	if (intval(substr($file,1,1))<8){
		$rok = " 20";
	}
	switch (strpos($file,".")){
	case "6":
		return change_month(substr($file,4,2)).$rok.substr($file,1,2);
	case "8":
		return change_month(substr($file,4,2))." - ".change_month(substr($file,6,2)).$rok.substr($file,1,2);
	default:
		return "špatná verze souboru";
	}

}

?>
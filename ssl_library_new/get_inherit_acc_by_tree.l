<?PHP
/*
utf8
	funkce vrati pole dedenych prav.
*/
function get_inherit_acc_by_tree($men, $db){
	
	preg_match( "/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts );
	$s2= $parts[1];
	$s3= $parts[2];
	$s4= $parts[3];
	$s5= $parts[4];

	//echo "Prava uzivatele $l na $s2.$s3.$s4.$s5"; ($l=id_m
	$SEL = "
	SELECT 
		DISTINCT p.permission_id,
		CONCAT(c.s1, '.', c.s2, '.', c.s3, '.', c.s4) as level,
		IF (p.role_id=-1, 'Individuální', ro.role_descr) as role,
		p.role_id, p.value, p.permission_id, p.id_m
	FROM 
		m_acc_permission p,
		m_acc_uid2role u2r, 
		m_acc_class c,
		m_acc_role ro
	WHERE
			c.class_id=p.class_id AND
			(
				p.role_id=ro.role_id OR
				p.role_id=-1
			)AND
			(
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=$s5 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=$s4 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=$s3 and c.s3=0 and c.s4=0 ) OR
				(c.s1=$s2 and c.s2=0 and c.s3=0 and c.s4=0 ) OR
				(c.s1=0 and c.s2=0 and c.s3=0 and c.s4=0 )
			)
	 ORDER BY
	      c.s1 desc, c.s2 desc, c.s3 desc, c.s4 desc, 
		  p.id_m desc, p.value asc, p.role_id desc";

/*
echo "<br>******************************<br>";
echo $SEL;
echo "<br>******************************<br>";
*/
		$tmp = $db->Query($SEL);
		$i =  $db->getNumRows($tmp);

		while($i--){
		  	 $a_acc[$i]["level"] =  $db->getResult($tmp, $i,"level");
			 $a_acc[$i]["role"] = $db->getResult($tmp, $i,"role");
			 $a_acc[$i]["value"] = $db->getResult($tmp, $i,"value");
			 $a_acc[$i]["role_id"] = $db->getResult($tmp, $i,"role_id");
			 $a_acc[$i]["id_p"] = $db->getResult($tmp, $i,"permission_id");
			 $a_acc[$i]["id_m"] = $db->getResult($tmp, $i,"id_m");
		}
		return $a_acc;
}
?>

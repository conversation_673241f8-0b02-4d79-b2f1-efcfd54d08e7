<?php
function show_obory(&$db, $bNeuvedeno = true){
	$sSEL = ($bNeuvedeno) ?
			"SELECT * FROM m_list_obory ORDER BY obor DESC" :
			"SELECT * FROM m_list_obory " .
			"WHERE ((obor NOT LIKE '%neuveden%') AND " .
				   "(obor NOT LIKE '%ostatní%') AND " .
				   "(obor NOT LIKE '%nezam%') AND " .
				   "(obor NOT LIKE '%dovolená%'))" .
			"ORDER BY obor DESC";
	$tmp = $db->Query($sSEL);

	$i = $db->getNumRows($tmp);
	while($i--){
		$a_obory[$i]["id"] = $db->getResult($tmp, $i, "m_id_obor");
		$a_obory[$i]["obor"] = $db->getResult($tmp, $i, "obor");
	}

	return $a_obory;
}
?>

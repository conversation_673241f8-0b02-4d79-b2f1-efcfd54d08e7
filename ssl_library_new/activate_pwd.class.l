<?PHP

/**********************************************************************
 * AUTHOR:    (c) Roman Brzuska, <EMAIL>
 * CREATED:   2004
 * PROJECT:   INTRANET, password library
 * DESC
 * Seccondary object
 * /DESC
 * HISTORYLIST
 * 2018-01-22 MPS Změna odesílatele zprávy (From, Reply-to) z '<EMAIL>' na '<EMAIL>','<EMAIL>'
 * 2025-03-12 Sendgrid Mailer
 * /HISTORYLIST
 **********************************************************************/

/**********************************************************************
 * References:
 ***********************************************************************/
class activate_pwd
{

    /**
     * @param $db
     * @param $email
     * @param $pw
     * @param $strError
     * @return bool
     */
    function check_email(&$db, $email, $pw, &$strError)
    {

        $SEL = "SELECT id_m, email FROM m_members WHERE email like '$email'";
        $tmp = $db->Query($SEL);

        if (intval($db->getNumRows($tmp)) === 0) {
            $strError = "Účet s uvedeným emailem neexistuje. Pro pomoc se změnou emailu napiš<NAME_EMAIL>.";
            return false;
        } elseif (intval($db->getNumRows($tmp)) === 1) {

            $ID_UNIQ = uniqid("", 50);
            $this->put_pwd_confirm($db, $pw, $ID_UNIQ, $db->getResult($tmp, 0, "id_m"));

            $body = "Prosím, pro potvrzení nového hlesla klikněte na náledující odkaz:
https://intranet.mensa.cz/show.php?pg=lost_pwd&t=change&id=$ID_UNIQ
ihned poté dojde k aktivaci nového hesla, a můžete jej použít.

Pokud jste požadavek na zmeěu hesla neiniciovali, neklikejte na tento link a zprávu ignorujte.
V případě opakování, prosím, kontaktujte <EMAIL>.

S pozdravem,
Mensa Česko
";

            $header  = "From: =?UTF-8?B?" . base64_encode("Intranet Mensy Česko") . "?=<<EMAIL>>\n";
            $header .= 'Return-Path: <EMAIL>\n';
            $header .= 'Content-Type: text/plain;charset=\"utf-8\"\n';
            $header .= 'Content-Transfer-Encoding: 8bit\n';
            $header .= 'MIME-Version: 1.0\n';
            $header .= 'X-Mailer: PHP/Mensaweb\n';

            $mailer = new mailer\Mailer();
            $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $email, "=?utf-8?B?" . base64_encode("Změna hesla do intranetu Mensy Česko") . "?=", $body, $header);

            return true;

        } else {
            $strError = "Účtů s uvedeným e-mailem existuje více, nelze změnit heslo. Pro pomoc se změnou e-mailu napiš<NAME_EMAIL>";
            return false;
        }
    }

    function activate(&$db, $PwdKey, &$strError)
    {
        $strError = "";
        $delete_time = date("YmdHi", mktime(date("G") - 12, date("i"), 0, date("m"), date("d"), date("Y")));

        $SEL = "DELETE FROM change_pwd WHERE Date<$delete_time";
        $tmp = $db->Query($SEL);

        $SEL = "SELECT * FROM change_pwd WHERE PwdKey='$PwdKey'";
        $tmp = $db->Query($SEL);
        if (intval($db->getNumRows($tmp)) === 1) {
            $id_m = $db->getResult($tmp, 0, "id_member");
            $spwd = $db->getResult($tmp, 0, "pwd");
            $SEL = "UPDATE m_members SET heslo='$spwd' WHERE id_m=$id_m";
            $tmp = $db->Query($SEL);

            $SEL = "DELETE FROM change_pwd WHERE PwdKey='$PwdKey'";
            $tmp = $db->Query($SEL);
            $strError = @$email;
            return true;
        } else {
            $strError = "Zadaný klíč neexistuje nebo mu již skončila expirační doba.";
            return false;
        }
    }

    function put_pwd_confirm(&$db, $new_pwd, $PwdKey, $id_m)
    {
        $now = date("YmdHi");
        $delete_time = date("YmdHi", mktime(date("G") - 12, date("i"), 0, date("m"), date("d"), date("Y")));

        $SEL = "DELETE FROM change_pwd WHERE id_member=$id_m OR Date<$delete_time";
        $db->Query($SEL);

        $SEL = "INSERT INTO change_pwd VALUES('', old_password('$new_pwd'), '$PwdKey', $now, $id_m)";
        $db->Query($SEL);

    }
}

<?PHP
function show_order(&$db,$filtr, $start, $krok, $id_o=0){

	switch ($filtr){
		case "0":
			$SEL="SELECT o.*, m.jmeno, m.prijmeni FROM m_order as o INNER JOIN m_members AS m ON o.id_user=m.id_m ORDER BY o.date desc LIMIT $start, $krok";
		break;
		case "1": //vyrizene
			$SEL="SELECT o.*, m.jmeno, m.prijmeni FROM m_order as o INNER JOIN m_members AS m ON o.id_user=m.id_m WHERE  o.vyrizeno<>'0' ORDER BY o.date desc LIMIT $start, $krok";
		break;
		case "2": // nevyrizene
			$SEL="SELECT o.*, m.jmeno, m.prijmeni FROM m_order as o INNER JOIN m_members AS m ON o.id_user=m.id_m WHERE o.vyrizeno='0' ORDER BY o.date desc LIMIT $start, $krok";
		break;
		case "3": // ID_o = $id_o
			$SEL="SELECT o.*, m.jmeno, m.prijmeni FROM m_order as o INNER JOIN m_members AS m ON o.id_user=m.id_m WHERE o.id_order=$id_o ORDER BY o.date desc LIMIT $start, $krok";
		break;
	}

	$tmp = $db->Query($SEL);

	$i=$db->getNumRows($tmp);
	while($i--){
		$a_ret[$i]["id_order"]= $db->getResult($tmp, $i, "id_order");
		$a_ret[$i]["what_order"]= $db->getResult($tmp, $i, "what_order");
		$a_ret[$i]["id_user"]= $db->getResult($tmp, $i, "id_user");
		$a_ret[$i]["date"]= $db->getResult($tmp, $i, "date");
		$a_ret[$i]["clen_cislo"]= $db->getResult($tmp, $i, "clen_cislo");
		$a_ret[$i]["vyrizeno"]= $db->getResult($tmp, $i, "vyrizeno");
		$a_ret[$i]["vyrizeno_by_clen_cislo"]= $db->getResult($tmp, $i, "vyrizeno_by_clen_cislo");
		$a_ret[$i]["jmeno"]= $db->getResult($tmp, $i, "jmeno");
		$a_ret[$i]["prijmeni"]= $db->getResult($tmp, $i, "prijmeni");
	}

	return $a_ret;

}
?>
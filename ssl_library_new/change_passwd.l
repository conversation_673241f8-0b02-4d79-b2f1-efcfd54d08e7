<?PHP
function change_passwd(&$db, $id_m, $heslo, $new_heslo){

	$SEL="select clen_cislo from m_members where id_m=$id_m AND heslo=old_password('$heslo')";

	$tmp = $db->Query($SEL);
	$i = $db->getNumRows($tmp);

	if($i==1){
		$datum=Date("Y-m-d");
		$UPD="update m_members set heslo=old_password('$new_heslo'), date='$datum' WHERE id_m=$id_m AND heslo=old_password('$heslo')";

		if($db->Query($UPD)){
			return "1"; // v poradku
		} else {
			return "2"; // oprava neprobehla
		}
	} else {
		return "0"; //spatne heslo
	}
}
?>
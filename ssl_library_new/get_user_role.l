<?PHP
/* 
utfá
	Funkce vraci role(skupiny), v nichz je uzivatel zapsan.
*/
function get_user_role(&$db, $id_m){
	
		$a_role = array();
	
		$SEL = "SELECT * FROM m_acc_uid2role, m_acc_role WHERE m_acc_uid2role.id_m=$id_m AND m_acc_uid2role.role_id=m_acc_role.role_id ORDER BY role_descr desc";
		$tmp = $db->Query($SEL);
		$i =  $db->getNumRows($tmp);

		while($i--){
		  	 $a_role[$i]["role_id"] =  $db->getResult($tmp, $i,"role_id");
			 $a_role[$i]["role_descr"] = $db->getResult($tmp, $i,"role_descr");
			 $a_role[$i]["uid2role_id"] = $db->getResult($tmp, $i,"uid2role_id");
		}
		return $a_role;
}
?>
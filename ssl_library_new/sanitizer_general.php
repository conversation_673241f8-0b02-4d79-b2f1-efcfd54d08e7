<?PHP

/**
    Sanitizer
    
    Provede př<PERSON> vyčitěn<PERSON> v<PERSON>ech vstupních proměnných.
    
    Původní kód je převzazt z: 
        http://php.net/manual/en/security.magicquotes.php
    
    
    Změnovník:
        2012-04-23, TK: Založeno

*/


function super_sanitize_post_and_get ()
{

    // sanitization function
    function sanitizeVariables(&$item, $key)
    {
        if (!is_array($item))
        {
            $item = sanitizeText($item);
        }
    }
    

    // does the actual 'html' and 'sql' sanitization. customize if you want.
    function sanitizeText($text)
    {
        global $db;
        $text = str_replace("<", "&lt;", $text);
        $text = str_replace(">", "&gt;", $text);
        $text = str_replace("\"", "&quot;", $text);
        $text = str_replace("'", "&#039;", $text);
       
        return $db->getEscapedString($text);    
    }


    // escaping and slashing all POST and GET variables.
    array_walk_recursive($_POST, 'sanitizeVariables');
    array_walk_recursive($_GET,  'sanitizeVariables');
    
    
    // promítne změny i do globálních proměnných (register_globals = On)
    // které se bohužel v intranetu velice intenzivně používají 
    
    foreach (array_keys($_POST) as $ehsanKey)
        $GLOBALS[$ehsanKey] = $_POST[$ehsanKey];
    
    foreach (array_keys($_GET) as $ehsanKey)
        $GLOBALS[$ehsanKey] = $_GET[$ehsanKey];

    // hotovo
}

?>

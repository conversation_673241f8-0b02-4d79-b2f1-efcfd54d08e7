<?PHP
function order_state(&$db){

	$SEL="SELECT Count(id_order) as pocet FROM m_order WHERE vyrizeno='0'";
	$tmp = $db->Query($SEL);
	if($db->getNumRows($tmp)==1){
		$a_ret["nevyrizeno"]=$db->getResult($tmp, 0, "pocet");
			$SEL="SELECT Count(id_order) as pocet FROM m_order WHERE vyrizeno<>'0'";
			$tmp = $db->Query($SEL);
			if($db->getNumRows($tmp)==1){
				$a_ret["vyrizeno"]=$db->getResult($tmp, 0, "pocet");
				$a_ret["celkem"] = $a_ret["vyrizeno"] + $a_ret["nevyrizeno"];
				return $a_ret;
			} else {
				return false;
			}
		return $a_ret;
	} else {
		return false;
	}
}
?>
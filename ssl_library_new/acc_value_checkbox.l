<?PHP
/*

Funkce primo zobrazuje tabulku s hodnotami.
*/
function acc_value_checkbox($value){
/*
1 - pr<PERSON><PERSON> (UPDATE) 
2 - právo vytvoření nového z<PERSON>znamu (CREATE) 
4 - pr<PERSON><PERSON> (READ) 
8 - právo přidělovat práva dalším (GRANT)-(nastavení poístupu jiným uživatelum) 
16 - právo přidělit právo GRANT2 (pravo nastavit dalšímu uživateli právo GRANT) 
32 - právo vyvoření nové třídy v daném podstromu 
64 - právo mazání záznam¨ (DELETE) 
*/

If(! ((($value & 64)>0)OR(($value & 32)>0)OR(($value & 16)>0)OR(($value & 8)>0)OR(($value & 4)>0)OR(($value & 2)>0)OR(($value & 1)>0) )){ echo "zákaz"; 
} else {
	If(($value & 1)>0){ echo '<img src="images/acc_u.gif" width="12" height="12" border="0" alt="Aktualizovat">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';};
	If(($value & 2)>0){ echo '<img src="images/acc_c.gif" width="12" height="12" border="0" alt="Vytvářet">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';};
	If(($value & 4)>0){ echo '<img src="images/acc_r.gif" width="12" height="12" border="0" alt="Číst">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';};
	If(($value & 8)>0){ echo '<img src="images/acc_g1.gif" width="12" height="12" border="0" alt="Grant 1">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';};
	If(($value & 16)>0){ echo '<img src="images/acc_g2.gif" width="12" height="12" border="0" alt="Free">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';};
	If(($value & 32)>0){ echo '<img src="images/acc_t.gif" width="12" height="12" border="0" alt="Třída">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';}; 
	If(($value & 64)>0){ echo '<img src="images/acc_d.gif" width="12" height="12" border="0" alt="Mazat">'; } else { echo '<img src="images/acc.gif" width="12" height="12" border="0" alt="">';};
};
 
}
?>
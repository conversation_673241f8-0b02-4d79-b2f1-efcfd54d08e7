<?PHP
/* 
utf8
	Funkce vraci pole roli.
	Jestlize je vyplneno UID, funkce vraci seznam roli mimo role, ktere jiz dany uzivatel ma.
*/
function show_role(&$a_role, &$db, $id_m=0){

	If (Empty($id_m)){
			$SEL = "SELECT * FROM m_acc_role WHERE role_id!=-1 ORDER BY role_descr desc";
	} else {
		$SEL1="SELECT role_id FROM m_acc_uid2role WHERE id_m=$id_m";
		$tmp = $db->Query($SEL1);
		$i =  $db->getNumRows($tmp);
		If($i!=0){
			$string="";
			while($i--){
			  	 $string = $string." AND role_id!=".$db->getResult($tmp, $i,"role_id");
			}
			$SEL = "SELECT * FROM m_acc_role WHERE role_id!=-1 $string ORDER BY role_descr desc";
		} else {
			$SEL = "SELECT * FROM m_acc_role WHERE role_id!=-1 ORDER BY role_descr desc";
		}
	}
		
		$tmp = $db->Query($SEL);
		$i = $db->getNumRows($tmp);
		If($i==0){
			return false;
		}
		
		while($i--){
		  	 $a_role["role_id"][$i] =  $db->getResult($tmp, $i,"role_id");
			 $a_role["role_descr"][$i] = $db->getResult($tmp, $i,"role_descr");
		}

		return $db->getNumRows($tmp);
}
?>
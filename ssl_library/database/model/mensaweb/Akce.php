<?php

namespace database\model\mensaweb;

use database\model\AbstractModel;

class Akce extends AbstractModel
{
    /**
     * @param int $id_a
     * @return array
     */
    public function getByIdA($id_a)
    {
        $query = "
        SELECT *
        FROM mc_akce
        WHERE id_a = ?
        ";

        return $this->database->fetchArray($query, [$id_a]);
    }

    /**
     * Vrati detail budouci udalosti
     * @param int $id_a
     * @return array
     */
    public function getFutureEvent($id_a)
    {
        $query = "
        SELECT *
        FROM mc_akce
        WHERE id_a = ?
        AND date_start > ?
        ";

        return $this->database->fetchArray($query, [$id_a, date("Y-m-01")]);
    }
    
    /**
     * Vrati seznam vsech akci (testovani i jine) v budoucnosti (od prvniho dne soucasneho mesice dale).
     * @return array
     */
    public function getFutureEvents()
    {
        $query = "
        SELECT *
        FROM mc_akce
        WHERE date_start > ?
        ";

        // vzdy vratit cely aktualni mesic
        return $this->database->fetchArrays($query, [date("Y-m-01")]);
    }
    
    /**
     * Vrati seznam vsech testovani IQ v budoucnosti (od prvniho dne soucasneho mesice dale).
     * @return array
     */
    public function getFutureIQEvents()
    {
        $query = "
        SELECT *
        FROM mc_akce
        WHERE typ = ?
        AND date_start > ?
        ";

        // vzdy vratit cely aktualni mesic
        return $this->database->fetchArrays($query, ["test", date("Y-m-01")]);
    }
    
    /**
     * Vrati seznam vsech budoucich akci, ktere nejsou stestem IQ  (od prvniho dne soucasneho mesice dale).
     * Pouziva se pro export kalendare akci na novy web.
     * @return array
     */
    public function getFutureNonIQEvents()
    {
        $query = "
        SELECT *
        FROM mc_akce
        WHERE typ <> ?
        AND date_start > ?
        ";

        // vzdy vratit cely aktualni mesic
        return $this->database->fetchArrays($query, ["test", date("Y-m-01")]);
    }

    /**
     * @param int $id_a
     * @return array
     */
    public function getEventWithOrg($id_a)
    {
        // TODO odebrat formátování data z databázového modelu

        $query = "
        SELECT a.city, a.place, DATE_FORMAT(a.date_start, '%e.%c.%Y %k:%i') den, a.email, a.telefon, a.nazev, a.popis,
        a.id_owner, m.jmeno, m.prijmeni 
        FROM mc_akce a
        JOIN m_members m ON a.id_owner = m.id_m AND id_a = ?
        ";

        return $this->database->fetchArray($query, [$id_a]);
    }

    /**
     * @param int $id_z
     * @return array
     */
    public function getEventByIdZ($id_z)
    {
        // TODO odebrat formátování data z databázového modelu do šablony apod.

        $query = "
        SELECT a.id_a, a.date_start, a.date_end, a.nazev, a.place, a.city, a.email, a.telefon,
        DATE_FORMAT(a.date_start, '%d.%m.%Y %H:%i') date_start_formatted,
        m.jmeno, m.prijmeni, p.amount
        FROM mc_akce a
        JOIN m_members m ON m.id_m = a.id_owner
        JOIN www_form_1 f ON f.id_a = a.id_a
        JOIN payments p ON p.customId = f.id_z
        WHERE f.id_z = ?
        LIMIT 1
        ";

        return $this->database->fetchArray($query, [$id_z]);
    }

    /**
     * @return array
     */
    public function getCities()
    {
        $query = "
        SELECT city
        FROM mc_akce
        WHERE typ LIKE ?
        AND date_start > ?
        GROUP BY city
        ORDER BY city
        ";

        return $this->database->fetchColumn($query, ["test", date("Y-m-d H:i:s")]);
    }

    /**
     * @param string $city
     * @return array
     */
    public function getIqTestsByCity($city)
    {
        // TODO odebrat formátování data z databázového modelu

        $query = "
        SELECT DATE_FORMAT(a.date_start, '%e.%c. %k:%i') den, a.place, a.id_a, a.test, p.kapacita, t.typ_nazev,
        (
          SELECT COUNT(id_z) FROM (
            SELECT * FROM www_form_1 WHERE id_z = (SELECT MAX(id_z) FROM www_form_1)
          ) f WHERE f.id_a = a.id_a
        ) poc_prihlasek
        FROM (
          SELECT * FROM mc_akce WHERE id_a = (SELECT MAX(id_a) FROM mc_akce)
        ) a
        LEFT JOIN (
          SELECT * FROM mc_akce_prihlasky WHERE id_a = (SELECT MAX(id_a) FROM mc_akce_prihlasky)
        ) p ON a.id_a = p.id_a
        LEFT JOIN (
          SELECT * FROM mc_typy_akci WHERE id_t = (SELECT MAX(id_t) FROM mc_typy_akci)
        ) t ON a.test = t.id_t
        WHERE a.typ = ?
        AND a.date_start > ?
        AND a.city = ?
        ORDER BY a.date_start
        ";

        return $this->database->fetchArrays($query, ["test", date("Y-m-d H:i:s"), $city]);
    }
}

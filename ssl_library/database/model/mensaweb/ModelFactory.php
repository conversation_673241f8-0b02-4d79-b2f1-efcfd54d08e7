<?php

namespace database\model\mensaweb;

use database\model\AbstractModelFactory;

class ModelFactory extends AbstractModelFactory
{
    /**
     * @return ActivityLog
     */
    public function getActivityLogModel()
    {
        return new ActivityLog($this->database);
    }

    /**
     * @return Akce
     */
    public function getAkceModel()
    {
        return new Akce($this->database);
    }

    /**
     * @return AkcePrihlasky
     */
    public function getAkcePrihlaskyModel()
    {
        return new AkcePrihlasky($this->database);
    }

    /**
     * @return Members
     */
    public function getMembersModel()
    {
        return new Members($this->database);
    }

    /**
     * @return MembersLogin
     */
    public function getMembersLoginModel()
    {
        return new MembersLogin($this->database);
    }

    /**
     * @return PoukazyNaTest
     */
    public function getPoukazyNaTestModel()
    {
        return new PoukazyNaTest($this->database);
    }

    /**
     * @return WwwForm1
     */
    public function getWwwForm1Model()
    {
        return new WwwForm1($this->database);
    }

    /**
     * @return WwwTestPoints
     */
    public function getWwwTestPointsModel()
    {
        return new WwwTestPoints($this->database);
    }
}

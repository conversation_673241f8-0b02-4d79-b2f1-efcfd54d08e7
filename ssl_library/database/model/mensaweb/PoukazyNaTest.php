<?php

namespace database\model\mensaweb;

use \PDOException;
use database\model\AbstractModel;

class PoukazyNaTest extends AbstractModel
{
    /**
     * @param array $voucher_params
     * @return int
     */
    public function getVoucherCode(array $voucher_params)
    {
        $query = "
        SELECT kod
        FROM m_poukazy_na_test
        WHERE id_prihlasky = ?
        AND CONCAT(TRIM(jmeno_obdarovaneho), ' ', TRIM(prijmeni_obdarovaneho)) LIKE ?
        ORDER BY kod DESC
        ";

        $params = [
            $voucher_params['id_z'],
            $voucher_params['name'] . " " .
            $voucher_params['surname'],
        ];

        return $this->database->fetchValue($query, $params);
    }

    /**
     * @param string $slevovy_kod
     * @param string $jmeno_a_prijmeni
     * @return array
     */
    public function getVoucherCodeForTestCheck($slevovy_kod, $jmeno_a_prijmeni)
    {
        $query = "
        SELECT *
        FROM m_poukazy_na_test
        WHERE kod = ?
        AND ((CONCAT(TRIM(jmeno_obdarovaneho), ' ', TRIM(prijmeni_obdarovaneho)) LIKE ?) OR na_jmeno = 0)
        AND je_pouzity = 0
        ORDER BY kod DESC
        ";

        return $this->database->fetchArray($query, [$slevovy_kod, $jmeno_a_prijmeni]);
    }

    /**
     * @param array $form_data
     * @return bool
     */
    public function isValidVoucherCode(array $form_data)
    {
        $query = "
        SELECT 1
        FROM m_poukazy_na_test
        WHERE kod = ?
        AND (
          (na_jmeno = ? AND CONCAT(TRIM(jmeno_obdarovaneho), ' ', TRIM(prijmeni_obdarovaneho)) LIKE ?)
          OR
          (na_jmeno = ?)
        )
        AND je_pouzity = ?
        ORDER BY kod DESC
        ";

        $params = [
            $form_data['voucher'],
            1,
            "%{$form_data['name']}% %{$form_data['surname']}%",
            0,
            0,
        ];

        return $this->database->fetchValue($query, $params);
    }

    /**
     * @param string $voucher_code
     * @param int $id_z
     * @param bool $paid_by_card
     * @throws PDOException
     */
    public function setVoucherAsApplied($voucher_code, $id_z, $paid_by_card)
    {
        $query = "
        UPDATE m_poukazy_na_test
        SET id_prihlasky = ?,
        je_pouzity = ?,
        platba_kartou = ?
        WHERE kod = ?
        ";

        $params = [
            $id_z,
            1,
            $paid_by_card,
            $voucher_code,
        ];

        $this->database->execute($query, $params);
    }

    /**
     * @param string $voucher_type
     * @param array $voucher_params
     * @return bool
     */
    public function insertNewVoucher($voucher_type, array $voucher_params)
    {
        $query = "
        INSERT INTO m_poukazy_na_test (
            typ,
            id_prihlasky,
            jmeno_obdarovaneho,
            prijmeni_obdarovaneho,
            email_pro_zaslani_kodu,
            aktivovat_do,
            prihlasit_do,
            je_pouzity,
            platba_kartou
        )
        VALUES (
            ?,
            ?,
            ?,
            ?,
            ?,
            CURDATE(),
            DATE_ADD(CURDATE(), INTERVAL +1 YEAR),
            ?,
            ?
        )
        ";

        $params = [
            $voucher_type,
            $voucher_params['id_z'],
            $voucher_params['name'],
            $voucher_params['surname'],
            $voucher_params['mail'],
            1,
            1,
        ];

        return $this->database->execute($query, $params);
    }
}

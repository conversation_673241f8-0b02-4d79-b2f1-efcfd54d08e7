<?php
use PHPUnit\Framework\TestCase;

/**
 * How to set it up (for dummies), on mac.
 *
 * 1)   New Mac OS' come with PHP interpreter. It is 7.3 (not ideal, but lets keep that one for now).
 *
 * 2)   Install phpunit:
 *      https://medium.com/@eduardobcolombo/installing-phpunit-globally-by-terminal-mac-osx-el-capitan-30313b87e8b5
 *
 *      Test:
 *          TKs-MacBook:~ tkubes$ /usr/local/bin/phpunit --version
 *          PHPUnit 9.1.5 by <PERSON> and contributors.
 *
 * 3)   Run the unit test using command line:
 *      /usr/bin/php /usr/local/bin/phpunit --configuration /Users/<USER>/Mensa/intranet/ssl_test_suite_2013/phpunit.xml  /Users/<USER>/Mensa/intranet/ssl_test_suite_2013/test_mcaptcha.php
 */


/* intranet encryption library, this is set to absolute path due to config issues on my own computer. */
require_once("../ssl_library_2013/mcaptcha.php");


class test_mcaptcha extends TestCase
{

    public function test_print()
    {
        $captcha = new MCaptcha(5);
        echo "\n\n\n{$captcha->print_label()}";
        echo "\n{$captcha->print_input()}\n\n\n";
    }
}

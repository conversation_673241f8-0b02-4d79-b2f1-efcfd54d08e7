<?php

use PHPUnit\Framework\TestCase;

class input_globals_classTest extends TestCase {

    private $promenna;

    function __construct() {
        parent::__construct();

        require_once('../ssl_library_2013/input_globals_class.php');
        $this->promenna = new input_globals();
        $_GET['zkoumanaInt'] = 10;
        $_POST['zkoumanaIntbez'] = -10;
        $_GET['zkoumanaIntPlus'] = +10;
        $_GET['zkoumanaIntRuzne'] = 1012;
        $_GET['zkoumanaIntStejne'] = 1010;
        $_POST['zkoumanaText'] = 'trava';
        $_GET['zkoumanaText'] = 'trava';
        $_POST['nenivgetu'] = 'krava';
        $_GET['nenivpostu'] = 'krava';
        $_GET['nenivposturovnase'] = 'kra=va';
        $_GET['nenivposturovnase1'] = 'kra==va=';
        $_POST['nenivgetudatum'] = '1.1.2012';
        $_GET['nenivpostudatum'] = '1.1.2012';
        $_GET['nenivpostudatumsmes'] = ' 1,1;20  12 ';
        $_GET['nenivpostuemail'] = '<EMAIL>';
        $_POST['nenivgetuemail'] = '<EMAIL>';
        $_POST['vadnyemail']='nema&zadnyzavinac';
        $_GET['vadnyemail1']='nema@zadnyzavi nac.cz';
        $_POST['vadnyemail2']='nema@zadnyzavi nac.cz';
        $_POST['email']='<EMAIL>';
        $_GET['email2']='<EMAIL>';
    }

    public function testJeGlobals() {
        $this->assertEquals(TRUE, $this->promenna->jeGLobals('zkoumanaInt'), "Nenašlo proměnnou v globální");
        $this->assertEquals(FALSE, $this->promenna->jeGLobals('neexistuje'), "Našlo neexistující");
        $this->assertEquals(TRUE, $this->promenna->jeGLobals('zkoumanaInt'), "Nenašlo proměnnou v globální");
        $this->assertEquals(TRUE, $this->promenna->jeGLobals('nenivgetu'), "Nenašlo pouze POSTovou proměnnou");
        $this->assertEquals(TRUE, $this->promenna->jeGLobals('nenivpostu'), "Nenašlo pouze GETovou proměnnou");
    }

    public function testJeInteger() {
        $this->assertEquals(NULL, $this->promenna->jeInteger('jinaint'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals(TRUE, $this->promenna->jeInteger('zkoumanaInt'), "Vyhodnotilo jako neinteger číslo bez znaménka");
        $this->assertEquals(TRUE, $this->promenna->jeInteger('zkoumanaIntbez'), "Vyhodnotilo jako neinteger záporné číslo");
        $this->assertEquals(TRUE, $this->promenna->jeInteger('zkoumanaIntPlus'), "Vyhodnotilo jako neinteger číslo s plus");
        $this->assertEquals(FALSE, $this->promenna->jeInteger('zkoumanaText'), "Vyhodnotilo jako integer, vstup text");
        $this->assertEquals(TRUE, $this->promenna->jeInteger('zkoumanaInt', 10), "Vyhodnotilo jako jinou hodnotu. Test s hodnotou 10");
        $this->assertEquals(FALSE, $this->promenna->jeInteger('zkoumanaInt', -10), "Vyhodnotilo jako jinou hodnotu. Test s hodnotou -10 proti 10");
        $this->assertEquals(FALSE, $this->promenna->jeInteger('zkoumanaIntStejne', 10), "Vyhodnotilo jako správný opakujíc se pattern. Test s hodnotou 10");
        $this->assertEquals(FALSE, $this->promenna->jeInteger('zkoumanaIntPlusRuzne', 10), "Vyhodnotilo jako správný podpattern. Test s hodnotou 10");
        $this->assertEquals(FALSE, $this->promenna->jeInteger('zkoumanaInt', '10z'), "Vyhodnotilo že 10 je totožné s 10z");
    }

    public function testOdchytHodnotu() {
        // test pro private metodu
        $metoda = new ReflectionMethod(get_class($this->promenna), 'odchytHodnotu');
        $metoda->setAccessible(true); // zpristupnim si metodu
        $this->assertEquals(NULL, $metoda->invokeArgs($this->promenna, array('zkoumanaTrrr','trava')), "Vyhodnotilo že proměnná existuje, ikdyž není");
        $this->assertEquals('trava', $metoda->invokeArgs($this->promenna, array('zkoumanaText','trava')), "Vyhodnotilo že je obsah proměnné jiný");
        $this->assertNotEquals('snop', $metoda->invokeArgs($this->promenna, array('zkoumanaText','trava')), "Vrátilo špatnou hodnotu");
    }
    public function testGetInteger(){
        $this->assertEquals(NULL, $this->promenna->getInteger('jinaint'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals(NULL, $this->promenna->getInteger('zkoumanaText'), "Vyhodnotilo textovou jako integer");
        $this->assertEquals(0, $this->promenna->getInteger('zkoumanaText',0), "Vyhodnotilo textovou jako integer a vratilo 0");
        $this->assertEquals(0, $this->promenna->getInteger('jinaint',0), "Vyhodnotilo špatně jako existující proměnnou a vratilo 0");
        $this->assertEquals(10, $this->promenna->getInteger('zkoumanaInt',0), "Vrátilo jinou hodnotu než 10");
        $this->assertEquals(10, $this->promenna->getInteger('zkoumanaInt'), "Vrátilo jinou hodnotu než 10");
        $this->assertEquals(-10, $this->promenna->getInteger('zkoumanaIntbez'), "Vrátilo jinou hodnotu než -10");
    }
    public function testGetString(){
        $this->assertEquals(NULL, $this->promenna->getString('jinaint'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals('ll', $this->promenna->getString('jinaint','ll'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertNotEquals('kk', $this->promenna->getString('zkoumanaInt','kk'), "Vyhodnotilo integer jako textovou a nevrátilo");
        $this->assertEquals('10', $this->promenna->getString('zkoumanaInt'), "Nevyhodnotilo integer jako textovou");
        $this->assertEquals('krava', $this->promenna->getString('nenivgetu'), "Nenašlo proměnnou v postu");
        $this->assertEquals('krava', $this->promenna->getString('nenivpostu'), "Nenašlo proměnnou v getu");
        $this->assertNotEquals('kra=va', $this->promenna->getString('nenivposturovnase'), "Nezrušilo rovnáse");
        $this->assertNotEquals('kra==va=', $this->promenna->getString('nenivposturovnase1'), "Nezrušilo rovnáse");
    }
    public function testGetDatum(){
        $this->assertEquals(NULL, $this->promenna->getDatum('jinaint'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals('l.1.2013', $this->promenna->getDatum('jinaint','l.1.2013'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals('l.1.2013', $this->promenna->getDatum('zkoumanaInt','l.1.2013'), "Vyhodnotilo krátký integer jako datum a nevrátilo defaultní hodnotu");
        $this->assertEquals('1.1.2012', $this->promenna->getDatum('nenivgetudatum'), "Nenašlo proměnnou v postu");
        $this->assertEquals('1.1.2012', $this->promenna->getDatum('nenivpostudatum'), "Nenašlo proměnnou v getu");
        $this->assertEquals('1.1.2012', $this->promenna->getDatum('nenivpostudatumsmes'), "Nezrušilo chybné znaky");
    }
    public function testGetEmail(){
        $this->assertEquals(NULL, $this->promenna->getEmail('jinaint'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('jinaint','<EMAIL>'), "Vyhodnotilo špatně jako existující proměnnou");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('email','<EMAIL>'), "Špatně vyhodnotilo email");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('nenivgetuemail'), "Nenašlo proměnnou v postu");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('nenivpostuemail'), "Nenašlo proměnnou v getu");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('vadnyemail1','<EMAIL>'), "Nevyhodnotilo chybu emailu chybějící zavináč");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('vadnyemail11','<EMAIL>'), "Nevyhodnotilo chybu emailu mezera v postu");
        $this->assertEquals('<EMAIL>', $this->promenna->getEmail('vadnyemail12','<EMAIL>'), "Nevyhodnotilo chybu emailu mezera v getu");
    }

}

?>

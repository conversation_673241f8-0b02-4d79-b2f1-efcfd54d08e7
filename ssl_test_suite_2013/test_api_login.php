<?php
use PHPUnit\Framework\TestCase;

/**
 * This is test case for the encryption functions.
 *
 * How to set it up (for dummies), on mac.
 *
 * 1)   New Mac OS' come with PHP interpreter. It is 7.3 (not ideal, but lets keep that one for now).
 *
 * 2)   Install phpunit:
 *      https://medium.com/@eduardobcolombo/installing-phpunit-globally-by-terminal-mac-osx-el-capitan-30313b87e8b5
 *
 *      Test:
 *          TKs-MacBook:~ tkubes$ /usr/local/bin/phpunit --version
 *          PHPUnit 9.1.5 by <PERSON> and contributors.
 *
 * 3)   Run the unit test using command line:
 *      /usr/bin/php /usr/local/bin/phpunit --configuration /Users/<USER>/Mensa/intranet/ssl_test_suite_2013/phpunit.xml /Users/<USER>/Mensa/intranet/ssl_test_suite_2013/test_api_login.php
 */


// test intranet
$TARGET_ENDPOINT = "https://10.10.5.88/api/login.php";


class test_api_login extends TestCase
{
    /**
     * Helper pro odeslani pozadavku.
     * @param $data
     * @return false|string
     */
    public function send_data($data){
        global $TARGET_ENDPOINT;

        $options = array(
            'ssl'=>array(
                'verify_peer'=>false,
                'verify_peer_name'=>false
            ),
            // https://www.php.net/manual/en/context.http.php
            'http' => array(
                'method'  => 'POST',
                'header'  => 'Content-Type: application/x-www-form-urlencoded',
                'content' => http_build_query($data)
            )
        );

        $context = stream_context_create($options);
        $result = file_get_contents($TARGET_ENDPOINT, false, $context);
        if ($result === FALSE) {
            $this->assert(false, "send_data(): Communication failed");
        }

        // @TODO: verify it is json
        var_dump($result);
        return json_decode($result);
    }


    /**
     * Incorrect parameters given.
     */
    public function test_needs_data()
    {
        $result = $this->send_data(array());
        $this->assertObjectHasAttribute('code', $result, 'Code not in response.');
        $this->assertEquals($result->code, -2, 'Wrong response code.');
    }

    public function test_wrong_api_key()
    {
        $result = $this->send_data(array('api_key' => 'a', 'clencislo' => 'a', 'heslo' => 'a'));
        $this->assertObjectHasAttribute('code', $result, 'Code not in response.');
        $this->assertEquals($result->code, -3, 'Wrong response code.');
    }

    public function test_wrong_user()
    {
        $result = $this->send_data(array('api_key' => '3A6A03E0699F6EE858018F5F5005576D', 'clencislo' => '1', 'heslo' => 'a'));
        $this->assertObjectHasAttribute('code', $result, 'Code not in response.');
        $this->assertEquals($result->code, -4, 'Wrong response code.');
    }

    /**
     * Correct user and password
     */
    public function test_wrong_password_6126()
    {
        $result = $this->send_data(array('api_key' => '3A6A03E0699F6EE858018F5F5005576D', 'clencislo' => '6126', 'heslo' => 'a'));
        $this->assertObjectHasAttribute('code', $result, 'Code not in response.');
        $this->assertEquals($result->code, -4);
    }

    /**
     * Correct user and password
     */
    public function test_6126()
    {
        $result = $this->send_data(array('api_key' => '3A6A03E0699F6EE858018F5F5005576D', 'clencislo' => '6126', 'heslo' => 'mensa'));
        $this->assertObjectHasAttribute('code', $result, 'Code not in response.');
        $this->assertEquals($result->code, 0);

        // test the content, since some values are anonymized, cannot test exact match
        $this->assertEquals($result->id_m, '1547');
        $this->assertEquals($result->clencislo, '6126');
        $this->assertEquals($result->typ, '1');
        $this->assertObjectHasAttribute('jmeno', $result, 'jmeno not in response.');
        $this->assertObjectHasAttribute('prijmeni', $result, 'prijmeni not in response.');
        $this->assertEquals($result->email, '<EMAIL>');
        // $this->assertEquals($result->pohlavi, '1');
        $this->assertObjectHasAttribute('telefon', $result, 'telefon not in response.');
        $this->assertObjectHasAttribute('mobil', $result, 'mobil not in response.');
        $this->assertObjectHasAttribute('roles', $result, 'roles not in response.');
    }

}

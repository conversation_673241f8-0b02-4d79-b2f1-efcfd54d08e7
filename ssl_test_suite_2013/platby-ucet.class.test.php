<?php
/**
 * Toto puvodne mel byt klasicky unit test, ale bohuzel nemam ide nakonfigurovane tak,
 * aby mi byl k necemu. Je to proto napsane ciste jako kod, ktery se spusti v ramci stranky
 *
 * User: tkubes
 * Date: 29/07/2017
 * Time: 11:42
 */

namespace intranet\platby\ucet;

// třída pro zpracování plateb
require_once '../ssl_pages_new/centr/sekretarka/platby-ucet.class.php';


class TransakceTest // extends \PHPUnit_Framework_TestCase
{
    function test_1(){
        // test input
        $input_row = '"6929017";"5500450000";"CZK";"29.06.2017";"Příchozí tuzemská platba";"<PERSON><PERSON><PERSON><PERSON><PERSON>,Bc.";"";"";"**********";"0800";"";"150 (jed<PERSON><PERSON><PERSON><PERSON><PERSON> poplatek) + 300 (<PERSON><PERSON><PERSON><PERSON><PERSON> příspěvek)";"450,00";"DOMESTICPAYMENT";"Domácí platba";"PROCESSED";"Zpracováno";"";"";"001D11I171801736-I170629JYWGWK";"29.06.2017";"";"21091992";"";"308";"";"";"";"";"";"false";"";"";"";"";"";"";"";"";';

        // parse row, do not save to database
        $transakce =  new Transakce($input_row, NULL);

        echo("<p><b>Data</b><br>");
        print_r($transakce->get_radek());
        echo("</p>");

        // verify output
        echo("<table style='border: 1px solid black' cellspacing='1' cellpadding='1' border='1'>");
        echo("<tr>");
        echo($transakce->print_s('table'));
        echo("</tr>");
        echo("</table>");


        //echo("get_popis(): " . $transakce->get_popis());
        assert($transakce->get_popis() == "150 (jednorázový poplatek) + 300 (základní členský příspěvek)");

        //echo($transakce->get_vs());
        assert($transakce->get_vs() == 21091992);

        //echo($transakce->get_ks());
        assert($transakce->get_ks() == 308);

        //echo($transakce->is_valid() == TRUE);
        assert($transakce->is_valid());

        //echo($transakce->is_saved());
        assert($transakce->is_saved() == FALSE);

        //echo($transakce->get_error());
        //assert($transakce->get_error());

        //echo($transakce->get_castka());
        assert($transakce->get_castka() == 450);

        echo("get_cislo_uctu(): " . $transakce->get_cislo_uctu() . "<br>");
        assert($transakce->get_cislo_uctu() == "**********/0800");

        echo("get_nazev_uctu(): " . $transakce->get_nazev_uctu() . "<br>");
        // assert($transakce->get_nazev_uctu() == "Hořejší Jan,Bc.");

        // dte is as unix timestamp
        // echo("get_datum():" . $transakce->get_datum());
        assert($transakce->get_datum() == strtotime('2017-06-29'));

        // echo($transakce->get_cislo_vypisu());
        assert($transakce->get_cislo_vypisu() == 6929017);

        echo("get_reference_banky(): " . $transakce->get_reference_banky() . "<br>");
        // v referenci banky prevadime vse mimo alfanumerickych znaku na podtrzitka
        assert($transakce->get_reference_banky() == "001D11I171801736_I170629JYWGWK");

    }

    function test_2()
    {
        // puvodni nazev souboru statement_5500450000_20170601_20170630.csv
        $radky = file("../sandbox/9.csv", FILE_SKIP_EMPTY_LINES + FILE_IGNORE_NEW_LINES);
        // print_r($radky);

        $total = 0;
        $ok = 0;

        echo("<table style='border: 1px solid black' cellspacing='1' cellpadding='1' border='1'>");
        foreach ($radky as $radek_transakce)
        {
            $total ++;
            $transakce = new Transakce($radek_transakce, NULL);

            echo("<tr>");
            if ($transakce->is_valid()){
                echo($transakce->print_s('table'));
                $ok++;
            }
            else echo("<td colspan='8'>" . $transakce->print_s() . "</td>");
            echo("</tr>");
        }
        echo("</table>");
        assert($total == ($ok+1));
    }
}


// run the test
$t = new TransakceTest();
$t->test_1();
$t->test_2();

<?php

namespace config;

use test\MensaTestCase;

class ConfigTest extends MensaTestCase
{
    public function test_live_environment()
    {
        $_SERVER = [
            "SERVER_ADDR" => "**************",
        ];

        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [
                    "**************",
                ],
            ],
            "live" => [],
        ]);

        $this->assertEquals("live", $obj->getEnvironment());
        $this->assertTrue($obj->isLiveEnvironment());
    }

    public function test_devel_environment()
    {
        $_SERVER = [
            "SERVER_ADDR" => "**********",
        ];

        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [
                    "**************",
                ],
            ],
            "devel" => [],
        ]);

        $this->assertEquals("devel", $obj->getEnvironment());
        $this->assertTrue($obj->isDevelEnvironment());
    }

    public function test_default_environment_for_empty_whitelist()
    {
        $_SERVER = [
            "SERVER_ADDR" => "**************",
        ];

        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [],
            ],
            "devel" => [],
        ]);

        $this->assertEquals("devel", $obj->getEnvironment());
        $this->assertTrue($obj->isDevelEnvironment());
    }

    public function test_hasValue_from_shared_config()
    {
        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [],
                "custom" => [
                    "parameter" => 123,
                ],
            ],
            "devel" => [],
        ]);

        $this->assertTrue($obj->hasValue("custom.parameter"));
    }

    public function test_hasValue_from_env_config()
    {
        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [],
            ],
            "devel" => [
                "custom" => [
                    "parameter" => 123,
                ],
            ],
        ]);

        $this->assertTrue($obj->hasValue("custom.parameter"));
    }

    public function test_getValue_from_shared_config()
    {
        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [],
                "scalar" => 1,
                "array" => [
                    "aaa" => 2,
                    "bbb" => 3,
                ],
            ],
            "devel" => [],
        ]);

        $this->assertEquals(1, $obj->getValue("scalar"));
        $this->assertEquals(["aaa" => 2, "bbb" => 3], $obj->getValue("array"));
        $this->assertEquals(2, $obj->getValue("array.aaa"));
    }

    public function test_getValue_from_env_config()
    {
        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [],
            ],
            "devel" => [
                "scalar" => 1,
                "array" => [
                    "aaa" => 2,
                    "bbb" => 3,
                ],
            ],
        ]);

        $this->assertEquals(1, $obj->getValue("scalar"));
        $this->assertEquals(["aaa" => 2, "bbb" => 3], $obj->getValue("array"));
        $this->assertEquals(2, $obj->getValue("array.aaa"));
    }

    public function test_getValue_override_in_env_config()
    {
        $obj = new Config([
            "shared" => [
                "live_ip_address_whitelist" => [],
                "scalar" => 1,
                "array" => [
                    "aaa" => 2,
                    "bbb" => 3,
                ],
            ],
            "devel" => [
                "scalar" => 4,
                "array" => [
                    "uuu" => 5,
                    "vvv" => 6,
                ],
            ],
        ]);

        $this->assertEquals(4, $obj->getValue("scalar"));
        $this->assertEquals(["uuu" => 5, "vvv" => 6], $obj->getValue("array"));

        $this->assertNull($obj->getValue("array.aaa"));
        $this->assertEquals(5, $obj->getValue("array.uuu"));
    }
}

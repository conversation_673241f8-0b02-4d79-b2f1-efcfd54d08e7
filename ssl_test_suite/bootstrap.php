<?php

ini_set("display_errors", 1);
error_reporting(E_ALL & ~E_STRICT & ~E_NOTICE);


define("LIB_DIR", dirname(__FILE__) . "/../ssl_library");

require_once LIB_DIR . "/autoload.php";
require_once LIB_DIR . "/vendor/autoload.php";

require_once dirname(__FILE__) . "/mocks.php";

$autoload_dirs = [
    LIB_DIR,
];

spl_autoload_register(function($class) use ($autoload_dirs) {
    autoload($class, $autoload_dirs);
});

<?php

namespace helpers;

use test\MensaTestCase;

class MailTest extends MensaTestCase
{
    public function test_toPhpMailerAltBody()
    {
        $this->assertEquals(
            "<p>html<br />\ntext</p>",
            Mail::toPhpMailerAltBody("<p>html\ntext</p>")
        );

        $mail_text = Mail::toPhpMailerAltBody(
"<p>Vážená/ý paní/pane,</p>

<p>děkujeme za Vaší platbu za IQ test a potvrzujeme Vaší registraci na testování IQ.</p>

<p><a href='mailto:<EMAIL>'><EMAIL></a></p>"
        );

        $expected_text = "<p>Vážená/ý paní/pane,</p><br />\n<br />\n<p>děkujeme za Vaší platbu za IQ test a potvrzujeme Vaší registraci na testování IQ.</p><br />\n<br />\n<p><a href='mailto:<EMAIL>'><EMAIL></a></p>";

        $this->assertEquals($expected_text, str_replace("\r\n", "\n", $mail_text));

        $this->assertEquals(
            "plain text",
            Mail::toPhpMailerAltBody("plain text")
        );
    }
}


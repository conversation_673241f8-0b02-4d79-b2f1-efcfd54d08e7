<?php

namespace helpers;

use test\MensaTestCase;

class HtmlTest extends MensaTestCase
{
    public function test_deduplicateLineBreakTags()
    {
        $this->assertEquals("x<br />y", Html::deduplicateLineBreakTags("x<br />y"));
        $this->assertEquals("x<br /><br />y", Html::deduplicateLineBreakTags("x<br /><br />y"));
        $this->assertEquals("x<br /><br />y", Html::deduplicateLineBreakTags("x<br /><br /><br />y"));
        $this->assertEquals("x<br /><br />y", Html::deduplicateLineBreakTags("x<br /><br /><br /><br />y"));

        $this->assertEquals("x<br /><br />y", Html::deduplicateLineBreakTags("x<br><br><br>y"));
        $this->assertEquals("x<br /><br />y", Html::deduplicateLineBreakTags("x<br ><br ><br >y"));
        $this->assertEquals("x<br /><br />y", Html::deduplicateLineBreakTags("x<br/><br/><br/>y"));
    }
}

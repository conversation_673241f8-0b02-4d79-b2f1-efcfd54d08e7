<?php

namespace helpers;

use test\MensaTestCase;

class DatesTest extends MensaTestCase
{
    public function test_addLeadingZerosToPlainDate()
    {
        $this->assertEquals("01.02.3021", Dates::addLeadingZerosToPlainDate("01.02.3021"));
        $this->assertEquals("01.02.3021", Dates::addLeadingZerosToPlainDate("1.2.3021"));
        $this->assertEquals("27.12.3021", Dates::addLeadingZerosToPlainDate("27.12.3021"));

        $this->assertEquals("", Dates::addLeadingZerosToPlainDate(""));
        $this->assertEquals("", Dates::addLeadingZerosToPlainDate(123));
    }
}

<?php

/**
 * Adapted from JK project <<EMAIL>>.
 */
namespace helpers;

use test\MensaTestCase;

class FilesTest extends MensaTestCase
{
    public function test_removeAccents()
    {
        $this->assertEquals("simple-string", Files::document("simple string"));
        $this->assertEquals("uplne-zlutoucky-kun-pel-dabelske-ody", Files::document("úplně žluťoučký kůň pěl ďábelské ódy"));
        $this->assertEquals("special-chars", Files::document("special chars &#$@"));
        $this->assertEquals("brackets", Files::document("brackets []"));
        $this->assertEquals("", Files::document(""));
    }
}

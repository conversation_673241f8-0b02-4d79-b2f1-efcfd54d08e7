<?php

namespace helpers;

use test\MensaTestCase;

class SqlTest extends MensaTestCase
{
    public function test_buildQueryFields()
    {
        $data = [
            "id_a" => 2368,
            "name" => "<PERSON><PERSON>",
            "surname" => "<PERSON><PERSON><PERSON>",
        ];

        $this->assertEquals("id_a, name, surname", Sql::buildQueryFields($data));
    }

    public function test_buildQueryPlaceholders()
    {
        $data = [
            "id_a" => 2368,
            "name" => "<PERSON><PERSON>",
            "surname" => "<PERSON><PERSON><PERSON>",
        ];

        $this->assertEquals("?, ?, ?", Sql::buildQueryPlaceholders($data));
    }

    public function test_getQueryParameters()
    {
        $data = [
            "id_a" => 2368,
            "name" => "<PERSON><PERSON>",
            "surname" => "<PERSON><PERSON><PERSON>",
        ];

        $this->assertEquals([2368, "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"], Sql::getQueryParameters($data));
    }
}

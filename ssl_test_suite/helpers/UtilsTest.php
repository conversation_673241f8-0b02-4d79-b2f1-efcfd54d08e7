<?php

namespace helpers;

use test\MensaTestCase;

class UtilsTest extends MensaTestCase
{
    public function test_parseFullName()
    {
        $this->assertEquals([
            "jmeno" => "name",
            "prijmeni" => "surname",
        ], Utils::parseFullName([
            "jmeno" => "name surname",
        ]));

        $this->assertEquals([
            "jmeno" => "name",
            "prijmeni" => "middlename surname",
        ], Utils::parseFullName([
            "jmeno" => "name middlename surname",
        ]));

        $this->assertEquals([
            "jmeno" => "fullname",
            "prijmeni" => "",
        ], Utils::parseFullName([
            "jmeno" => "fullname",
        ]));

        $this->assertEquals([
            "jmeno" => "",
            "prijmeni" => "",
        ], Utils::parseFullName([
            "jmeno" => "",
        ]));

        $this->assertEquals([], Utils::parseFullName([]));
    }
}

<?php

namespace payment\Data;

use test\MensaTestCase;

class PaymentTest extends MensaTestCase
{
    public function test_get_set()
    {
        $obj = new Payment();

        $value = 300;
        $this->assertEquals($value, $obj->setAmount($value)->getAmount());

        $value = 48932;
        $this->assertEquals($value, $obj->setCustomId($value)->getCustomId());

        $value = "Yngwie";
        $this->assertEquals($value, $obj->setName($value)->getName());

        $value = "Malmsteen";
        $this->assertEquals($value, $obj->setSurname($value)->getSurname());

        $value = "20200102";
        $this->assertEquals($value, $obj->setVoucher($value)->getVoucher());
    }

    public function test_validateForPayment_true()
    {
        $obj = new Payment();

        $obj->setAmount(300);
        $obj->setCustomId(48932);
        $obj->setName("Yngwie");
        $obj->setSurname("Malmsteen");

        $this->assertTrue($obj->validateForPayment());
    }

    public function test_validateForPayment_false()
    {
        $obj = new Payment();

        $this->assertFalse($obj->validateForPayment());
    }

    public function test_validateForVoucher_true()
    {
        $obj = new Payment();
        $obj->setCustomId(48932);

        $this->assertTrue($obj->validateForVoucher());
    }

    public function test_validateForVoucher_false()
    {
        $obj = new Payment();

        $this->assertFalse($obj->validateForVoucher());
    }

    public function test_escape()
    {
        $obj = new Payment();

        $obj->setName("<div>hallowed be<br /> thy name</div>");

        $this->assertEquals("hallowed be thy name", $obj->getName());
    }
}

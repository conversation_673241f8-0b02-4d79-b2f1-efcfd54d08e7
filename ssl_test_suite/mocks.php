<?php

/** @noinspection PhpIllegalPsrClassPathInspection */
namespace test;

use config\ConfigInterface;
use database\connection\Connection;
use database\DatabaseInterface;
use mailer\MailerInterface;
use PDOStatement;
use PHPMailer\PHPMailer\PHPMailer;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use vcalendar\VCalendarBuilder;
use vcalendar\VCalendarInterface;

class MensaTestCase extends TestCase
{
    /**
     * @return MockObject|ConfigInterface
     */
    protected function getConfigMock()
    {
        return $this->getMockBuilder('config\ConfigInterface')->disableOriginalConstructor()->getMock();
    }

    /**
     * @param PDOStatement $statement
     * @return MockObject|Connection
     */
    protected function getDatabaseConnectionMock(PDOStatement $statement)
    {
        $connection = $this->getMockBuilder('database\connection\Connection')->disableOriginalConstructor()->getMock();
        $connection->expects($this->any())->method("prepare")->will($this->returnValue($statement));

        return $connection;
    }

    /**
     * @return DatabaseInterface|MockObject
     */
    protected function getDatabaseMock()
    {
        return $this->getMockBuilder('\database\DatabaseInterface')->disableOriginalConstructor()->getMock();
    }

    /**
     * @return PDOStatement|MockObject
     */
    protected function getDatabaseStatementMock()
    {
        return $this->getMockBuilder('\PDOStatement')->disableOriginalConstructor()->getMock();
    }

    /**
     * @return MockObject|MailerInterface
     */
    protected function getMailerMock()
    {
        return $this->getMockBuilder('mailer\MailerInterface')->disableOriginalConstructor()->getMock();
    }

    /**
     * @return MockObject|PHPMailer
     */
    protected function getPhpMailerMock()
    {
        return $this->getMockBuilder('\PHPMailer\PHPMailer\PHPMailer')->disableOriginalConstructor()->getMock();
    }

    /**
     * @return MockObject|VCalendarInterface
     */
    protected function getVCalendarMock()
    {
        return $this->getMockBuilder('\vcalendar\VCalendarInterface')->disableOriginalConstructor()->getMock();
    }

    /**
     * @return MockObject|VCalendarBuilder
     */
    protected function getVCalendarBuilderMock()
    {
        return $this->getMockBuilder('\vcalendar\VCalendarBuilder')->disableOriginalConstructor()->getMock();
    }
}

<?php

namespace database;

use PDOStatement;
use PHPUnit\Framework\MockObject\MockObject;
use test\MensaTestCase;

class DatabaseTest extends MensaTestCase
{
    public function test_fetchAll()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("setFetchMode");
        $statement->expects($this->once())->method("fetchAll");
        
        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchAll("SELECT * FROM test", []);
    }

    public function test_fetchArrays()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("setFetchMode");
        $statement->expects($this->once())->method("fetchAll");

        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchArrays("SELECT * FROM test", []);
    }

    public function test_fetchObject_default()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("setFetchMode");
        $statement->expects($this->once())->method("fetch");
        
        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchObject("SELECT * FROM test LIMIT 1", []);
    }

    public function test_fetchArray_default()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("setFetchMode");
        $statement->expects($this->once())->method("fetch");

        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchArray("SELECT * FROM test LIMIT 1", []);
    }

    public function test_fetchPairs()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("setFetchMode");
        $statement->expects($this->once())->method("fetchAll");

        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchMap("SELECT id, name FROM test", []);
    }

    public function test_fetchColumn()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("setFetchMode");
        $statement->expects($this->once())->method("fetchAll");
        
        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchColumn("SELECT name FROM test LIMIT 1", []);
    }

    public function test_fetchValue()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");
        $statement->expects($this->once())->method("fetchColumn");
        
        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->fetchValue("SELECT name FROM test", []);
    }
    
    public function test_execute()
    {
        $statement = $this->getStatementMock();
        $statement->expects($this->once())->method("execute");

        $obj = new Database($this->getDatabaseConnectionMock($statement));

        $obj->execute("UPDATE test SET name = ? WHERE id = ?");
    }

    public function test_getInsertId()
    {
        $connection = $this->getDatabaseConnectionMock($this->getStatementMock());
        $connection->expects($this->once())->method("lastInsertId");
        
        $obj = new Database($connection);
        
        $obj->getInsertId();
    }
    
    public function test_begin()
    {
        $connection = $this->getDatabaseConnectionMock($this->getStatementMock());
        $connection->expects($this->once())->method("beginTransaction");

        $obj = new Database($connection);
        
        $obj->begin();
    }
    
    public function test_commit()
    {
        $connection = $this->getDatabaseConnectionMock($this->getStatementMock());
        $connection->expects($this->once())->method("commit");
        
        $obj = new Database($connection);
        
        $obj->commit();
    }
    
    public function test_rollback()
    {
        $connection = $this->getDatabaseConnectionMock($this->getStatementMock());
        $connection->expects($this->once())->method("rollBack");
        
        $obj = new Database($connection);

        $obj->rollback();
    }

    public function test_debug()
    {
        $obj = new Database($this->getDatabaseConnectionMock($this->getStatementMock()));
        
        $query = "SELECT * FROM test WHERE id = ? AND status = ?";
        $parameters = [318, "active"];
        
        ob_start();
        $obj->debug($query, $parameters);
        $output = ob_get_clean();
        
        $this->assertEquals("<div>SELECT * FROM test WHERE id = '318' AND status = 'active'</div>", $output);
    }

    /**
     * @return PDOStatement|MockObject
     */
    protected function getStatementMock()
    {
        return $this->getMockBuilder('\PDOStatement')->disableOriginalConstructor()->getMock();
    }
}

<?php

namespace database\model;

use database\model\mensaweb\ModelFactory;
use test\MensaTestCase;

class AbstractModelFactoryTest extends MensaTestCase
{
    public function test_getDatabase()
    {
        /* abstract class is tested via concrete class */
        $obj = new ModelFactory($this->getDatabaseMock());

        $this->assertInstanceOf('\database\DatabaseInterface', $obj->getDatabase());
    }
}

<?php

namespace database\model\mensaweb;

use test\MensaTestCase;

class ModelFactoryTest extends MensaTestCase
{
    public function test_get_models()
    {
        $obj = new ModelFactory($this->getDatabaseMock());

        $this->assertInstanceOf('\database\model\mensaweb\ActivityLog', $obj->getActivityLogModel());
        $this->assertInstanceOf('\database\model\mensaweb\Akce', $obj->getAkceModel());
        $this->assertInstanceOf('\database\model\mensaweb\AkcePrihlasky', $obj->getAkcePrihlaskyModel());
        $this->assertInstanceOf('\database\model\mensaweb\Members', $obj->getMembersModel());
        $this->assertInstanceOf('\database\model\mensaweb\MembersLogin', $obj->getMembersLoginModel());
        $this->assertInstanceOf('\database\model\mensaweb\PoukazyNaTest', $obj->getPoukazyNaTestModel());
        $this->assertInstanceOf('\database\model\mensaweb\WwwForm1', $obj->getWwwForm1Model());
        $this->assertInstanceOf('\database\model\mensaweb\WwwTestPoints', $obj->getWwwTestPointsModel());
    }
}

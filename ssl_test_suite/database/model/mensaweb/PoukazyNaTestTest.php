<?php

namespace database\model\mensaweb;

use test\MensaTestCase;

class PoukazyNaTestTest extends MensaTestCase
{
    public function test_getVoucherCode()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchValue");

        $obj = new PoukazyNaTest($database);

        $obj->getVoucherCode([
            "id_z" => 123,
            "name" => "<PERSON>",
            "surname" => "<PERSON>",
        ]);
    }

    public function test_getVoucherCodeForTestCheck()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new PoukazyNaTest($database);

        $obj->getVoucherCodeForTestCheck(902033, "<PERSON>");
    }

    public function test_isValidVoucherCode()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchValue");

        $obj = new PoukazyNaTest($database);

        $obj->isValidVoucherCode([
            "voucher" => "100237",
            "name" => "Martin",
            "surname" => "Smith",
        ]);
    }

    public function test_setVoucherAsApplied()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("execute");

        $obj = new PoukazyNaTest($database);

        $obj->setVoucherAsApplied("100237", 123, 1);
    }

    public function test_insertNewVoucher()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("execute");

        $obj = new PoukazyNaTest($database);

        $obj->insertNewVoucher("custom", [
            "id_z" => 123,
            "name" => "Martin",
            "surname" => "Smith",
            "mail" => "<EMAIL>",
        ]);
    }
}

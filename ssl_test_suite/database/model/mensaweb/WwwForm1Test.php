<?php

namespace database\model\mensaweb;

use test\MensaTestCase;

class WwwForm1Test extends MensaTestCase
{
    public function test_getAttendeeByIdZ()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new WwwForm1($database);

        $obj->getAttendeeByIdZ(123);
    }

    public function test_insertAction()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("execute");

        $obj = new WwwForm1($database);

        $obj->insertAction([
            "id_a" => 11499,
            "id_org" => 2144,
        ]);
    }
}

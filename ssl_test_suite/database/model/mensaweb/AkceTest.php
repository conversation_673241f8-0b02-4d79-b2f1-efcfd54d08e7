<?php

namespace database\model\mensaweb;

use test\MensaTestCase;

class AkceTest extends MensaTestCase
{
    public function test_getByIdA()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new Akce($database);

        $obj->getByIdA(123);
    }

    public function test_getFutureEvent()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new Akce($database);

        $obj->getFutureEvent(456);
    }

    public function test_getEventWithOrg()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new Akce($database);

        $obj->getEventWithOrg(123);
    }

    public function test_getEventByIdZ()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new Akce($database);

        $obj->getEventByIdZ(456);
    }

    public function test_getCities()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchColumn");

        $obj = new Akce($database);

        $obj->getCities();
    }

    public function test_getIqTestsByCity()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArrays");

        $obj = new Akce($database);

        $obj->getIqTestsByCity("Brno");
    }
}

<?php

namespace database\model\mensaweb;

use test\MensaTestCase;

class MembersTest extends MensaTestCase
{
    public function test_getUserByCredentials()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new Members($database);

        $obj->getUserByCredentials("<EMAIL>", "password");
    }

    public function test_getUserByLogin()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("fetchArray");

        $obj = new Members($database);

        $obj->getUserByLogin("<EMAIL>");
    }
}

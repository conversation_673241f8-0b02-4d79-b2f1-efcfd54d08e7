<?php

namespace database\model\mensaweb;

use test\MensaTestCase;

class WwwTestPointsTest extends MensaTestCase
{
    public function test_insertRegistration()
    {
        $database = $this->getDatabaseMock();
        $database->expects($this->once())->method("execute");
        $database->expects($this->once())->method("getInsertId");

        $obj = new WwwTestPoints($database);

        $obj->insertRegistration([
            "jmeno" => "Tobias",
            "prijmeni" => "Sammet",
        ]);
    }
}

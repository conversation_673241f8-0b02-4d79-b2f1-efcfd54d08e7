<?php

namespace crypto;

use test\MensaTestCase;

class AESTest extends MensaTestCase
{
    public function test_encrypt_decrypt()
    {
        $aes_crypt = new AES("secret");

        $plain_text = "test";
        $this->assertEquals("test", $aes_crypt->decrypt($aes_crypt->encrypt($plain_text)));

        $plain_text = "125";
        $this->assertEquals("125", $aes_crypt->decrypt($aes_crypt->encrypt($plain_text)));

        $plain_text = "";
        $this->assertEquals("", $aes_crypt->decrypt($aes_crypt->encrypt($plain_text)));
    }
}


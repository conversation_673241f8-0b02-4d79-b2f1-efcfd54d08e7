<?php

namespace crypto;

use test\MensaTestCase;

/**
 * This is test case for the encryption functions.
 *
 * How to set it up (for dummies), on mac.
 *
 * 1)   New Mac OS' come with PHP interpreter. It is 7.3 (not ideal, but lets keep that one for now).
 *
 * 2)   Install phpunit:
 *      https://medium.com/@eduardobcolombo/installing-phpunit-globally-by-terminal-mac-osx-el-capitan-30313b87e8b5
 *
 *      Test:
 *          TKs-MacBook:~ tkubes$ /usr/local/bin/phpunit --version
 *          PHPUnit 9.1.5 by <PERSON> and contributors.
 *
 * 3)   Run the unit test using command line:
 *      /usr/bin/php /usr/local/bin/phpunit --configuration /Users/<USER>/Mensa/intranet/ssl_test_suite_2013/phpunit.xml  /Users/<USER>/Mensa/intranet/ssl_test_suite_2013/test_RB.php
 */

/** @noinspection PhpUnusedLocalVariableInspection */
$test_data = array(
    '' => '#*##*#',
    '100' => 'PS#*##*#',
    '91' => 'XR#*##*#',
    'v<PERSON><PERSON> jak 138' => 'ŽUS[SR' . "\0" . '#*##*#',
    '138' => 'PP#*##*#',
    '115' => 'PR#*##*#',
    '126' => 'PQ' . "\0" . '#*##*#',
    '97' => 'XT#*##*#',
    '96' => 'XU#*##*#',
    '81' => 'YR#*##*#',
    '95' => 'XV#*##*#',
    '80' => 'YS#*##*#',
    '93' => 'XP#*##*#',
    '98' => 'X[#*##*#',
    '142' => 'PW#*##*#',
    '89' => 'YZ#*##*#',
    '84' => 'YW#*##*#',
    '99' => 'XZ#*##*#',
    '92' => 'XQ#*##*#',
    '94' => 'XW#*##*#',
    '85' => 'YV#*##*#',
    '90' => 'XS#*##*#',
    'pod 81' => 'R' . "\0" . '#*##*#',
    'více jak 144' => 'ŽUS[SU#*##*#',
    '88' => 'Y[#*##*#',
    '87' => 'YT#*##*#',
    '83' => 'YP#*##*#',
    '86' => 'YU#*##*#',
    'více jak 143' => 'ŽUS[SU#*##*#',
    '82' => 'YQ#*##*#',
    'více jak 140' => 'ŽUS[SU#*##*#',
    'více jak 142' => 'ŽUS[SU
#*##*#',
    '19' => 'PZ#*##*#',
    '0' => 'Q#*##*#',
    '000' => 'QS#*##*#'
);

class RBTest extends MensaTestCase
{
    public function testEncrypt()
    {
        global $test_data;
        $cr = new RB();

        foreach ($test_data as $plain => $encrypted){
            $result = $cr->encrypt($plain);
            echo "\nTesting encryption\tplain: '{$plain}', encrypted: '{$encrypted}', result: '{$result}'";
            $this->assertEquals($encrypted, $result);
        }
    }

    public function testDecrypt()
    {
        global $test_data;
        $cr = new RB();
        foreach ($test_data as $plain => $encrypted){
            $decrypted = $cr->decrypt($encrypted);
            echo "\nTesting decryption\texpected: '{$plain}', encrypted: '{$encrypted}', decrypted: '{$decrypted}'";
            $this->assertEquals($plain, $decrypted);
        }
    }
}

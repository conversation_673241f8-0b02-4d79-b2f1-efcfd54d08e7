<?php

namespace crypto;

use test\MensaTestCase;

class CryptoFactoryTest extends MensaTestCase
{
    public function test_getAES()
    {
        $this->assertInstanceOf('crypto\AES', CryptoFactory::getAES());
        $this->assertInstanceOf('crypto\AES', CryptoFactory::getAES("custom_secret"));
    }

    public function test_getRB()
    {
        $this->assertInstanceOf('crypto\RB', CryptoFactory::getRB());
        $this->assertInstanceOf('crypto\RB', CryptoFactory::getRB("custom_secret"));
    }
}

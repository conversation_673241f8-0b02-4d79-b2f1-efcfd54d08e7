<?php

/** @noinspection PhpUnhandledExceptionInspection */

namespace vcalendar;

use test\MensaTestCase;

class VCalendarTest extends MensaTestCase
{
    public function test_getPhpMailer()
    {
        $obj = new VCalendar($this->getVCalendarBuilderMock());

        $this->assertInstanceOf('\vcalendar\VCalendarBuilder', $obj->getCalendarBuilder());
    }

    public function test_getPaymentIcal_ok()
    {
        $builder = $this->getVCalendarBuilderMock();

        $builder->setUid("uuid");
        $builder->setDtstamp("timestamp");

        $builder->expects($this->once())->method("setDateStartFromString");
        $builder->expects($this->once())->method("setDateEndFromString");
        $builder->expects($this->once())->method("setOrganizer");
        $builder->expects($this->once())->method("setLocation");
        $builder->expects($this->once())->method("setSummary");
        $builder->expects($this->once())->method("getCalendarAsString");

        $obj = new VCalendar($builder);

        $obj->getEventIcal([
            "date_start" => "2021-01-27 15:30:00",
            "date_end" => "2021-01-27 17:20:00",
            "nazev" => "Testování",
            "email" => "<EMAIL>",
            "place" => "Krátká 123",
            "city" => "Praha 98",
            "jmeno_org" => "organizer",
            "email_org" => "<EMAIL>",
        ]);
    }

    public function test_getIqTestIcal_ok()
    {
        $builder = $this->getVCalendarBuilderMock();

        $builder->setUid("uuid");
        $builder->setDtstamp("timestamp");

        $builder->expects($this->once())->method("setDateStartFromString");
        $builder->expects($this->once())->method("setDateEndFromString");
        $builder->expects($this->once())->method("setOrganizer");
        $builder->expects($this->once())->method("setLocation");
        $builder->expects($this->once())->method("setSummary");
        $builder->expects($this->once())->method("setStatus");
        $builder->expects($this->once())->method("getCalendarAsString");

        $obj = new VCalendar($builder);

        $obj->getIqTestIcal([
            "date_start" => "2021-01-27 15:30:00",
            "date_end" => "2021-01-27 17:20:00",
            "nazev" => "Testování",
            "jmeno_org" => "Mensa Česko",
            "email_org" => "<EMAIL>",
            "place" => "Krátká 123",
            "city" => "Praha 98",
        ]);
    }
}

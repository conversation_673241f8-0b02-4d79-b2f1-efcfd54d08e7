<?php

/** @noinspection PhpUnhandledExceptionInspection */

namespace vcalendar;

use \DateTime;
use test\MensaTestCase;

class VCalendarBuilderTest extends MensaTestCase
{
    public function test_validate_ok_1()
    {
        $obj = new VCalendarBuilder();

        /* deterministic mock values */
        $obj->setUid("uid");
        $obj->setDtstamp("timestamp");

        $obj->setDateStart(new DateTime("2021-01-27 15:30:00"));
        $obj->setDateEnd(new DateTime("2021-01-27 17:20:00"));
        $obj->setSummary("Testování");

        $this->assertEquals([], $obj->validate());
    }

    public function test_validate_ok_2()
    {
        $obj = new VCalendarBuilder();

        /* deterministic mock values */
        $obj->setUid("uid");
        $obj->setDtstamp("timestamp");

        $obj->setDateStartFromString("2021-01-27 15:30:00");
        $obj->setDateEndFromString("2021-01-27 17:20:00");
        $obj->setSummary("Testování");

        $this->assertEquals([], $obj->validate());
    }

    public function test_validate_errors()
    {
        $obj = new VCalendarBuilder();

        /* deterministic mock values */
        $obj->setUid("uid");
        $obj->setDtstamp("timestamp");

        $errors = [
            "Missing date_start.",
            "Missing date_end.",
            "Missing summary.",
        ];

        $this->assertEquals($errors, $obj->validate());
    }

    public function test_getCalendarAsString_min_ok()
    {
        $obj = new VCalendarBuilder();

        /* deterministic mock values */
        $obj->setUid("uid");
        $obj->setDtstamp("timestamp");

        $obj->setDateStartFromString("2021-01-27 15:30:00");
        $obj->setDateEndFromString("2021-01-27 17:20:00");

        $obj->setSummary("Testování");

        $expected_result = "BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//intranet.mensa.cz//NONSGML VCalendar 1.0.0//CZ
BEGIN:VEVENT
UID:uid
DTSTAMP:timestamp
DTSTART;TZID=Europe/Prague:20210127T153000
DTEND;TZID=Europe/Prague:20210127T172000
SUMMARY:Testování
END:VEVENT
END:VCALENDAR";

        $this->assertEquals(
            str_replace("\r\n", "\n", $expected_result),
            str_replace("\r\n", "\n", $obj->getCalendarAsString())
        );
    }

    public function test_getCalendarAsString_full_ok()
    {
        $obj = new VCalendarBuilder();

        /* deterministic mock values */
        $obj->setUid("uid");
        $obj->setDtstamp("timestamp");

        $obj->setDateStartFromString("2021-01-27 15:30:00");
        $obj->setDateEndFromString("2021-01-27 17:20:00");

        $obj->setOrganizer("Mensa Česko", "<EMAIL>");
        $obj->setLocation("Krátká 123", "Praha");
        $obj->setSummary("Testování");
        $obj->setStatus("CONFIRMED");

        $expected_result = "BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//intranet.mensa.cz//NONSGML VCalendar 1.0.0//CZ
BEGIN:VEVENT
UID:uid
DTSTAMP:timestamp
DTSTART;TZID=Europe/Prague:20210127T153000
DTEND;TZID=Europe/Prague:20210127T172000
ORGANIZER;CN=Mensa Česko:mailto:<EMAIL>
LOCATION:Krátká 123, Praha
SUMMARY:Testování
STATUS:CONFIRMED
END:VEVENT
END:VCALENDAR";

        $this->assertEquals(
            str_replace("\r\n", "\n", $expected_result),
            str_replace("\r\n", "\n", $obj->getCalendarAsString())
        );
    }

    public function test_getCalendarAsString_date_offsets()
    {
        $obj = new VCalendarBuilder();

        /* deterministic mock values */
        $obj->setUid("uid");
        $obj->setDtstamp("timestamp");

        $obj->setDateStartFromString("2021-01-27 15:30:00");
        $obj->setDateEndFromString("2021-01-27 17:20:00");
        $obj->setDateStartOffsetInSeconds(1800);
        $obj->setDateEndOffsetInSeconds(3600);

        $obj->setSummary("Testování");

        $expected_result = "BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//intranet.mensa.cz//NONSGML VCalendar 1.0.0//CZ
BEGIN:VEVENT
UID:uid
DTSTAMP:timestamp
DTSTART;TZID=Europe/Prague:20210127T160000
DTEND;TZID=Europe/Prague:20210127T182000
SUMMARY:Testování
END:VEVENT
END:VCALENDAR";

        $this->assertEquals(
            str_replace("\r\n", "\n", $expected_result),
            str_replace("\r\n", "\n", $obj->getCalendarAsString())
        );
    }
}

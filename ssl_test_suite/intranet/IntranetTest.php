<?php

namespace intranet;

use database\model\mensasec\ModelFactory as MensasecModelFactory;
use database\model\mensaweb\ModelFactory as MensawebModelFactory;
use PHPUnit\Framework\MockObject\MockObject;
use test\MensaTestCase;

class IntranetTest extends MensaTestCase
{
    public function test_isDevelEnvironment_ok()
    {
        $config = $this->getConfigMock();
        $config->expects($this->any())->method("isDevelEnvironment")->will($this->returnValue(true));

        $obj = new Intranet($config);

        $this->assertTrue($obj->isDevelEnvironment());
    }

    public function test_isDevelEnvironment_false()
    {
        $config = $this->getConfigMock();
        $config->expects($this->any())->method("isDevelEnvironment")->will($this->returnValue(false));

        $obj = new Intranet($config);

        $this->assertFalse($obj->isDevelEnvironment());
    }

    public function test_isLiveEnvironment_ok()
    {
        $config = $this->getConfigMock();
        $config->expects($this->any())->method("isLiveEnvironment")->will($this->returnValue(true));

        $obj = new Intranet($config);

        $this->assertTrue($obj->isLiveEnvironment());
    }

    public function test_isLiveEnvironment_false()
    {
        $config = $this->getConfigMock();
        $config->expects($this->any())->method("isLiveEnvironment")->will($this->returnValue(false));

        $obj = new Intranet($config);

        $this->assertFalse($obj->isLiveEnvironment());
    }

    public function test_config()
    {
        $obj = new Intranet($this->getConfigMock());

        $this->assertInstanceOf('\config\ConfigInterface', $obj->getConfig());
    }

    public function test_mailer()
    {
        $obj = new Intranet($this->getConfigMock());
        $obj->setMailer($this->getMailerMock());

        $this->assertInstanceOf('\mailer\MailerInterface', $obj->getMailer());
    }

    public function test_mensasec()
    {
        $obj = new Intranet($this->getConfigMock());
        $obj->setMensasec($this->getMensasecModelFactoryMock());

        $this->assertInstanceOf('\database\model\mensasec\ModelFactory', $obj->getMensasec());
    }

    public function test_mensaweb()
    {
        $obj = new Intranet($this->getConfigMock());
        $obj->setMensaweb($this->getMensawebModelFactoryMock());

        $this->assertInstanceOf('\database\model\mensaweb\ModelFactory', $obj->getMensaweb());
    }

    public function test_vcalendar()
    {
        $obj = new Intranet($this->getConfigMock());
        $obj->setVcalendar($this->getVCalendarMock());

        $this->assertInstanceOf('\vcalendar\VCalendarInterface', $obj->getVcalendar());
    }

    /**
     * @return MensasecModelFactory|MockObject
     */
    protected function getMensasecModelFactoryMock()
    {
        return $this->getMockBuilder('\database\model\mensasec\ModelFactory')->disableOriginalConstructor()->getMock();
    }

    /**
     * @return MensawebModelFactory|MockObject
     */
    protected function getMensawebModelFactoryMock()
    {
        return $this->getMockBuilder('\database\model\mensaweb\ModelFactory')->disableOriginalConstructor()->getMock();
    }
}

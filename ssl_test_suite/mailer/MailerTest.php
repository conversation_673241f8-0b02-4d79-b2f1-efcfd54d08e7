<?php

namespace mailer;

use test\MensaTestCase;

class MailerTest extends MensaTestCase
{
    public function test_getPhpMailer()
    {
        $php_mailer = $this->getPhpMailerMock();

        $obj = new Mailer($php_mailer);

        $this->assertInstanceOf('\PHPMailer\PHPMailer\PHPMailer', $obj->getPhpMailer());
    }

    public function test_sendDefaultMail_ok()
    {
        $php_mailer = $this->getPhpMailerMock();

        $php_mailer->expects($this->once())->method("isHtml");
        $php_mailer->expects($this->once())->method("addAddress");
        $php_mailer->expects($this->once())->method("addAttachment");
        $php_mailer->expects($this->once())->method("send");
        $php_mailer->expects($this->once())->method("clearAllRecipients");

        $obj = new Mailer($php_mailer);

        $obj->sendDefaultMail(
            "<EMAIL>",
            "from",
            ["to" => "<EMAIL>"],
            "subject",
            "<p>text</p>",
            ["file.pdf" => "attachment"],
            ["filestring" => "attachment"]
        );
    }

    public function test_sendIcalMail_ok()
    {
        $php_mailer = $this->getPhpMailerMock();

        $php_mailer->expects($this->once())->method("isHtml");
        $php_mailer->expects($this->once())->method("addAddress");
        $php_mailer->expects($this->exactly(2))->method("addStringAttachment");
        $php_mailer->expects($this->once())->method("send");
        $php_mailer->expects($this->once())->method("clearAllRecipients");

        $obj = new Mailer($php_mailer);

        $obj->sendIcalMail(
            "<EMAIL>",
            "from",
            ["to" => "<EMAIL>"],
            "subject",
            "<p>text</p>",
            ["string_content" => "attachment"],
            "ical content",
            "ical.ics"
        );
    }

    public function test_getDefaultMailer_ok()
    {
        $php_mailer = $this->getPhpMailerMock();

        $php_mailer->expects($this->once())->method("isHtml");
        $php_mailer->expects($this->once())->method("addAddress");
        $php_mailer->expects($this->once())->method("addAttachment");

        $obj = new Mailer($php_mailer);

        $mailer = $obj->getDefaultMailer(
            "<EMAIL>",
            "from",
            ["to" => "<EMAIL>"],
            "subject",
            "content",
            ["file.pdf" => "attachment"],
            ["filestring" => "attachment"]
        );

        $this->assertInstanceOf('\PHPMailer\PHPMailer\PHPMailer', $mailer);
    }

    public function test_getAutoSendMailer_ok()
    {
        $php_mailer = $this->getPhpMailerMock();
        $php_mailer->expects($this->once())->method("isHtml");

        $obj = new Mailer($php_mailer);

        $mailer = $obj->getAutoSendMailer("<EMAIL>", "from");

        $this->assertInstanceOf('\PHPMailer\PHPMailer\PHPMailer', $mailer);
    }
}

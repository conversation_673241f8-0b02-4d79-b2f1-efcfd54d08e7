<?php
$secDbHost = getenv("MENSASEC_HOST") ?: '127.0.0.1';
$webDbHost = getenv("MENSAWEB_HOST") ?: '127.0.0.1';

$secDbUser = getenv("MENSASEC_USER") ?: 'mensasec';
$webDbUser = getenv("MENSAWEB_USER") ?: 'mensaweb';

$secDbPort = getenv("MENSASEC_PORT") ?: 3306;
$webDbPort = getenv("MENSAWEB_PORT") ?: 3306;

$config_data = [
    "shared" => [
        "default_timezone" => "Europe/Prague",

        "live_ip_address_whitelist" => [
            "*************",  // "**************",
            "2a02:2b88:2:1::5bfb:1",
        ],
    ],

    "devel" => [
        "database" => [
            "mensasec" => [
                "dsn" => "mysql:dbname=mensasec;host={$secDbHost};port=" . $secDbPort,
                "user" => $secDbUser,
                "password" => getenv("MENSASEC_MAGIC"),
            ],
            "mensaweb" => [
                "dsn" => "mysql:dbname=mensaweb;host={$webDbHost};port=" . $webDbPort,
                "user" => $webDbUser,
                "password" => getenv("MENSAWEB_MAGIC"),
            ],
        ],
    ],
    "live" => [
        "database" => [
            "mensasec" => [
                "dsn" => "mysql:dbname=mensasec;host={$secDbHost};port=" . $secDbPort,
                "user" => $secDbUser,
                "password" => getenv("MENSASEC_MAGIC"),
            ],
            "mensaweb" => [
                "dsn" => "mysql:dbname=mensaweb;host={$webDbHost};port=" . $webDbPort,
                "user" => $webDbUser,
                "password" => getenv("MENSAWEB_MAGIC"),
            ],
        ],
    ],
];

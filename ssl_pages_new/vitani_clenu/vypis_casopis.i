<!-- <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->
<!-- <PERSON><PERSON><PERSON>ně v Mense vítáme tyto členy: -->
<?PHP
/**
    Vyp<PERSON><PERSON><PERSON> seznam členů, kte<PERSON><PERSON> se mají uvítat v časopise.
    
    Změnovník:
        2012-07-16, TK: Přidal možnost vložit uživatelské datum začátku.


    Princip:
        Od poloviny roku 2010 se eviduje datum zaslání p<PERSON>,
        to se bere jako datum vstupu do Mensy a vypíší se členové,
        kte<PERSON><PERSON> vstoupili po daném datu.
        
        Je tu drobný h<PERSON>ek: člověk se stane členem až po zaplacení
        příspěvku. Pokud tedy pošle přihlášku a zaplatí až později, můž<PERSON> se stát,
        že jeho datum vstupu "propadne" - m<PERSON><PERSON> v<PERSON>j<PERSON> v minulém časopise,
        ale o ještě nebyl člen a pro tento časopise je moc staré.
        
        To se bohužel musí řešit/hlídat manuálně.
*/

// dekóduj datum
// nejprve přirav default
// ulozen extra, protoze se na to same datum budeme jeste jednou ptat
$date_string  = " DATE_SUB(CURRENT_DATE(), INTERVAL 56 DAY) ";

// pokud je uživatelské datum korektní, použij toto
if (isset($_POST['datum']))
{
    // print_r($_POST['datum']);

    // over spravnost a rovnou rozkouskuj udaje do pole    
    $matches = Array();
    if (preg_match ('/(?P<den>\d{2})\. (?P<mesic>\d{2})\. (?P<rok>\d{4})/', trim($_POST['datum']), $matches) === 1)
    {
        // print_r($matches);
        $date_string  = " '{$matches['rok']}-{$matches['mesic']}-{$matches['den']}' ";
    
        // over zda datum neni moc stare (starsi nez pul roku)      
        // musi byt zadano znovu, bez uvozovek !
        if (trim(strtotime("{$matches['rok']}-{$matches['mesic']}-{$matches['den']}")) < (time() - 130*24*60*60)) 
            die ("Zvolene datum je prilis stare!"); 
    }    
}

// zjistit datum od ktereho se jmene vypisuji
$result = $db->Query("SELECT DATE_FORMAT(CONVERT(".$date_string.", DATETIME), '%e. %c.') as datum FROM dual");
$datum = $result->fetch_object()->datum;

// datum pro input box
$result = $db->Query("SELECT DATE_FORMAT(CONVERT({$date_string}, DATETIME), '%d. %m. %Y') as datum FROM dual");
$datum_pole = $result->fetch_object()->datum;

// clenove
$novi_clenove_sql = "SELECT 
			jmeno, prijmeni, psc, mesto, public  
		FROM
			mensaweb.m_members m
			LEFT OUTER JOIN m_members_priznaky ON (id_m = intranet_id_m)
		WHERE
			(datum_vstupu >= ( {$date_string} ))     	-- nove vstoupil
			AND (ukonceni_clenstvi_poplatky = 0)	-- nema ukoncene clenstvi kvuli neplaceni
			AND (nevitat_v_casopise = 0)			-- nezakazal si vitani v casopise
			AND (clencislo_v_dm IS NULL)			-- nebyl clenem v DM (toho znovu vitat nebudeme)
		ORDER BY
			(trim(prijmeni)) ASC";

// proved dotaz
$vysledek = $db->Query($novi_clenove_sql);
if ($vysledek === FALSE) die("Nastala chyba pri pristupu k databazi. " .$db->error);

$pocet = $db->getNumRows($vysledek);
if($pocet==0) die ("<p>Nebyl nalezen žádný záznam.</p>");

?>


<h3>Srdečně vítáme v Mense <?= $pocet; ?> nových členů</h3>

<p><i>
Následující členové vstoupili do Mensy od <?= $datum ?> do <?PHP echo date("j. n."); ?> V seznamu se objeví pouze ti, kteří zaslali přihlášku, zaplatili příspěvek i registrační poplatek a souhlasili s uvedením v časopise.
</i></p>



<form method="post" action="./index.php?men=men25.1.0.0">
<p>Jiné datum začátku výpisu: <input type="text" name="datum" value="<?= $datum_pole ?>" size="9" >. 
Prosím, dodržte formát DD. MM. YYYY, lze zvolit datum maximálně půl roku nazpět.
<input type="submit" name="Odeslat" value="Změnit">
</p>
</form> 



<table border="0">
	<tr>
		<th>Jméno</th>
		<th>Příjmení</th>
		<th>Obec</th>
	</tr>

<?PHP
	// vypis tabulku
	while($row=$db->FetchArray($vysledek))
	{
		?>
		<tr>
			<td><?PHP echo $row["jmeno"]; ?></td>
			<td><?PHP echo $row["prijmeni"]; ?></td>
			<td><?PHP // pokud je profil verejny, uved mesto
					  if($row["public"] == 'Y'){ echo $row["mesto"];}  ?></td>
		</tr>
		<?PHP
	}	
?>
</table>

<p><b>Poznámka:</b>		
Aby se člen objevil v tomto výpise, musí nejprve zaslat přihlášku. 
Lidé, kteří pouze zaplatili, ale nepřihlásili se, se zde nevypisují.
</p>

<p>	
	Město je v tomto výpisu uvedeno pouze u lidí, kteří dali souhlas se zveřejněním své adresy na intranetu.
	Vyhledávání využívá data z profilů členů na intranetu, někteří členové si mohli svoji adresu z této databáze záměrně odstranit.
</p>

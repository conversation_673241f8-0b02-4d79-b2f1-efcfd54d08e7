<!-- utf-8 -->
<h1>Výpis pro vítače</h1>
<?PHP

// zapni vypis chyb
error_reporting(E_ALL);
ini_set('display_errors', 1);

/**
    Vyp<PERSON><PERSON>e seznam členů, kte<PERSON><PERSON> na které se mají vrhnout vítači.


    Princip:
        Od poloviny roku 2010 se eviduje datum zaslání <PERSON>,
        to se bere jako datum vstupu do Mensy a vypíší se členové,
        kte<PERSON><PERSON> vstoupili po daném datu.

        Je tu drobný háček: člověk se stane členem až po zaplacení
        příspěvku. Pokud tedy pošle přihlášku a zaplatí až později, může se stát,
        že jeho datum vstupu "propadne" - mělo vyjít v minulém časopise,
        ale o ještě nebyl člen a pro tento časopise je moc staré.

        To se bohužel musí řešit/hlídat manu<PERSON>lně.


    Změnovník:
        2015-02-08, TN: Doplněna možnost označit zda byl dotyčný přivítán.
        2012-08-16, TK: Přidal možnost vložit uživatelské datum začátku a nově zformátoval výpis.
*/




// zachycení uživatelského data
// akontrola správnosti
if (isset($_POST['dny']))
{
    // násilné přetypování na int
    $dny = (int) $_POST['dny'];
    // kontrola správné hodnoty
    if (!($dny > 1 and $dny < 160)) $dny = 61;
}
else
    $dny = 61;



?>



<form method="post" action="./index.php?men=men25.2.0.0">
<p>Zde máte k dispozici výpis všech nových členů Mensy za poslední
<select id='dny' name='dny' onChange='submit();'>
    <option value='14'  <?php if($dny == 14)  echo "selected='selected'"; ?> >14 dnů</option>
    <option value='31'  <?php if($dny == 31)  echo "selected='selected'"; ?> >1 měsíc</option>
    <option value='61'  <?php if($dny == 61)  echo "selected='selected'"; ?> >2 měsíce</option>
    <option value='122' <?php if($dny == 122) echo "selected='selected'"; ?> >4 měsíce</option>
    <option value='153' <?php if($dny == 153) echo "selected='selected'"; ?> >5 měsíců</option>
</select>.
Prosím, nakládejte s daty zodpovědně a
respektujte soukromí členů. Obzvláště opatrně postupujte u lidí, kteří si některé z kontaktních údajů z databáze záměrně odstranili.
Jakožto „vítači“ jste ambasadory Mensy před novými členy a často jste první mensovní tváří, se kterou se setkají. Na Vaší práci
opravdu záleží. Prosím, přistupujte k ní pečlivě a s rozvahou.
</p>
</form>









<?PHP

/**
    Ulozi priznak toho, ze dotycny byl uvitan
*/
function runMyFunction() {
    global $db;
    $id=(int) $_GET['zpracovat'];
    $SQL = "UPDATE mensaweb.m_members SET vitan = 1 WHERE id_m = {$id}";
    if ($db->Query($SQL)) echo "<p>Záznam uložen.</p>";
}
if (isset($_GET['zpracovat'])) {
    runMyFunction();
}


// tato podmínak se použije při výběru data
$date_string = " DATE_SUB(CURRENT_DATE(), INTERVAL {$dny} DAY) ";

// vypočti datum, od kdy se lidé zobrazují
$datum=$db->FetchAssoc($db->Query("SELECT DATE_FORMAT({$date_string}, '%e. %c. %Y') as datum"));
?>


<h3 style="margin-top: 2em;">Popis sloupců</h3>

<p>
<b>Vstoupil:</b> Datum udává, kdy člen zaslal přihlášku do Mensy. Tento údaj je nicméně ošemetný, protože
ve výpisu pro vítání se objeví člen až poté, co zašle přihlášku a také zaplatí.
Pokud někdo zaplatí se zpožděním, může se nově objevit ve výpisu, ač má datum vstupu klidně dva měsíce staré.
</p>

<p>
<b>Přihlášení:</b> Datum udává, kdy se člen naposledy přihlásil do intranetu. Prosím, připomeňte členům, kteří se nepřihlásili,
že tak mají učinit a dle výpisu zkontrolujte, zda se tak stalo a případně upomínejte dále.
</p>

<p><b>Přivítán</b> (evidence vítání):
	po odeslání vítacího mailu, vítacím telefonátu nebo podobně můžete kliknutím na slovo "odfajfkovat" v příslušném
	řádku potvrdit přivítání, čímž se hodnota ve sloupci "Přivítán" změní na "ano". Tak můžete snadno sledovat,
	kdo již přivítán byl a kdo ne a snáze se tak podělit o práci. :)
</p>

<p>Modře jsou zvýrazněni členové Dětské Mensy. Prosím, mějte na paměti, že často komunikujete s rodičem.</p>


<h3 style="margin-top: 2em;">Následující lidé se stali členy Mensy po <?= $datum['datum'] ?></h3>
<table border="0">
	<tr>
		<th>Čl. č.</th>
		<th>Typ</th>
		<th>Vstoupil</th>
		<th>Jméno</th>
		<th>Věk</th>
		<th>Obec</th>
		<th>PSČ</th>
		<th>Kraj</th>
		<th>Email</th>
		<th>Profil</th>
		<th>Přihlášení</th>
		<th>Přivítán</th>
	</tr>
<?PHP


// vyber nove cleny,
// zazna upraven za poslednich 65 dnu
// pouze dospele Mensy (typ = 1)
// preskocit nizka clenska cisla (heuristika proti zobrazovani drivejsich clenu)
// ulozen extra, protoze se na to same datum budeme jeste jednou ptat
// m.id_m je vytazerne extra do specialniho sloupce zpracovat, duvod je totiz ten, ze v dalsich join tabulkach
// je tez sloupec id_m, ktery prekrije ten puvodni, avsak tim ze to je left outer join, tak nemusi byt vzdy pritomen
$SEL = "SELECT
			*, m.id_m as zpracovat,
			DATE_FORMAT(datum_vstupu, '%d. %m.') as vstoupil,
			if(cas IS null, 'nikdy', DATE_FORMAT(cas, '%d. %m.'))  as prihlaseni

		FROM
			(m_members m LEFT OUTER JOIN m_list_kraj k ON (k.id_kraj = m.kraj))
			LEFT OUTER JOIN m_members_priznaky p ON (m.id_m = p.intranet_id_m)
			LEFT OUTER JOIN m_members_login l  ON (l.id_m = m.id_m)
		WHERE
				(datum_vstupu > ({$date_string})) 	-- nove vstoupil
			AND ukonceni_clenstvi_poplatky = 0			-- nema ukoncene clenstvi
			AND (nevitat_clenem = 0 OR nevitat_clenem IS NULL)   	-- povolil si vitani
			AND clencislo_v_dm IS NULL					-- nebyl clenem v DM (toho znovu vitat nebudeme)
		ORDER BY
			psc ASC,
			prijmeni ASC";
$tmp = $db->Query($SEL);


if($db->getNumRows($tmp)==0)
{
	echo "<tr><td colspan='3'>Nebyl nalezen žádný záznam.</td></tr>";
} else
{
	// vypis tabulku
	while($row=$db->FetchAssoc($tmp))
	{
		?>

		<tr <?php
		      if($row["clen"] == 2) echo "style='color: blue; background-color: rgb(240, 240, 255);'";
            ?> >
			<td style="text-align:right;"><?PHP echo $row["clen_cislo"]; ?></td>
			<td style="text-align:right;"><?PHP
					if($row["clen"] == 2) echo "DM";
					else echo "M";
				?>
			</td>
			<td style="text-align:right;"><?PHP echo $row["vstoupil"]; ?></td>

			<td><?PHP echo $row["jmeno"]; ?> <?PHP echo $row["prijmeni"]; ?></td>

			<td style="text-align:right;"><?PHP
					if (is_numeric ($row["rok_narozeni"]) === TRUE)
					{
						$momentalni_rok = getdate();
						// spocti skutecny vek
						$vek = $momentalni_rok['year']  - $row["rok_narozeni"];

						// vyrob upraveny vek - zaokrouhli dolu na desitky pro volbu skupiny, stare php neumi mode pro funkci round
						$vek2 = floor (($vek-1) / 10) * 10;
						$vek2 = $vek2 . "-" . ($vek2+10);
						// uprava po lidi 17-20, zde je kazdy rok dulezity
						if ($vek <= 23) $vek2 = $vek;

						echo ($vek2);
					}
				?></td>
			<td><?PHP echo $row["mesto"]; ?></td>
			<td><?PHP echo strtr($row["psc"], Array(' '=>'&nbsp;')); ?></td>
			<td><?PHP if ($row["id_kraj"] != 15) echo $row["kraj_name"]; ?></td>
			<td><a href='mailto:<?PHP echo $row["email"]; ?>?subject=Vítejte%20v%20Mense'><?PHP echo $row["email"]; ?></a></td>
			<td>
				<?PHP
					if($row["public"] == 'Y')
					{
						echo "<a target='_new' href='./index.php?men=men4.1.0.0&s_content=view.i&id_m=" . $row["id_m"] . "'>profil</a>";
					}else
					{
						echo "neveřejný";
					}
				?>
			</td>
			<td style="text-align:right;"><?PHP echo $row["prihlaseni"]; ?></td>
			<td style="text-align:right;">


					<?PHP

			// 2015-02-08, Tomáš Nováček: indikace toho, zda již byl dotyčný vítán
			if ($row["vitan"]==1)
			    echo "ano";
			else {
			    // v poliz pracovat je ulozene id_m, event generovany kliknutim je zpracovan na zacatku stranky
				echo "<a href='./index.php?men=men25.2.0.0&zpracovat={$row['zpracovat']}'>odfajfkovat</a>";
			}

 ?>



</td>
		</tr>
		<?PHP
	}
}
?>

</table>

<p>
	<b>Poznámka:</b>

	<?PHP
		// zachovat velikost posledniho dotazu
		$pocet = $db->getNumRows($tmp);


		// vypsat udaje
		echo "Od {$datum['datum']} bylo nalezeno " . $pocet . " nových členů. Seznam je řazen dle PSČ. ";
	?>

	Vyhledávání využívá data z profilů členů na intranetu a někteří členové si mohli svoji adresu z této databáze záměrně odstranit.
	Kraj si členové musí sami nastavit.
</p>




<p style="text-align:right; margin-top:5em;"><em>Stránku naprogramoval Tomáš Kubeš 24. června 2012.</em></p>



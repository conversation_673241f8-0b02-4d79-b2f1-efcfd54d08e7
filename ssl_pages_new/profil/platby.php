<!--     <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->
<script src="/js/qrcode.js"></script>

<h1>Platby členských příspěvků</h1>
<style>
    p {
        text-align: justify;
    }

    #qr {
        float: right;
        margin: 1.5em;
        margin-top: 0;
    }

    #qr img {
        width: 200px;
    }

    ul#cenik>li{
        margin-top: 1em;
    }
</style>

<div id="qr"></div>



<?PHP
/**
 * Platby.php
 * Platby členských příspěvků
 *
 *
 * Změnovník
 * 2012-Jan-02, TK: Založeno vylčnením ze stránky 1.i v adresáři billboard
 */


// informace o zaplacení na tento rok
echo "
<p style='font-weight: bold;'>Členské příspěvky na rok " . date('Y') .
    " " . (($a_user['prizpevky'] >= date('Y')) ? "" : "<span style='color: red;'>ne</span>") .
    "máte uhrazeny, " .
    (($a_user['prizpevky'] >= date('Y')) ? "děkujeme" : ("prosíme, zaplaťte je nejpozději do 28. února " . date('Y'))) . ".</p>";



////////////////////////////////////////////////////////////////////////////////
/// nepokracuj dal, pokud uz ma clen zaplaceno a je moc brzo.
if ($a_user['prizpevky'] >= date('Y') and date('m') <= 9) return;




// vyndej jekolive necislice z clenskeho cisla
$clc_bez_znaku = trim(preg_replace("/[^\d]/", "", $a_user['clen_cislo']));

// poznámka: jakýkoliv aktivní účet v intranetu musí mít vyplněno pole příspěvky
// tj. není třeba řešit prázdnou hodnotu, buď bude minulý rok, nebo tento rok, jinak by uživatel neměl 
// mít možnost přihlásit se
$rok_placeni = ($a_user['prizpevky'] + 1);
$nahodne_cislo = sprintf("%02u", rand(0, 99));


// cenik
require_once "../ssl_pages_new/centr/sekretarka/platby-cenik.class.php";
?>

<h3 style='margin-top: 2em;'>Jak zaplatit členské příspěvky na rok <?php echo "{$rok_placeni}"; ?>?</h3>


<p style='font-weight: bold; color: blue; margin-top: 3em;'>Bankovním převodem</p>
<p>
    Zaplacení členského příspěvku je velmi snadné.
    Stačí převést peníze na účet Mensy u Fio banky platebním příkazem v elektronickém bankovnictví.
    Při placení použijte QR kód vpravo nebo následující údaje:
</p>

<ul id="cenik">
	<li><label for="cu">číslo účtu:</label>
	    <input id='cu' type='text' value='**********' size='12' readonly='readonly' > /
	    <input id='kb' type='text' value='2010' size='5' readonly='readonly'> (Fio banka)
	</li>
	<li>částka <b>do konce února</b>:
	    <ul>
	    <li><?php echo intranet\platby\ucet\Cenik::ROCNI_DOPORUCENE; ?> Kč (doporučené),</li>
	    <li><?php echo intranet\platby\ucet\Cenik::ROCNI_CLENSTVI; ?> Kč (základní),</li>
	    <li><?php echo intranet\platby\ucet\Cenik::DM_ROCNI_CLENSTVI; ?> Kč (snížený pro studenty a seniory),</li>
	    <li>5 000 Kč (zlatý – na podporu aktivit pro nadané děti),</li>
	    <li>máte-li více členů v rodině, prosím, spočtěte částku dle návodu na 
	        <a href='https://mensa.cz/stavajici-clenove/'>mensa.cz</a>,</li>
	    </ul>
	</li>
	<li>částka <b>od 1. března</b>:
	    <ul>
            <li><?php echo (intranet\platby\ucet\Cenik::ROCNI_CLENSTVI + intranet\platby\ucet\Cenik::ZPOZDNE); ?> Kč (základní),</li>
            <li><?php echo (intranet\platby\ucet\Cenik::DM_ROCNI_CLENSTVI + intranet\platby\ucet\Cenik::ZPOZDNE); ?> Kč (snížený pro studenty a seniory),</li>
	    <li>5 000 Kč (zlatý – na podporu aktivit pro nadané děti),</li>
	    <li>máte-li více členů v rodině, prosím, spočtěte částku dle návodu na 
	        <a href='https://mensa.cz/stavajici-clenove/'>mensa.cz</a>,</li>
	    </ul>
	</li>

    <li><label for="vs">variabilní symbol (VS):</label>
	    <input id="vs" type='text' value='<?php echo $clc_bez_znaku;?>' size='5' readonly='readonly'>
	    <b>NEZBYTNÉ</b> (Vaše členské číslo bez předpony)
	</li>
	<li><label for="zp">zpráva pro příjemce:</label>
        <input id='zp' type='text' readonly='readonly' value='<?php echo "{$a_user['clen_cislo']} {$a_user['jmeno']} {$a_user['prijmeni']}, rok {$rok_placeni}"?>' size='40'>
        (rozpis platby, členské číslo a případné další informace)
	</li>
</ul>




<?php
$payments_link = "https://payments.mensa.cz/?" . http_build_query([
        "paymentType" => "memberFee",
        "memberNumber" => $a_user['clen_cislo'],
        "name" => $a_user['jmeno'],
        "surname" => $a_user['prijmeni'],
        "email" => $a_user['email'],
        "mobile" => $a_user['mobil'],
        "address" => $a_user['ulice'],
        "city" => $a_user['mesto'],
        "zip" => $a_user['psc']
    ]);
?>

<p style='font-weight: bold; color: blue; margin-top: 3em;'>Platební kartou</p>
<p>
    <a target='_blank' href='<?php echo $payments_link;?>'>Přejděte na platební bránu Mensy Česko</a>
    (otevře se v novém okně).
</p>

<p>
    <i>Z platby kartou platí Mensa Česko relativně vysoký transakční poplatek, prosím,
       použijte tuto možnost pouze pokud nemůžete platit převodem.</i>
<p>



<p style='font-weight: bold;  margin-top: 4em;'>Obecné informace</p>

<p>Při placení za více osob najednou použijte jako VS členské číslo jedné osoby
a členská čísla dalších osob uveďte ve zprávě pro příjemce, případně v emailu zaslaném <a href='mailto:<EMAIL>?subject=Příspěvky%20<?php echo $a_user['clen_cislo'];?>'>sekretářce</a>.</p>

<p>Více o příspěvcích se dozvíte na veřejné stránce <a target='_new' href='https://mensa.cz/stavajici-clenove/'>Členské příspěvky</a>.</p>


<!-- QR code needs to be changed -> new bank account -->
<script>
    new QRCode(document.getElementById("qr"),
        {
            text: "SPD*1.0*ACC:CZ372010000000***********CC:CZK*X-KS:0308*X-VS:<?PHP echo $clc_bez_znaku ?>"
            , width: 512
            , height: 512
            , correctLevel: QRCode.CorrectLevel.M
        });
</script>

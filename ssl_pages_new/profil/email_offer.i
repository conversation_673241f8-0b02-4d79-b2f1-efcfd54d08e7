<?php
    /*
    // blokovani pristupu vsem mimo testovacich uzivatelu
    if ($a_user['id_m'] != 1547 &&  $a_user['id_m'] != 2188  &&  $a_user['id_m'] != 2350){
        echo "<h2>Omlou<PERSON><PERSON><PERSON> se, prob<PERSON><PERSON><PERSON>ž<PERSON><h2>";
        return;
    }*/
?>



<!--  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->
<h1>Váš e-mail @mensa.cz</h1>
<style>h2{margin-top: 2em;}</style>
<p>Všichni členové <PERSON>, kte<PERSON>í mají zaplacené členské příspěvky,
    mohou využívat prestižní e-mailovou adresu ve formátu <EMAIL>.
    Technicky lze tuto adresu provozovat buď jako přes<PERSON> (alias/forward)
    na Vaši existující e-mailovou schránku u jiného poskytovatele, či jako plnohodnotnou
    IMAP schránku fyzicky realizovanou prostřednictvím platformy Google Apps.</p>

<p>
<b style="color:red">
Upozorňujeme členy Dětské Mensy, že některé další služby spojené s Google (například Google+ ) jsou věkově limitovány, nejsou určeny např. pro uživatele mladší než 13 let.
Pokud při aktivaci udáte věk pod stanoveným limitem, dojde následně k zablokování celého účtu.</b>
</p>

<!--  style='color:red;'-->
<p>Při zániku vašeho členství v Mense (např. z důvodu nezaplacení
    členských příspěvků),
    dojde ke smazání Vaší e-mailové schránky včetně všech zpráv tam uložených.</p>

<!--
<p>Od srpna 2013 e-maily Mensy technicky zajišťuje služba Google Apps,
poštu tedy fyzicky nezpracovává server Mensy, ale servery společnosti Google. Toto
řešení nabízí výrazně vyšší spolehlivost, úložný prostor a uživatelský komfort než původní řešení.
Pokud z nějakého důvodu nechcete svěřovat svůj e-mail společnosti Google, můžete
svoji mensovní adresu realizovat jako přesměrování do vlastní schránky.
</p>
-->



<?php
/**
    Knihovna pro správu emiailů.
    Autoři: Pavel Vyhlídal a Tomáš Kubeš 2013-2014

    Změnovník:
    2014-Jul-20, TK: Nasazeno založení plnohodnotné schránky.
    2014-Jun-17, TK: Nasazeno do částečného provozu, zmigrováno na m_members_gmail.

*/




// debug
error_reporting(-1);
ini_set("display_errors", 1);
ini_set("html_errors", TRUE);
$PUBLIC_DATABASE_NAME = "mensaweb"; //  "test"; // pro pouziti ve vyvojarskem koutku
require_once "gapps-intranet.php"; // knihovny






////////////////////////////////////////////////////////////////////////////////
/*
 * Resetuj hesla.
 *
 * Při založení schránky helso ukládáme, aby bylo možné jej zobrazit nebo
 * zjistit, pokud by uživatel stránku opustil.
 *
 * Z bezpečnostního hlediska je to ovšem velké riziko. Proto chceme heslo po 24
 * hodinách smazat.
 *
 * Automatický trigger ale nemáme, a proto je to kontrolováno při každém
 * zobrazení této stránky.
 */
$mazani = $db->Query("UPDATE {$PUBLIC_DATABASE_NAME}.m_members_gmail SET password = NULL WHERE modified < DATE_SUB(date,INTERVAL 1 DAY)");
//echo "mazani: $mazani";







////////////////////////////////////////////////////////////////////////////////
/*
 * Ziskej navrzene user name.
 * Budto soucasne nebo nove, pokud tento uzivatel nic nema.
 *
 */


/**
 * Vrati true jen tehdy a pouze thedy, pokud je tento email volny nebo patri
 * tomuto uzivateli. Jinak vraci false.
 *
 * @global type $db
 * @param type $id_m
 * @param type $eml
 * @return boolean
 */
function isEmailOfThisUser($id_m, $eml)
{
    global $PUBLIC_DATABASE_NAME, $db;
    $tmp = $db->Query("SELECT * FROM {$PUBLIC_DATABASE_NAME}.m_members_gmail WHERE id_m != $id_m AND email_address like '$eml'");
    if ($db->getNumRows($tmp) === 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}


/**
 * Vrati username, ktere budto jiz patri tomuto uzivateli nebo je zatim volne.
 * Vrati false, pokud funkce selze.
 *
 * @param type $id_m
 * @param type $jmeno
 * @param type $prijmeni
 * @return string nebo false
 */
function getFreeUserName($id_m, $jmeno, $prijmeni)
{
    $em_jmeno = generateUserName($jmeno, $prijmeni);
    if ($em_jmeno == false) return false;

    $em_jmeno2 = $em_jmeno;

    $i = "";
    while (!isEmailOfThisUser($id_m, ($em_jmeno2 . $i))) {
        $i = $i + 1;
        $em_jmeno2 = $em_jmeno . $i;
    }
    return $em_jmeno2;
}

/*
// test case
//$tmp = $db->Query("SELECT first_name, last_name FROM {$PUBLIC_DATABASE_NAME}.m_members_gmail");
//while ($a = $db->FetchArray($tmp, MYSQL_ASSOC))
//    echo getFreeUserName (0, $a['first_name'], $a['last_name']) . '<br>';
 * */



// zavolej hledani (user name potrebujeme pro dalsi zpracovani)
$userName = getFreeUserName($a_user['id_m'], $a_user['jmeno'], $a_user['prijmeni']);
if ($userName == false)
{
    echo "<p>Došlo k interní chybě, stránku nelze zobrazit, omlouváme se.</p>";
    send_email(INFO_MAIL, "CHYBA EMAIL USER NAME", "Pro id_m {$a_user['id_m']} ({$a_user['jmeno']} {$a_user['prijmeni']}) se nepodarilo vygenerovat uživatelské jméno!");
    return;
}







////////////////////////////////////////////////////////////////////////////////
// zpracuj pozadavek
if (isset($_POST['action']))
{
    echo "<h2>Aktualizace</h2>";
    switch (r_text("action")) {

        // smazani presmerovani
        case "del_fwd":
            echo "<p>Mazání přesměrování.</p>";
            deleteForwardForUser($a_user['id_m']);
            break;

        // zalozeni presmerovani
        case "new_fwd":
            echo "<p>Založení přesměrování.</p>";
            newForward($a_user['id_m'], /*R_text("email_address")*/ $userName, R_text("fwd_address"), $a_user['jmeno'], $a_user['prijmeni']);
            break;

        // libovolny novy uzivatel
        case "new_imap":
            echo "<p>Založení schránky.</p>";
            newUser($a_user['id_m'], /*R_text("email_address")*/ $userName, $a_user['jmeno'], $a_user['prijmeni'], rand_string(10));
            break;

        // default
        default:
            echo "<p>Neznámá akce. Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>?subject=Chyba%20emailu'><EMAIL></a></p>";
            return;
    }
}







////////////////////////////////////////////////////////////////////////////////
/*
 * Vypiš informace a zobraz menu akcí.
 * //echo "<p>Jste přihlášen jako {$a_user['jmeno']} {$a_user['prijmeni']}, členské číslo {$a_user['clen_cislo']}.
 * //    <!--V intranetu máte uvedenu poštovní adresu {$a_user['ulice']}, {$a_user['psc']}  {$a_user['mesto']}.--></p>";
 */
// vypis info o uctu a zaroven nacti typ
echo "<h2>Aktuální nastavení</h2>";
$typ = printUserInfo($a_user);






// doslo k zasadni chybe
if ($typ === false)
{
    return; // v tomto pripade nemame co delat, dal nechod
    // toto samo uz hodi hlasku
}
elseif ($typ === '0')// standardni schranka
{
    // vypis navod, protoze uzivatel ma plnohodnotny IMAP
    echo "<h2>Možné akce</h2>";
    echo "<h3>Smazat schránku</h3>";
    echo "<p>Pokud chcete svoji schránku zrušit, prosíme, napište na <a href='mailto:<EMAIL>?Subject=Zrušení%20emailu%20{$a_user['clen_cislo']}'><EMAIL></a>. Při zrušení budou smazány všechny zprávy! Při zániku vašeho členství schránku zrušíme automaticky.</p>";
    require 'email_offer.help.php';
    // zatim se neda nic delat
}
elseif ($typ === '3') // forward
{
    //echo "<p><b>Pokud chcete přesměrování zrušit, prosíme, napište na <a href='mailto:<EMAIL>?Subject=Zrušení%20přesměrování%20{$a_user['clen_cislo']}'><EMAIL></a>.</b></p>";
    // zatim se neda nic delat
    echo "<h2>Možné akce</h2>";
    echo "<h3>Změnit cíl přesměrování</h3>";
    echo "<p>Pokud chcete cíl přesměrování změnit, změňte si <a href='/index.php?men=men1.6.0.0'>cílový e-mail v intranetu</a>, následně přesměrování smažte a založte znovu.</p>";
    echo "<h3>Smazat přesměrování</h3>";
    echo "<p>Pokud přesměrování již nechcete nadále používat, můžete jej smazat.
    Tím se zruší předávání pošty došlé na mensovní adresu do cílové schránky.
    Pošta v cílové schránce nebude dotčena.
    Přesměrování musíte smazat, pokud chcete začít používat plnohodnotnou schránku.</p>";
    echo "<form name='delete_forward' method='POST' onsubmit='return confirm(\"Chcete opravdu zrušit vaše přesměrování?\");'><input type='submit' name='Smazat' value='Smazat přesměrování'><input type='hidden' name='action' value='del_fwd'></form>";
}
elseif ($typ === true) // uživatel nic nemá a je možné něco založit.
{
    echo "<p>Nemáte založen žádný e-mail v doméně @mensa.cz.</p>";


    echo "<h2>Možné akce</h2>";
    echo "<h3>Založení přesměrování</h3>
        <!--<p>Můžete si zřídit nové přesměrování z mensovní adresy <b>{$userName}@mensa.cz</b>
        do cílové schránky <b>{$a_user['email']}</b> (změnit <a href='/index.php?men=men1.6.0.0'>cílový e-mail</a>).</p>
            Při zřízení přesměrování nevznikne nová schránka, ale -->
     <p>Při zřízení přesměrování nevznikne nová schránka,
     ale všechna pošta zaslaná na adresu <b>{$userName}@mensa.cz</b>
     dorazí do schránky <b>{$a_user['email']}</b>.
     Cílová adresa je totožná s adresou, na kterou vám chodí pošta z intranetu,
     změnit ji můžete na stránce <a href='/index.php?men=men1.6.0.0'>cílový e-mail</a>.</p>";


    echo "<p><form name='create_forward' method='POST' onsubmit='return confirm(\"Chcete založit přesměrování?\");'>
     <input type='submit' name='Zalozit' value='Založit přesměrování'>
     <input type='hidden' name='fwd_address' value='{$a_user['email']}'>
    <input type='hidden' name='email_address' value='{$userName}'>
     <input type='hidden' name='action' value='new_fwd'>
     </form></p>";


    //       echo "<p>&nbsp;</p>
    echo "<h3 style='margin-top:2em;'>Založení plnohodnotné schránky</h3>";
    echo "<p>Založí novou plnohodnotnou schránku IMAP s adresou <b>{$userName}@mensa.cz</b> přístupnou přes moderní webové
        rozhraní nebo poštovního klienta na vašem počítači či v mobilu. Tuto schránku bude fyzicky spravovat společnost Google.
        <b style='color: red;'>Pro založení klikněte pouze JEDNOU.</b>
        </p>";
    //disabled='disabled'
    /*if ($a_user['id_m'] == 2350)*/
    echo "<p><form name='create_forward' method='POST' onsubmit='return confirm(\"Chcete založit schránku {$userName}@mensa.cz?\");'><input type='submit' name='Zalozit' value='Založit schránku'><input type='hidden' name='action' value='new_imap'><input type='hidden' name='email_address' value='{$userName}'></form></p>";
    //else echo "<p>Tento knoflík teprve vyvíjíme. Potřebujete-li zřídit plnohodnotnou schránku, kontaktujte <a href='mailto:<EMAIL>?subject=Zřízení%20emailu%20{$a_user['clen_cislo']}'>administrátora intranetu</a>.</p>";
}
else
{
    // neumíme
    echo "<p><b>Máte nestandardní schránku, kterou je třeba spravovat manuálně.</b></p>";
}
?>






<p  style="margin-top:4em;">
Pokud potřebujete speciální e-mail nebo individuální přesměrování (pořádáte akci, máte složité jméno, zaplatil jste zlaté členství atd.) nebo máte další dotazy,
prosíme, kontaktujte  <a href='mailto:<EMAIL>?subject=Správa%20emailu%20pro%20člč%20<?php echo $a_user['clen_cislo']; ?>'>administrátora intranetu</a>.</p>



<p style="margin-top:7em; text-align: right;"><i>
    Tuto stránku vytvořil Tomáš Kubeš na základě knihovny Pavla Vyhlídala, poslední úprava proběhla 19. července 2014.
    </i></p>

<h1>Nastavení Vašeho profilu v intranetu</h1>
<!--     <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->
<?PHP
/*

  Nastavení členského účtu

  Změnovník
  2013-05-20, TK: Zaveden<PERSON>novník<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, úprava textu souhlasu

 */
?>



<?PHP
/////////////////////////
// z<PERSON>pis <PERSON>lan<PERSON>

if (isset($zapsat))
{
    require_once("../ssl_library_new/edit_member.l");
    $datum = date("Y-m-d");
    $EDIT = "update m_members set public='" . addslashes($souhlas) . "', titul='" . addslashes($titul) . "', titul_za_jmenem='" . addslashes($titul_za_jmenem) . "', prezdivka='" . addslashes($prezdivka) . "',
		 tel_d='" . addslashes($tel_d) . "', rok_narozeni='" . addslashes($rok_narozeni) . "', fax_d='" . addslashes($fax_d) . "',
		  tel_p='" . addslashes($tel_p) . "', fax_p='" . addslashes($fax_p) . "', www='" . addslashes($www) . "',
		   mobil='" . addslashes($mobil) . "', sigh='" . addslashes($sigh) . "', ulice='" . addslashes($ulice) . "',
		    mesto='" . addslashes($mesto) . "', psc='" . addslashes($psc) . "', zamestnani=" . addslashes($zamestnani) . ",
			 zamestnani_pozn='" . addslashes($zamestnani_pozn) . "', owner='" . $a_user["id_m"] . "',
			 date='" . addslashes($datum) . "', kraj='" . addslashes($kraj) . "' WHERE id_m=" . $a_user["id_m"];
    edit_member($db, $EDIT);
    if ($mesto != $old_mesto OR $ulice != $old_ulice OR $psc != $old_psc)
    {
        $db->Query("DELETE FROM m_members_changed_addr WHERE id_m=$id_m");
        $db->Query("INSERT INTO m_members_changed_addr (id, id_m) VALUES ('', '$id_m')");
    }

    require_once("../ssl_library_new/delete_user_zajmy.l");
    delete_user_zajmy($db, $id_m);
    require_once("../ssl_library_new/insert_user_zajmy.l");
    $i = count($zajmy);
    while ($i--) {
        $j = $zajmy[$i];
        $string = "zajmy_" . $j;
        insert_user_zajmy($db, $id_m, $j, ${$string});
    }
    echo "<p align=\"center\" style=\"font-size=10; color=red;\">Vaše osobní údaje byly změněny.<br><br>";
}
require_once("../ssl_library_new/find_user.l");
$search = " m_members.id_m='" . $a_user["id_m"] . "'";
$a_user2 = find_user($db, 5, 0, $search);
$id_m = $a_user2[0]["id_m"];
?>








<p style="margin: 1em 0 1em 0;"><b>Upozornění</b></p>
<ul>
    <li>Jste platným členem Mensy do konce roku <?PHP echo $a_user2[0]["prizpevky"] ?>.</li>
    <?PHP
    if ($a_user2[0]["prizpevky"] < date("Y"))
        echo "<li>Prosím, <a href='./index.php?men=men1.8.0.0'>uhraďte</a> členské příspěvky nejpozději do 28. února " . date("Y") . ".</li>";
    ?>
    <li>Nezapomeňte provedené změny uložit.</li>
</ul>










<form action="index.php" method="post">
    <input type="hidden" name="id_m" value="<?PHP echo $id_m ?>">
    <input type="hidden" name="men" value="<?PHP echo $men ?>">
    <input type="hidden" name="old_mesto" value="<?PHP echo $a_user2[0]["mesto"] ?>">
    <input type="hidden" name="old_ulice" value="<?PHP echo $a_user2[0]["ulice"] ?>">
    <input type="hidden" name="old_psc" value="<?PHP echo $a_user2[0]["psc"] ?>">




    <table border="0" cellpadding="0" cellspacing="0">
        <tr>
            <td align="left" colspan="4">
                <p style="margin: 1em 0 1em 0;">
                    <b>Souhlas se zveřejněním základních údajů ostatním návštěvníkům intranetu</b>
                </p>

                <div style="width: 70em;">
                    <p>
                        <i>
                            Zveřejňuji k nahlížením ostatním členům Mensy
                            jméno, titul a přezdívku, telefon, email, www stránky,
                            adresu, rok narození, záliby a zaměstnání.
                            Tyto informace budou sloužit pouze pro interní potřebu
                            členů Mensy a to pouze po dobu mého členství.
                        </i>
                    </p>

                    <p>
                        <i>
                            Jestliže nechováte důvěru v ostatní členy nebo
                            si z jiného důvodu nepřejete,
                            aby se o Vás ostatní členové dozvěděli
                            jakékoliv další
                            informace, stačí nesouhlasit s tímto prohlášením.
                        </i>
                    </p>

                    <p>
                        <i>
                            Základním motivačním prvkem pro sdílení profilů
                            je usnadnění kontaktu mezi jednotlivými členy.
                            Děkujeme Vám za projevenou důvěru.
                        </i>
                    </p>
                </div>
            </td>
        </tr>




        <tr>
            <td  align="center"  colspan="4">
                <select name="souhlas">
                    <option value="Y"<?PHP if ($a_user2[0]["public"] == "Y") echo " SELECTED"; ?>  >SOUHLASÍM</option>
                    <option value="N"<?PHP if ($a_user2[0]["public"] == "N") echo " SELECTED"; ?>  >NESOUHLASÍM</option>
                </select>
            </td>
        </tr>


        <tr>
            <td align="left" colspan="4"><p style="margin: 2em 0 1em 0;"><b>Základní údaje</b></p>
                <p style="margin: 0 0 1em 0;">
                    Pro změnu jména, prosím, kontaktujte
                    <a href="mailto:<EMAIL>?subject=Změna%20jména">sekretářku</a>.
                    Email si změníte na stránce
                    <a href='/index.php?men=men1.6.0.0'>Změna emailu</a>.
                </p>
            </td>
        </tr>


        <tr>
            <td colspan="2"><p>Titul:&nbsp;<input type="text" name="titul" size="5" value="<?PHP echo $a_user2[0]["titul"] ?>"> <b><?PHP echo $a_user2[0]["jmeno"] . " " . $a_user2[0]["prijmeni"] ?></b>&nbsp;&nbsp;&nbsp;Titul za jménem:&nbsp;<input type="text" name="titul_za_jmenem" size="5" value="<?PHP echo $a_user2[0]["titul_za_jmenem"] ?>"></td>
            <td align="right"><p>Zaplaceno na rok:&nbsp;</td>
            <td><p><?PHP echo $a_user2[0]["prizpevky"] ?>&nbsp;&nbsp;&nbsp;Člen. číslo:&nbsp;<b><?PHP echo $a_user2[0]["clen_cislo"] ?></b></td>
        </tr>
        <tr>
            <td align="right"><p>Přezdívka:&nbsp;</td>
            <td><p><input type="text" name="prezdivka" value="<?PHP echo $a_user2[0]["prezdivka"] ?>"></td>
            <td align="right"><p>Email:&nbsp;</td>
            <td><p><?PHP echo $a_user2[0]["email"] ?></td>
        </tr>
        <tr>
            <td align="right"><p>Město:&nbsp;</td>
            <td><p><input type="text" name="mesto" value="<?PHP echo $a_user2[0]["mesto"] ?>"></td>
            <td align="right"><p>Ulice:&nbsp;</td>
            <td><p><input type="text" name="ulice" value="<?PHP echo $a_user2[0]["ulice"] ?>"></td>
        </tr>
        <tr>
            <td align="right"><p>Kraj:&nbsp;</td>
            <td><p><select name="kraj">
                        <?php
                        require_once("../ssl_library_new/show_kraj.l");
                        $a_kraj = show_kraj($db);
                        $i = count($a_kraj);
                        while ($i--) {
                            ?>
                            <option value="<?PHP echo $a_kraj[$i]["id"] ?>"<?PHP
                        if ($a_kraj[$i]["id"] == $a_user2[0]["kraj_cislo"]) echo " selected";
                            ?>><?PHP echo $a_kraj[$i]["kraj"]; ?></option>
                                    <?PHP
                                }
                                ?>
                    </select></td>
            <td align="right"><p>PSČ:&nbsp;</td>
            <td><p><input type="text" name="psc" value="<?PHP echo $a_user2[0]["psc"] ?>"></td>
        </tr>
        <tr>
            <td align="right"><p>Tel. domů:&nbsp;</td>
            <td><p><input type="text" name="tel_d" value="<?PHP echo $a_user2[0]["tel_d"] ?>"></td>
            <td align="right"><p>Fax domů:&nbsp;</td>
            <td><p><input type="text" name="fax_d" value="<?PHP echo $a_user2[0]["fax_d"] ?>"></td>
        </tr>
        <tr>
            <td align="right"><p>Tel. do práce:&nbsp;</td>
            <td><p><input type="text" name="tel_p" value="<?PHP echo $a_user2[0]["tel_p"] ?>"></td>
            <td align="right"><p>Fax do práce:&nbsp;</td>
            <td><p><input type="text" name="fax_p" value="<?PHP echo $a_user2[0]["fax_p"] ?>"></td>
        </tr>
        <tr>
            <td align="right"><p>Zaměstnání:&nbsp;</td>
            <td><p><select name="zamestnani">
                        <?PHP
                        require_once("../ssl_library_new/show_zamestnani.l");
                        $a_zam = show_zamestnani($db);
                        $i = count($a_zam);
                        while ($i--) {
                            ?>
                            <option value="<?PHP echo $a_zam[$i]["id"] ?>"<?PHP
                        if ($a_zam[$i]["id"] == $a_user2[0]["zamestnani_cislo"])
                        {
                            echo " selected";
                        }
                            ?>><?PHP echo $a_zam[$i]["zamestnani"] ?></option>
                                    <?PHP
                                }
                                ?>
                    </select></td>
            <td align="right"><p>Poznámka zaměst.:&nbsp;</td>
            <td><p><input type="text" name="zamestnani_pozn" value="<?PHP echo $a_user2[0]["zamestnani_pozn"] ?>"></td>
        </tr>
        <tr>
            <td align="right"><p>Mobil:&nbsp;</td>
            <td><p><input type="text" name="mobil" value="<?PHP echo $a_user2[0]["mobil"] ?>"></td>
            <td align="right"><p>Rok narození:&nbsp;</td>
            <td><p><input type="text" name="rok_narozeni" value="<?PHP echo $a_user2[0]["rok_narozeni"] ?>"></td>
        </tr>
        <tr>
            <td align="right"><p>Účast SIGHT:&nbsp;</td>
            <td><p><select name="sigh">
                        <option value="Y"<?PHP if ($a_user2[0]["sigh"] == 'Y') echo " SELECTED" ?>>Chci se zúčastnit</option>
                        <option value="N"<?PHP if ($a_user2[0]["sigh"] == 'N') echo " SELECTED" ?>>Ne</option></select>
                    <br>
                    Více o SIGHT na
                    <a href="http://www.mensa.cz/mensa/sight/" target="_blank">webu Mensy</a>.

            </td>
            <td align="right"><p>WWW:&nbsp;http://</td>
            <td><p><input type="text" name="www" value="<?PHP echo $a_user2[0]["www"] ?>"></td>
        </tr>







        <tr>
            <td align="left" colspan="4"><p style="margin: 2em 0 1em 0;"><b>Příjem zpráv Mensy</b></p>
                <div style="width: 70em;">


                    <?php
                    require_once('../ssl_pages_new/auto_email2/config.i');
                    require_once('../ssl_pages_new/auto_email2/konference.config.i');



                    // pomocná funkce z autosend
                    function auth_send($typ)
                    {
                        global $send_auth, $send_disable, $typy, $a_user;
                        //$typ: array( 0=> typ zpravy (oz|ko), 1=> podtyp = id obecne zpravy/konference)
                        switch ($typ[0]) {
                            case 'oz':
                                if ($send_disable)
                                    return false;
                                $id_m = (int) $a_user['id_m'];
                                if (isset($send_auth[$typ[1]][$id_m]))
                                    return (bool) $send_auth[$typ[1]][$id_m]; //individual setting
                                if (isset($send_auth[0][$id_m]))
                                {
                                    return (bool) $send_auth[0][$id_m]; //master setting
                                }
                                return (bool) $typy[$typ[1]]['default_auth']; //default type setting
                            case 'ko':
                                $auth_konfs = get_auth_konfs();
                                return (isset($auth_konfs[$typ[1]]));
                        }
                    }




                    $q_typy = $db->query("SELECT id_type, type_name, default_send, default_auth FROM m_ae_types ORDER BY id_type");
                    $typy = $db->FetchAssocKey($q_typy, 'id_type');
                    $zpravy = Array();
                    foreach ($typy as $typ)
                    {
                        if (!auth_send(array('oz', $typ['id_type'])))
                            continue;
                        $zpravy[] = $typ['type_name'];
                    }
                    if (empty($zpravy))
                        $zpravy[] = "žádné";
                    sort($zpravy);
                    echo '<p>Odebíráte následující typy mensovních zpráv: ' . implode('; ', $zpravy) . '.</p>';



                    // pomocná funkce zkopírovaná z autosend
                    function get_auth_konfs()
                    {
                        global $a_user, $db, $konference, $auth_konfs;
                        if (isset($auth_konfs))
                            return $auth_konfs; //cached result
                        $SQL = "SELECT id_konf FROM m_ae_konfcfg WHERE id_m={$a_user['id_m']} AND banned=0";
                        $q_konf_loggedin = $db->query($SQL);
                        $konf_in = array();
                        while ($k = $db->FetchRow($q_konf_loggedin)) {
                            list($konf_in[]) = $k;
                        }
                        $auth_konfs = array();
                        foreach ($konference as $id => $konf)
                        {
                            if (isset($konf['auth_send']))
                            {
                                //pouze pro vybrane
                                $can_send = (is_numeric($konf['auth_send']) && $a_user['id_m'] == $konf['auth_send'])
                                    || (is_array($konf['auth_send']) && in_array($a_user['id_m'], $konf['auth_send']));
                            }
                            else
                            {
                                //pro prihlasene do konference

                                $can_send = (($konf['type'] == KONF_MEN) != in_array($id, $konf_in));
                            }
                            if ($can_send)
                            {
                                $auth_konfs[$id] = $konf;
                            }
                        }
                        return $auth_konfs;
                    }

                    $auth_konfs = get_auth_konfs();
                    $konference = Array();
                    if (!empty($auth_konfs))
                        foreach ($auth_konfs as $id => $konf)
                            $konference[] = $konf['name'];
                    if (empty($konference))
                        $konference[] = "žádné";
                    sort($konference);
                    echo '<p>Odebíráte následující mensovní konference: ' . implode(', ', $konference) . '.</p>';
                    ?>


                    <p>Příjem zpráv i konferencí si můžete nastavit na stránkách:
                        <a href="/index.php?men=men20.1.0.0">Příjem zpráv</a> a
                        <a href="/index.php?men=men20.2.0.0">Příjem konferencí</a>.
                    </p>

                </div>
            </td>
        </tr>










        <tr>
            <td align="left" colspan="4"><p style="margin: 2em 0 1em 0;"><b>Záliby</b></p></td>
        </tr>


        <tr>
            <td colspan="4">



                <table border="0" cellpadding="0">
                    <?PHP
                    require_once("../ssl_library_new/show_zajmy.l");
                    require_once("../ssl_library_new/show_user_zajmy.l");
                    $a_m_zajmy = show_user_zajmy($db, $a_user2[0]["id_m"]);
                    $a_zajmy = show_zajmy($db);
                    for ($i = 0; $i < count($a_zajmy); $i = $i + 2)
                    {
                        ?>
                        <tr>
                            <td><input type="checkbox" name="zajmy[]"
                                       value="<?PHP echo @$a_zajmy[$i]["id_l_z"] ?>"
                                       <?PHP
                                       if (!empty($a_m_zajmy[$a_zajmy[$i]["id_l_z"]]))
                                       {
                                           echo " CHECKED";
                                       }
                                       ?>></td>

                            <td><p><?PHP echo @$a_zajmy[$i]["name"] ?>:&nbsp;</td>

                            <td><input type="text" name="zajmy_<?PHP echo $a_zajmy[$i]["id_l_z"] ?>"
                                       value="<?PHP echo @$a_m_zajmy[$a_zajmy[$i]["id_l_z"]]["pozn"] ?>">&nbsp;&nbsp;&nbsp;</td>
                            <td width="1" bgcolor="Black"><img src="images/pics1.gif" width="1" height="1" border="0" alt=""></td>


                            <?PHP
                            if (!empty($a_zajmy[$i + 1]["name"]))
                            {
                                ?>
                                <td><input type="checkbox" name="zajmy[]" value="<?PHP echo @$a_zajmy[$i + 1]["id_l_z"] ?>" <?PHP
                        if (!empty($a_m_zajmy[$a_zajmy[$i + 1]["id_l_z"]]))
                        {
                            echo " checked";
                        }
                                ?>></td>
                                <td><p><?PHP echo @$a_zajmy[$i + 1]["name"] ?>:&nbsp;</td>
                                <td><input type="text" name="zajmy_<?PHP echo $a_zajmy[$i + 1]["id_l_z"] ?>" value="<?PHP echo @$a_m_zajmy[$a_zajmy[$i + 1]["id_l_z"]]["pozn"] ?>">&nbsp;&nbsp;&nbsp;</td>
                                <?PHP
                            }
                            else
                            {
                                ?>
                                <td colspan="3">&nbsp;</td>
                            <?PHP } ?>

                        </tr>
                        <?PHP
                    }
                    ?>
                </table>




            </td>
        </tr>



        <tr>
            <td colspan="4"  align="center">
                <p style="padding: 1em; margin-top: 1em;">Poslední změna: <?PHP echo $a_user2[0]["date2"] ?></p>
                <p style="background-color: green; padding: 1em;"><input type="submit" name="zapsat" value="Zapsat"></p>
            </td>
        </tr>



    </table>
</form>
<h1>Změna emailu pro mensovní <PERSON></h1>
<?PHP

/*
    Žádost o změnu emailu - formulář pro odeslání ž<PERSON>dosti
    
    Změnovník
        2013-02-10, ZV: Doplnění JS na ověření mailu před odesláním.
        2012-05-13, TK: Kontrola, že je zadán platný email.
        2012-03-21, TK: Upraven text odesílaného emailu.
        2025-03-12, MD: SendGrid Mailer

*/
require_once("../ssl_library_new/put_email_confirm.l");

/**
    Zapíše žádost o změnu mailu a pošle ověřovací email.
    
    Vrátí TRUE, pokud všechno proběhlo, jak mělo.
*/
function zadej_zmenu_mailu($new_email)
{
    $new_email = trim($new_email);

    // dostal jsem platny email
    // zaroven efektivne zabrani sql injection :) protoze neprojde nic skodl<PERSON>
    if (! preg_match("/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/", $new_email))
    {
        echo "<p style='color: red; font-weight: bold; margin: 3em; text-align: center;'>
            Zadali jste neplatnou emailovou adresu: '$new_email', 
            prosím, zkuste to znovu a lépe.</p>";
        return FALSE;
    }
    
    // vše ok, můžeme
    global $db, $a_user;

    // vygeneruj ID pro potvrzení
	$ID_UNIQ=uniqid("", 50);


	// sestave email
	$body = "Hezký den!

Uživatel {$a_user['jmeno']} {$a_user['prijmeni']} požádal o přesměrování elektronických
zpráv z intranetu Mensy Česko na adresu $new_email.

Pokud chcete tuto změnu potvrdit, prosím, zadejte do svého prohlížeče odkaz:
https://intranet.mensa.cz/confirm_email.php?id=$ID_UNIQ

Ihned po potvrzení začnou všechny zprávy z intranetu Mensy 
chodit na adresu $new_email.

* * *

Pokud změnu provést nechcete, nemusíte nic dělat, 
požadavek za 12 hodin automaticky vyprší.

Pokud se jedná o obtěžování, prosím nahlaste tento 
incident na: <EMAIL>.


S pozdravem
iRada Mensy Česko
";

    // headers musí začína na prvním znaku
    $headers = "From: \"Intranet Mensy CR\" <<EMAIL>>
Bcc: <EMAIL>	
Return-Path: <EMAIL>
Content-Type:text/plain;charset=\"utf-8\"
";

    // pošli email a věř, že je vše ok
    $mailer = new mailer\Mailer();
    $sent = $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $new_email, "Potvrzeni zmeny e-mailu v intranetu", $body, $headers);

    if (! $sent)
    {
        echo "<p style='color: red; font-weight: bold; margin: 3em; text-align: center;'>
            Nezdařilo se odeslat potvrzovací email.</p>";
        return FALSE;
    }

    // je vše ok, potvrď.
    echo "<p style='color: blue; font-weight: bold; margin: 3em; text-align: center;'>
        Email pro potvrzení změny byl odeslán na Vaši novou adresu: $new_email. Děkujeme.</p>";

    // zapiš do db.
	put_email_confirm($db, $new_email, $ID_UNIQ, $a_user["id_m"]);

    // vše ok
    return TRUE;
}






// má se zobrazit formulář
$formular = TRUE;



// pokud byla zaslána mailová adresa, požádej o změnu
// pokud změna proběhla kladně, nezobrazuj znovu formulář
if (isset($_POST['active_email'])) $formular = !zadej_zmenu_mailu($_POST['new_email']);


// zobraz formulář, pokud je to třeba	
if ($formular) { 
?>
<script type="text/javascript" language="JavaScript">
  function checkemail(key)
  {
    var email = key;
    var filter = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
    if (!filter.test(email.value))
    {
     alert('Zadaná adressa je v neplatném formátu, změnu nelze odeslat.');
     email.focus();
     return false;
    }
  }
</script>


<form action="index.php" method="post" name="formcheck" onsubmit="return checkemail(document.formcheck.new_email);">
    <input type="hidden" name="men" value="<?PHP echo $men ?>">
    <p>Nastavte si, na jakou adresu Vám budou chodit zprávy Mensy.</p>

    <p>Jaké zprávy Vám mají nebo nemají chodit si můžete vybrat
        na stránkách <a href="index.php?men=men20.2.0.0">Příjem konferencí</a> a <a href="index.php?men=men20.1.0.0">Příjem zpráv</a>. Pokud nechcete
        žádné zprávy Mensy dostávat, prosím, i tak si nastavte platný email
        a posléze se odhlaste z odběru všech zprváv.</p>


    <p>Na zadanou emailovou adresu Vám přijde potvrzovací zpráva.
        V ní naleznete link, který vložíte do prohlížeče.
        Teprve potom Váš nový email bude aktivován.
        Pokud tak neučiníte do 12 hodin nebo opětovně požádáte o změnu emailu, bude tato žádost zrušena.
        Nový email bude platit až po potvrzení přes došlý link.
    </p>


    <table border="1" cellpadding="0" align="center">
        <tr>
            <td><p><strong>Současný email:</strong> <?PHP echo $a_user["email"] ;?></p></td>
        </tr>
        <tr>
            <td align="center"><input type="text" name="new_email" size="40"></td>
        </tr>
        <tr>
            <td align="center"><input type="submit" name="active_email" value="Změnit email"></td>
        </tr>
    </table>
</form>

<?PHP
}
?>


<p style="text-align:right; margin-top:8em;"><em>Stránku upravovali Tomáš Kubeš, Zbyněk Vaculík a Martin Dub, poslední úprava 12. března 2025.</em></p>

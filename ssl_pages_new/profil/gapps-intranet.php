<?php
/**
 * Knihovna obsahujíc<PERSON> funkce maskující volaní GAPPS pro intranet.
 * (dodate<PERSON>n<PERSON> kontroly a propojení s naší interní evidenční tabulkou).
 *
 * Toto staví na knihovně Pavla <PERSON> a je doplněno o kontroly, které
 * připravil tom<PERSON>.
 *
 *
 * Změnovník
 *  2014-Jul-20, TK: Založeno vyčleněním z email_offer.i
 *
 */




// podkladové knihovny
require_once("../ssl_library_2013/M_lib.php"); //formulare, post, html
//require_once("../ssl_library_2013/M_sql.php"); //sql tab
//define('GAPPS_PASSWORD2', '673LUKD');
require_once("../ssl_library_2013/M_gapps.php"); //vytvori objekt $gapps (Google Apps)
define('INFO_MAIL', '<EMAIL>'); //Kam se budou posilat info maily

// toto má definovat inkludující stránka podle toho, kde je používána
// $PUBLIC_DATABASE_NAME = "mensaweb"; //  "test"; // pro pouziti ve vyvojarskem koutku



/**
 * Vygeneruje nahodny retezec použitelný jako heslo.
 * http://stackoverflow.com/questions/6101956/generating-a-random-password-in-php
 *
 * @param type $length
 * @return type
 */
function rand_string( $length ) {
    $chars = "abcdefghijklmnopqrstuvwxABCDEFGHIJKLMNOPQRSTUVWX0123456789!@#$";
    return mb_substr(str_shuffle($chars),0,$length);

}




/**
 * Preved ciselny typ na textovou reprezentaci.
 * @param type $typ
 * @return string
 */
function decodeTyp ($typ)
{
    if (! is_numeric($typ)) return "bad argument";
    $typ = (int) $typ;
    if ($typ<0 || $typ > 6) return "neplatny typ";
    $mail_t=array(0=>"IMAP schránka",1=>"konferenční IMAP schránka",2=>"mensovní IMAP schránka", 3=>"přesměrování", 4=>"ALIAS", 5=>"MAIL LIST", 6=>"TEST");
    return $mail_t[$typ];
}







/**
 * Vypise informace o aktualnim stavu uctu uzivatele identifikovaneho pomoci id_m.
 * Vrati informaci o tom, co je to za ucet.
 *
 * @global string $PUBLIC_DATABASE_NAME
 * @global type $db
 * @param type $a_user
 * @return String - cislo udavajici typ uctu nebo boolean, true pokud není ucet nebo false pokud se vyskytla chyba.
 * Pozor, je treba pouzivat === aby se odlisilo false od 0 a 1 od true.
 */
function printUserInfo($a_user)
{
    global $PUBLIC_DATABASE_NAME, $db;
    // inicializuj Google Apps knihovnu
    $gapps = new M_gapps();


    // Ziskej informace o uzivateli z interni intranetove databaze uzivatelskych uctu.
    $uzivatelQ = $db->Query("SELECT * FROM {$PUBLIC_DATABASE_NAME}.m_members_gmail gmail WHERE gmail.id_m = {$a_user['id_m']}");
    $pocetDbRadek = $db->getNumRows($uzivatelQ);
    if ($pocetDbRadek < 1)
    {
        echo "<!-- <p>V naší databázi nemáme žádný záznam o e-mailu připojeném k vašemu intranetovému účtu.
        Pravděpodobně jej nemáte zřízen nebo byl zřízen manuálně.
        Pokud mensovní adresu již máte, prosíme, neprovádějte žádné akce na této stránce, hrozila by ztráta všech vašich dat,
        ale kontaktujte administrátora <a href='mailto:<EMAIL>?subject=Chyba%20emailu'><EMAIL></a>.</p>-->";

        // v tomto případě povol zřízení dalšího mailu
        // vime, ze ho nemame v databazi
        // na zakalde databaze tedy navrhni user name
        // pokud pro toto user name Google neco zna, jedna se o pruser, ktery vyzaduje zasah admina
        $userName = getFreeUserName($a_user['id_m'], $a_user['jmeno'], $a_user['prijmeni']);
        //echo $userName;
        if ($gapps->existName($userName) !== false)
        {
            echo "<p>V naší databázi nemáme žádný záznam o e-mailu připojeném k vašemu intranetovému účtu.
                Na serveru Google Apps nicméně existuje účet <b>{$userName}@mensa.cz</b>, pravděpodobně se jedná o chybu.
                Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>?subject=Chyba%20emailu'><EMAIL></a>. </p>";
            send_email(INFO_MAIL, "CHYBA PARU EMAIL 1", "Schranka {$userName}@mensa.cz pro id_m {$a_user['id_m']} neni v mensaweb.m_members_gmail ale JE na Googlu!");

            return false;
        }

        // v tomto pripade povol zalozeni noveho
        return true;
    }


    // vice radek v db. specialni ucty nebo chyba
    if ($pocetDbRadek > 1){
        echo "<p>V interní databázi (tabulka m_members_gmail) máme pro Váš účet <!--id_m {$a_user['id_m']}--> {$pocetDbRadek} záznamů. "
        . "Pro správu, prosíme, kontaktujte <a href='mailto:<EMAIL>?subject=Správa%20emailu%20člč%20{$a_user['clen_cislo']}'>administrátora</a>.</p>";
        echo "<p><b>Záznamy</b><br>";
        while ($radka = $db->FetchArray($uzivatelQ)) {
            echo "{$radka['email_address']}@mensa.cz, typ schránky: " . decodeTyp($radka['typ']) . "<br>";
        }
        echo "</p>";
        return false;
    }



    // nacti info u ozivateli z tabulky m_members_gmail
    //Array ( [email_address] => tomas.kubes [first_name] => Tomáš [last_name] => Kubeš [password] => * [id_m] => 1547 [typ] => 0 [alias] => [sent] => 2 [neclen] => 0 )
    $uzivatelR = $db->FetchAssoc($uzivatelQ);
    echo "<p>V mensovní databázi máme o vašem e-mailovému účtu následující údaje:</p>
            <ul><li>adresa: {$uzivatelR['email_address']}@mensa.cz</li>
            <li>typ: " . decodeTyp($uzivatelR['typ']) . "</li>";
    if ($uzivatelR['typ'] === "0" && !empty($uzivatelR['password'])) echo "<li>heslo: {$uzivatelR['password']} (Mensa heslo ukládá po dobu 24 hodin od založení schránky, pak jej smaže)</li>";
    echo   "<!--<li>interní id.: {$uzivatelR['id_m']}</li>--></ul>";




    // paranoic check
    if ($gapps->existName($uzivatelR['email_address']) === false)
    {
        echo "<p style='color: red;'>V mensovní databázi máme záznam odpovídající vašemu účtu, avšak server Google Apps tento účet nezná.
                Pokud problém přetrvá, prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>?subject=Chyba%20emailu'><EMAIL></a>.</p>";
        // 2024-05-13 vypnuto, temer vzdy totiz selze pri novém emailu, reakce
        // send_email(INFO_MAIL, "CHYBA PARU EMAIL 2", "Schranka {$uzivatelR['email_address']}@mensa.cz pro id_m {$a_user['id_m']} je v mensaweb.m_members_gmail ale NENI na googlu!");
        return false;
    }


    // zkus načíst informace o uživateli
    $user = $gapps->retrieveUser($uzivatelR['email_address']);
    if ($user)
    {
        // Array ( [Username] => tomas.kubes [Given_Name] => Tomáš [Family Name] => Kubeš [Suspended] => No )
        echo "<p>Server Google Apps potvrdil, že máte zřízenu plnohodnotnou mensovní e-mailovou IMAP schránku s adresou <b>{$uzivatelR['email_address']}@mensa.cz</b>.</p>";
        if ($user['suspended'] == false)
            echo ""; //echo "<p>Vaše schránka je aktivní.</p>";
        else
            echo "<p>Schránka <b>není</b> aktivní.</p>";

        // paranoic check - typ uzivatele musi odpovidat '0'
        if ($uzivatelR['typ'] !== '0')
        {
            echo "<p>Typ vašeho účtu v databázi Mensy nesouhlasí s typem účtu na serveru Google Apps.
                Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>?subject=Chyba%20emailu'><EMAIL></a>.</p>";
            send_email(INFO_MAIL, "CHYBA PARU EMAIL 3", "Schranka {$uzivatelR['email_address']}@mensa.cz pro id_m {$a_user['id_m']} ma ruzny typ mezi mensaweb.m_members_gmail a Google!");
            return false;
        }


        // zobraz, zda neni zrizen nick
        //$nick = retrieveNicknames($gdata, $uzivatelR['email_address']);
        if ($user["aliases"]) echo "<p>K Vaší adrese {$uzivatelR['email_address']}@mensa.cz je zřízen alias: " . $user["aliases"] ."</p>";


        return $uzivatelR['typ'];
    }


    // zkus načíst informace o skupině
    // skupiny používáme k realizaci přesměrování
    $group = $gapps->retrieveFwd($uzivatelR['email_address']);
    if ($group)
    {
        //print_r($group); // AnyoneFWDguillermo.blanco
        echo "<p>Server Google Apps potvrdil, že máte zřízeno aktivní přesměrování z adresy <b>{$uzivatelR['email_address']}@mensa.cz</b> na adresu <b>{$uzivatelR['alias']}</b>.</p>";
        //echo "";



        // paranoic check
        if ($uzivatelR['typ'] !== '3')
        {
            echo "<p>Typ vašeho účtu v databázi Mensy nesouhlasí s typem účtu na serveru Google Apps.
                Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>?subject=Chyba%20emailu'><EMAIL></a>.</p>";
            send_email(INFO_MAIL, "CHYBA PARU EMAIL 4", "Schranka {$uzivatelR['email_address']}@mensa.cz pro id_m {$a_user['id_m']} ma ruzny typ mezi intranetem a Google!");
            return false;
        }

        // !!!
        // @TODO: Tady musi byt overeni cilove adresy vuci google a kontrola, zda nam tabulka na google sedi !!!!!

        echo "
            <p>Každá zpráva zaslaná na e-mail
            <strong>{$uzivatelR['email_address']}@mensa.cz</strong> dorazí do schránky <b>{$uzivatelR['alias']}</b>.
            Mensa zprávu pouze přepošle, nijak ji neukládá ani nezpracovává.
            Užívání cílové schránky se řídí pravidly jejího poskytovatele.</p>";


        // zobraz, zda neni zrizen nick
        //$nick = retrieveNicknames($gdata, $uzivatelR['email_address']);
        if ($group["aliases"]) echo "<p>K Vaší adrese {$uzivatelR['email_address']}@mensa.cz je zřízen alias: " . $group["aliases"] . ".</p>";

        // vrat, ze je vse ok
        return $uzivatelR['typ'];
    }

    // tady nemame co delat, to je chyba
    return false;
}









/**
 * Zalozi plnohodnotnou mailovou schranku pro uzivatele.
 *
 * @global string $PUBLIC_DATABASE_NAME
 * @global type $db
 *
 * @param type $id_m
 * @param type $email_address   BEZ DOMENY
 * @param type $jmeno
 * @param type $prijmeni
 * @param type $password
 * @return boolean true, pokud plne uspeje, false v opacnem pripade
 */
function newUser($id_m, $email_address, $jmeno, $prijmeni, $password)
{
    if (empty($id_m) || empty($email_address) || empty($jmeno)|| empty($prijmeni)|| empty($password))
    {
        echo "<h4 class='error'>Akce selhala, nebyly zadány všechny parametry pro založení schránky.</h4>";
        return;
    }

    // mail je bez @mensa.cz!!!
    global $PUBLIC_DATABASE_NAME, $db;
    $gapps = new M_gapps();


    if ($gapps->existName($email_address))
    {
        echo "<h4 class='error'>E-mail {$email_address}@mensa.cz již na serveru Google Apps existuje.
            Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }
    // @TODO ovver nasi tabulku!!!


    // zavolej zalozeni na gmail, toto schranku fyzicky zalozi
    $vysledek = $gapps->createUser($email_address, $jmeno, $prijmeni, $password);

    // pokud probehlo, zapis do db. a informuj usera
    if (!$vysledek)
    {
        echo "<h4 class='notice'>Schránku se nepodařilo založit. Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }


    $sql = "INSERT INTO {$PUBLIC_DATABASE_NAME}.m_members_gmail
                   (email_address,      first_name, last_name, password, id_m, typ, alias, neclen, note)
            VALUES ('{$email_address}', '{$jmeno}', '{$prijmeni}', '{$password}', {$id_m}, 0, '', 0, 'automaticke zalozeni z intranetu verze 2014-07-20')";

    $db->Query($sql);
    if ($db->getAffectedRows() === 1)
    {
        // send_email(INFO_MAIL, "Zalozeni schranky", "Uzivatel id: " . $id_m . " (" . $email_address . ") zalozil schranku {$email_address}@mensa.cz");
        echo "<h4 class='notice'>E-mailová schránka {$email_address}@mensa.cz s heslem <b>{$password}</b> byla úspěšně zřízena!</h4>";

        // musime mit jistotu, ze dalsi dotazy to uz uvidi
        $db->Query("commit");
        return true;
    }
    else
    {
        // toto je blby, jsme napul cesty
        send_email(INFO_MAIL, "Chyba", "Uzivatel id: " . $id_m . " vytvoril box {$email_address}@mensa.cz, ale nepodarilo se ho zaspat do databaze!");
        echo "<h4 class='notice'>Při zřizování e-mailu došlo k chybě, schránka byla zřízena částečně, ale nelze ji používat. Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }
}





/**
 * Zalozi forward.
 *
 * @global string $PUBLIC_DATABASE_NAME
 * @global type $db
 *
 * @param type $id_m
 * @param type $email_address   BEZ DOMENY
 * @param type $fwd_address
 * @param type $jmeno
 * @param type $prijmeni
 * @return boolean
 */
function newForward($id_m, $email_address, $fwd_address, $jmeno, $prijmeni)
{
    if (empty($id_m) || empty($email_address) || empty($jmeno)|| empty($prijmeni)|| empty($fwd_address))
    {
        echo "<h4 class='error'>Akce selhala, nebyly zadány všechny parametry pro založení přesměrování.</h4>";
        return;
    }


    // mail je bez @mensa.cz!!!
    global $PUBLIC_DATABASE_NAME, $db;
    $gapps = new M_gapps();

    if (!filter_var($fwd_address, FILTER_VALIDATE_EMAIL))
    {
        echo "<h4 class='error'>Zadaná e-mailová adresa pro přesmerování není platná. Přesměrování nebylo zřízeno.</h4>";
        return false;
    }
    // @TODO over nasi tabulku!!!


    if ($gapps->existName($email_address))
    {
        echo "<h4 class='error'>E-mail {$email_address}@mensa.cz již na serveru Google Apps existuje.
            Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }



    $vysledek = $gapps->new_Forward($email_address, $fwd_address);
    if (!$vysledek)
    {
        echo "<h4 class='notice'>Přesměrování pro uživatele {$jmeno} {$prijmeni} z adresy {$email_address} na adresu {$fwd_address} se nepodařilo založit.
            Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }

    // zalozeni probehlo
    $sql = "INSERT INTO {$PUBLIC_DATABASE_NAME}.m_members_gmail ";
    $sql.="     (email_address, first_name, last_name, id_m, typ, alias, neclen, note) ";
    $sql.="     VALUES ('{$email_address}', '{$jmeno}', '{$prijmeni}', {$id_m}, 3, '{$fwd_address}', 0, 'automaticke zalozeni z intranetu verze 2014-07-20')";

    $db->Query($sql);
    if ($db->getAffectedRows() === 1)
    {
        echo "<h4 class='notice'>Přesměrování e-mailové adresy " . $email_address . "@mensa.cz na adresu " . $fwd_address . " bylo aktivováno.</h4>";
        // send_email(INFO_MAIL, "Zalozeni presmerovani", "Uzivatel id: {$id_m} (" . $email_address . ") zalozil presmerovani na " . $fwd_address . ".");

        // musime mit jistotu, ze dalsi dotazy to uz uvidi
        $db->Query("commit");
        return true; // vse ok
    }
    else
    {
        // toto je blby, jsme napul cesty
        send_email(INFO_MAIL, "Chyba", "Uzivatel id: " . $id_m . " vytvoril presmerovani pro {$email_address}@mensa.cz, ale nepodarilo se ho zaspat do databaze!");
        echo "<h4 class='notice'>Při zřizování přesměrování došlo k chybě. Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }
}


/**
 *
 * @global string $PUBLIC_DATABASE_NAME
 * @global type $db
 *
 * @param type $gapps
 * @param type $id_m_owner
 * @return boolean
 */
function deleteForwardForUser ($id_m_owner)
{
    global $PUBLIC_DATABASE_NAME, $db;
    $gapps = new M_gapps();


    // Ziskej informace o uzivateli z interni intranetove databaze uzivatelskych uctu.
    // query delej na dve casti, abychom mohli tu druhou reusnout pro delete - a byla tedy jistaot ze je stejna
    $query = " FROM {$PUBLIC_DATABASE_NAME}.m_members_gmail  WHERE id_m = {$id_m_owner} AND typ = 3";
    $uzivatelQ = $db->Query("SELECT email_address " . $query);
    $pocetDbRadek = $db->getNumRows($uzivatelQ);

    if ($pocetDbRadek < 1)
    {
        echo "<h4 class='error'>Po Váš účet id_m={$id_m_owner} nemáme v naší databázi žádné přesměrování.
            Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }

    if ($pocetDbRadek > 1)
    {
        echo "<h4 class='error'>Po Váš účet id_m={$id_m_owner} máme v databázi více záznamů.
            Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }

    //ziskej email - adresa BEZ @mensa.cz
    $q = $db->FetchArray($uzivatelQ);
    $email_address= $q['email_address'];


    if (!$gapps->existName($email_address))
    {
        echo "<h4 class='error'>E-mail {$email_address}@mensa.cz na serveru Google Apps neexistuje.
            Prosíme, kontaktujte administrátora <a href='mailto:<EMAIL>'><EMAIL></a>.</h4>";
        return false;
    }



    // vse ok, lze mazat
    //zruseni skupiny (ty se pouzivaji na FWD)
    $gapps->deleteGroup($email_address); // ta bohulze nic nevraci, takze nemuzeme overit vysledek jinak nez dalsim dotazem
    if ($gapps->existName($email_address))
    {
        echo "<h4 class='error'>Přesměrování {$email_address}@mensa.cz se nepodařilo smazat na serveru.</h4>";
        return false;
    }

    // Ziskej informace o uzivateli z interni intranetove databaze uzivatelskych uctu.
    $db->Query("DELETE " . $query);
    $smazano = $db->getAffectedRows();

    if ($smazano === 1)
    {
        echo "<h4 class='notice'>Přesměrování pro adresu {$email_address}@mensa.cz bylo úspěšně smazáno, tato adresa již nebude dostupná. Cílová schránka nebyla dotčena.</h4>";
        send_email(INFO_MAIL, "Smazani presmerovani", "Uzivatel id: {$id_m_owner} (" . $email_address . ") si smazal presmerovani.");
        return true;
    }

    echo "<h4 class='error'>Smazázní záznamu v intranetu pro adresu {$email_address}@mensa.cz se nezdařilo, bylo smazáno $smazano záznamů.</h4>";
    return false;
}

?>

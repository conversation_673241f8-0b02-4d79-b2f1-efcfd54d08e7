<p align="center"><font color="Red"><b>
<?PHP
if(!empty($heslo0) AND !empty($new_heslo0) AND !empty($new_heslo1) AND $new_heslo0==$new_heslo1){
	include("../ssl_library_new/change_passwd.l");
	$tmp = change_passwd($db, $a_user["id_m"], $heslo0, $new_heslo0);
	switch ($tmp){
		case 0:
			echo "HESLO NEBYLO ZMĚNĚNO.<br>Chyba: <PERSON><PERSON><PERSON> jste špatné heslo.";
		break;
		case 1:
			echo "HESLO BYLO ZMĚNĚNO.<br><PERSON><PERSON><PERSON> dalš<PERSON>m kroku se budete muset znovu přihlásit.";
		break;
		case 2:
			echo "HESLO NEBYLO ZMĚNĚNO.<br>Chyba: <PERSON><PERSON><PERSON><PERSON> jste správné heslo";
		break;
	}
} else {
$txt="";

if(empty($heslo0) AND !empty($new_heslo0) AND !empty($new_heslo1)){
	$txt="Musíte zadat staré heslo!<br>";
}
if(!empty($heslo0) AND empty($new_heslo0) AND empty($new_heslo1)){
	$txt.="Musíte zadat nové heslo!<br>";
}
if(!empty($heslo0) AND (empty($new_heslo0) AND !empty($new_heslo1)) OR (!empty($new_heslo0) AND empty($new_heslo1))){
	$txt.= "Nové heslo musíte zadat dvakrát, aby jste si byli jisti jeho volbou!<br>";
}
if(empty($txt)AND !empty($heslo0) AND ($new_heslo0!=$new_heslo1) ){
	$txt="Nově zadaná hesla nejsou totožná! Aktualizace hesla nebude pokračovat.<br>";
}
echo $txt;
?><br></b>
<table align="center" cellpadding="0" cellspacing="0">
<form action="index.php" method="post">
<input type="hidden" name="men" value="<?PHP echo $men?>">
<tr>
	<td align="right"><p>Staré heslo:&nbsp;</p></td>
	<td><p><input type="password" name="heslo0"></p></td>
</tr>
<tr>
	<td align="right"><p>Nové heslo:&nbsp;</p></td>
	<td><p><input type="password" name="new_heslo0"></p></td>
</tr>
<tr>
	<td align="right"><p>Oveření nového hesla:&nbsp;</p></td>
	<td><p><input type="password" name="new_heslo1"></p></td>
</tr>
<tr>
	<td colspan="2" align="right"><p><input type="submit" name="odeslat" value="Změnit heslo"></p></td>
</tr>
</table>
</form>
<?PHP
}
?>
<?php
/**
 * User: <PERSON>
 * Date: 23/07/2018
 * Time: 18:11
 */


if (isset($smazat) and isset($cb)) {
    $ids = "(";
    foreach ($cb as $id) {
        $ids .= $id . ",";
    }
    $ids = rtrim($ids, ", ");
    $ids .= ")";
    $SQL = "DELETE FROM mensaweb.aktuality WHERE id IN " . $ids;
    $db->Query($SQL);
    echo "Záznamy s ID " . $ids . " smazány";
}

if (isset($pridat)){
    $SQL = "INSERT INTO mensaweb.aktuality (date_view_start, date_view_end, html, name)
VALUES ('{$date_view_start}', '{$date_view_end}', '{$html}', '{$name}')";
    $db->Query($SQL);
    echo "Zaznam pridan";
}

$SQL = "SELECT * from mensaweb.aktuality";
$aktuality = $db->Query($SQL);
if (!$aktuality) {
    echo "Žádné záznamy aktualit v databázi";
} else {
    echo "<form action='.' name='del_aktualita' method='post'><input type='hidden' name='men' value='{$men}'>
<table border='1' style='border-collapse:collapse'>";
    echo "<tr><td>ID</td><td>Zacatek viditelnosti</td><td>Konec viditelnosti</td><td>Zobrazeny text</td><td>Nazev</td><td>Smazat</td></tr>";
    $column_names = array('id', 'date_view_start', 'date_view_end', 'html', 'name');
    while ($row = $db->fetchArray($aktuality)) {
        echo "<tr>";
        foreach ($column_names as $cname) {
            echo "<td>" . $row[$cname] . "</td>";
        }
        $cb_name = "cb" . $row['id'];
        echo "<td><input type='checkbox' name='cb[]' value=" . $row['id'] . "></td>";
        echo "</tr>";
        }
}
echo "<td colspan='5'></td><td><button type='submit' name='smazat' value='Smazat'>Smaž vybrané</button></td>";
echo "</table></form>";

?>

<form action="." name="ins_aktualita" method="post">
    <input type='hidden' name='men' value='<?php echo $men; ?>'>
    <table border='1' style="border-collapse:collapse">
        <tr>
            <td colspan='5'><b>Vložit nový záznam</b></td>
        </tr>
        <tr>
            <td>Zacatek viditelnosti (yyyy-mm-dd)</td>
            <td>Konec viditelnosti (yyyy-mm-dd)</td>
            <td>Zobrazeny text (html)</td>
            <td>Nazev</td>
        </tr>
        <tr>
            <td><input type='text' name='date_view_start'></td>
            <td><input type='text' name='date_view_end'></td>
            <td><textarea name='html' rows="4" cols="40"></textarea></td>
            <td><input type='text' name='name'></td>
        </tr>
        <tr>
            <td colspan='5'>
                <button type='submit' name="pridat" value="Pridat">Vložit záznam</button>
            </td>
        </tr>
    </table>
</form>

<h1><PERSON><PERSON><PERSON><PERSON> akcí</h1>
<style type="text/css" >
    /* Styly pro automaticky generované tabulky */
    tr.even
    {
        background-color: #eeeeff;
    }
    
    .draw_table_from_query img
    {
        width: 50px;
    }
</style>





<?PHP
/**
 * Výpis akcí z kalendáře v daném rozmezí (primárně pro kalendář v časopise a
 * zasílání upomínek).
 *
 * Změnovník
 *  2013-Jun-23, TK: Založeno
 */




// zalozilo jadro intranetu
// databazovy objekt
global $db;



// vypis tabulky
require_once("../ssl_library_new/draw_table_from_query.i");



/**
 * Funkce, ktera se prada vypisovaci tabulky a ovlivni zobrazeni urcitych poli.
 *
 * @param type $key     nazev pole
 * @param type $value   hodnota pole
 * @return type         vrati kus HTML, ktery se objevi v bunce tabulky
 */
function konverzni_funkce($key, $value)
{
    if ($key == 'Web')
        return "<a target='_new' href='{$value}'>{$value}</a>";
    return $value;
}


// nejprve přirav default
// ulozen extra, protoze se na to same datum budeme jeste jednou ptat
// abychom to mohli naplni do dotazovaciho policka
$datumOd_sql = " DATE_ADD(NOW(), INTERVAL 1  DAY) ";
$datumDo_sql = " DATE_ADD(NOW(), INTERVAL 60 DAY) ";





// pokud je uživatelské datum korektní, použij toto
if (isset($_POST['datumOd']))
{
    // print_r($_POST['datum']);
    // over spravnost a rovnou rozkouskuj udaje do pole
    $matches = Array();
    if (preg_match('/(?P<den>\d{1,2})\. (?P<mesic>\d{1,2})\. (?P<rok>\d{4})/', trim($_POST['datumOd']), $matches) === 1)
    {
        // print_r($matches);
        $datumOd_sql = " '{$matches['rok']}-{$matches['mesic']}-{$matches['den']}' ";

        // over zda datum neni moc stare (starsi nez ctvrt roku)
        // musi byt zadano znovu, bez uvozovek !
        if (trim(strtotime("{$matches['rok']}-{$matches['mesic']}-{$matches['den']}")) < (time() - 90 * 24 * 60 * 60))
            die("Zvolene datum je prilis stare!");
    }
}




// pokud je uživatelské datum korektní, použij toto
if (isset($_POST['datumDo']))
{
    // print_r($_POST['datum']);
    // over spravnost a rovnou rozkouskuj udaje do pole
    $matches = Array();
    if (preg_match('/(?P<den>\d{1,2})\. (?P<mesic>\d{1,2})\. (?P<rok>\d{4})/', trim($_POST['datumDo']), $matches) === 1)
    {
        // print_r($matches);
        $datumDo_sql = " '{$matches['rok']}-{$matches['mesic']}-{$matches['den']}' ";
        // limit do budoucnosti neni
    }
}



// nyni pro kontrolu zobraz data uzivateli
// zjistit datum od ktereho se jmene vypisuji
$datumOd = $db->FetchArray($db->Query("SELECT DATE_FORMAT({$datumOd_sql}, '%d. %m. %Y') as datum FROM dual"));
$datumOd = $datumOd["datum"]; // uloz jako retezec


$datumDo = $db->FetchArray($db->Query("SELECT DATE_FORMAT(({$datumDo_sql}), '%d. %m. %Y') as datum FROM dual"));
$datumDo = $datumDo["datum"]; // uloz jako retezec


echo "<form method='post' action='./index.php?men={$men}'>
    <p><i>Vypisují se akce konané od
    <input type='text' name='datumOd' value='{$datumOd}' size='9'>
    do
    <input type='text' name='datumDo' value='{$datumDo}' size='9'>.
        Prosím, dodržte formát DD. MM. YYYY.
<input type='submit' name='Odeslat' value='Změnit data'>
</p></form>";








$sql = "
SELECT
    DATE_FORMAT(date_start, '%d. %m. %H:%i')  as 'Začátek akce',
    city as     'Město akce',
    nazev as    'Název akce',
    concat (m.jmeno, ' ', m.prijmeni) as 'Organizátor',
    a.email as    'Kontaktní email',
    perex as 'Popis akce',
    CONCAT('https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a=', id_a) as 'Web'

FROM mensaweb.`mc_akce` a
    LEFT JOIN
    mensaweb.m_members m ON (a.id_owner = m.id_m)
WHERE
    date_start >= {$datumOd_sql}
    AND date_start <= {$datumDo_sql}
    AND typ != 'test'
ORDER BY
    date_start ASC
";
$pocet = draw_table_from_query($db, "akce", $sql, 'konverzni_funkce');
echo "<p>Nalezeno $pocet záznamů.</p>\n";
?>





<p style="text-align:right; margin-top:5em;"><em>Stránku naprogramoval Tomáš Kubeš, poslední úprava 23. června 2013.</em></p>

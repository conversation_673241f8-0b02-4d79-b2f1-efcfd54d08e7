<?PHP 
require_once("../ssl_library_new/calendar.class.l"); 

$calendar = new calendar($db);
if (isset($del) && $del == "smazat"){
	$SQL = "DELETE FROM mc_prava WHERE id_t = $id_t AND id_m = $id_m";
	$db->Query($SQL);
}
if (isset($odeslat)){
	$SQL = "INSERT INTO mc_prava(id_t, id_m) VALUES ('$id_t', '$id_m')";
	$db->Query($SQL);
}
$id_m = $a_user["id_m"];

if(access("class", $men, $a_user["id_m"], $db)){
	$full_rights = true;
} else { 
	$full_rights = false;
} ?>

<h4>Vložení práv uživatele:</h4>
<form action="<?PHP echo $PHP_SELF;?>?men=men15.3.0.0" name="ins_prava" method="post">
<table border="0" cellspacing="1" cellpadding="2" class="table_formular">
	<tr>
		<td valign="top">Organizátor:</td>
		<td><select name="id_m">
<?PHP 
		$sql = "SELECT id_m, jmeno, prijmeni FROM m_members  ORDER BY prijmeni, jmeno";
		$tmp = $db->Query($sql);
		while($radek = $db->FetchArray($tmp)){ ?>		
			<option value="<?PHP echo $radek["id_m"];?>"<?PHP if($radek["id_m"] == $id_m){echo "selected";}?>><?PHP echo $radek["prijmeni"]." ".$radek["jmeno"]." (".$radek["id_m"].")";?></option>
<?PHP 	} ?>
			</select>
		</td>
	</tr> 
	<tr>
		<td>Typ akce:</td>
		<td><select name="id_t">
<?PHP 
		$sql = "SELECT * FROM mc_typy_akci order by typ, typ_nazev";
		$tmp = $db->Query($sql);
		while($radek = $db->FetchArray($tmp)){ ?>		
			<option value="<?PHP echo $radek["id_t"];?>"><?PHP echo $radek["typ"]." - ".$radek["typ_nazev"];?></option>
<?PHP 	} ?>
			</select>
	</tr>
	<tr>
		<td></td>
		<td><input type="submit" name="odeslat" value="Odeslat"></td>
	</tr>

</table>
</form>
<br clear="all">

<?PHP
	$arr_typy["test"] = "Testování IQ";
	$arr_typy["ms"] = "setkání místní skupiny";
	$arr_typy["sig"] = "setkání SIGu";
	$arr_typy["dm"] = "akce Dětské Mensy";
	$arr_typy["diag"] = "diagnostický den";
	$arr_typy["mensa"] = "celomensovní akce";
	$arr_typy["other"] = "ostatní";
	
	$sql = "SELECT m.jmeno, m.prijmeni, p.*, t.typ, t.typ_nazev FROM mc_prava p INNER JOIN m_members m ON p.id_m = m.id_m INNER JOIN mc_typy_akci t ON p.id_t = t.id_t ORDER BY m.prijmeni, m.jmeno, t.typ, t.typ_nazev ";
	$tmp = $db->Query($sql);
	$table = "";
	$old_m = 0;
	while ($radek = $db->FetchArray($tmp)){
		$member = $radek["jmeno"]." ".$radek["prijmeni"];
		$id_m = $radek["id_m"];
		$id_t = $radek["id_t"];
		if ($old_m != $id_m){
			$tr = "<tr style=\"border-top: 1 px solid; background-color: #cccccc; font-weight: bold;\">";
			$old_m = $id_m;
		} else {
			$tr = "<tr>";
		}
		$table .= $tr."<td>$member</td>
						<td>".$arr_typy[$radek["typ"]]."</td><td>".$radek["typ_nazev"]."</td>
						<td><a href=\"$PHP_SELF?men=$men&del=smazat&id_m=$id_m&id_t=$id_t\">del</a></td></tr>";
	}
	if (strlen($table)>0){
		$table = "<table>".$table."</table>";
	
	}
	echo $table;
	?>


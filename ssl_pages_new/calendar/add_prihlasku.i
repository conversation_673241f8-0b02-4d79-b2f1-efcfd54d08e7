<?PHP
require_once("../ssl_library_new/calendar.class.l");

$calendar = new calendar($db);

$pol[1] = "jmeno";
$text[1] = "Jméno a příjmení";
$pol[2] = "email";
$text[2] = "E-mail";
$pol[3] = "telefon";
$text[3] = "Telefon";
$pol[4] = "vek";
$text[4] = "Věk";
$pol[5] = "datumnar";
$text[5] = "Datum narození";
$pol[6] = "rc";
$text[6] = "Rodné číslo";
$pol[7] = "cc";
$text[7] = "Členské číslo";
$pol[8] = "adresa";
$text[8] = "Adresa";
$pol[9] = "mesto";
$text[9] = "Město";
$pol[10] = "psc";
$text[10] = "PSČ";
$pol[11] = "variabilni";
$text[11] = "Variabilní symbol";
$pol[12] = "vol1";
$text[12] = "Libovolná položka 1";
$pol[13] = "vol2";
$text[13] = "Libovolná položka 2 (pole se zobrazí jako výběr ano/ne)";
$pol[14] = "vol3";
$text[14] = "Libovolná položka 3 (pole se zobrazí jako výběr ano/ne)";
$pol[15] = "poznamka";
$text[15] = "Poznámka";
$pol[16] = "send_email";
$text[16] = "";
$pol[17] = "kapacita";
$text[17] = "";
$pol[18] = "date_end";
$text[18] = "";
$pol[19] = "date_view";
$text[19] = "";
$poc_z = 20;

$SQL = "SELECT nazev, typ, DATE_FORMAT(date_start, '%d.%m.%Y %H:%i:%s') AS date2 FROM mc_akce WHERE id_a={$id_a}";

$rs = $db->Query($SQL);
if ($db->getNumRows($rs) == 1){
	$nazev = $db->getResult($rs, 0, "nazev");
	$typ = $db->getResult($rs, 0, "typ");
	$text[18] = $db->getResult($rs, 0, "date2");
	$text[19] = $db->getResult($rs, 0, "date2");
} else {
	echo "Nemáte vybranou akci, vložení přihlášky nemůže pokračovat. ";
	die;
}

// uložení dat
if (isset($odeslat)){
	if ($edit == "edit") {
		$SQL = "UPDATE mc_akce SET prihlaska = 1 WHERE id_a = $id_a";
		$rs = $db->Query($SQL);
		$SET = "";
		$date_end =  $calendar->write_date_sql($date_end);
		$date_view =  $calendar->write_date_sql($date_view);
		for ($i = 1; $i < $poc_z ; $i++){
			$SET .= " ".$pol[$i] ."='". @${$pol[$i]}."',";
		}
		$SET = " SET ".substr($SET, 0, strlen($SET) - 1);
 		$SQL = "UPDATE mc_akce_prihlasky $SET WHERE id_a = $id_a ";
		$rs = $db->Query($SQL);
		echo "<p><strong>Přihláška k vaší akci byla změněna.</strong></p>";

	} elseif ($edit == "ins") {
		$SQL = "UPDATE mc_akce SET prihlaska = 1 WHERE id_a = $id_a";
		$rs = $db->Query($SQL);
		$SLOUPCE = "";
		$VALUES = "";
		$date_end =  $calendar->write_date_sql($date_end);
        $date_view =  $calendar->write_date_sql($date_view);
		for ($i = 1; $i < $poc_z ; $i++){
			$SLOUPCE .= " ".$pol[$i] .",";
			$VALUES .= " '". @${$pol[$i]}."',";
		}
		$SLOUPCE = " (id_a,  ".substr($SLOUPCE, 0, strlen($SLOUPCE) - 1).")";
		$VALUES = " VALUES ($id_a, ".substr($VALUES, 0, strlen($VALUES) - 1).")";

		$SQL = "INSERT INTO mc_akce_prihlasky $SLOUPCE
				$VALUES ";
		$rs = $db->Query($SQL);
		echo "<p><strong>Přihláška k vaší akci byla přidána.</strong></p>";
	}

    $prihlaska_odkaz_suffix = "/prihlaska/reg_akce.php?&id_a={$id_a}";
    $testovani_odkaz_suffix = "/prihlaska/test_iq.php?&id_a={$id_a}";

    $odkaz = "https://intranet.mensa.cz{$prihlaska_odkaz_suffix}";

    if ($typ == "test") {
        $odkaz = "https://intranet.mensa.cz{$testovani_odkaz_suffix}";
    }

    echo "
        <p>
            Odkaz na akci v intranetu: <a target='_blank' href='/index.php?men=men15.0.0.0&id_a={$id_a}'>https://intranet.mensa.cz/index.php?men=men15.0.0.0&id_a={$id_a}</a><br>
            Odkaz na veřejnou přihlášku: <a target='_blank' href='{$odkaz}'>{$odkaz}</a>
        </p>
    ";

    echo "<p><a href='/index.php?men=men15.2.0.0'>Zpět na výpis akcí</a></p>";

    /* selenium, test servers only */
    if ($_SERVER['SERVER_ADDR'] != '46.28.109.167' && $_SERVER['SERVER_ADDR'] != '2a02:2b88:2:1::5bfb:1') {
        $href = "<a id='selenium-link-prihlaska' href='{$_SERVER['HTTP_ORIGIN']}{$prihlaska_odkaz_suffix}'>přihláška</a>";

        if ($typ == "test") {
            $href = "<a id='selenium-link-testovani' href='{$_SERVER['HTTP_ORIGIN']}{$testovani_odkaz_suffix}'>testování</a>";
        }

        echo "<p>Testovací odkaz pro Selenium: {$href}</p>";
    }

	die;





} elseif (isset($smazat)) {
	$SQL = "UPDATE mc_akce SET prihlaska = 0 WHERE id_a = $id_a";
	$rs = $db->Query($SQL);

	$SQL = "DELETE FROM mc_akce_prihlasky WHERE id_a = $id_a ";
	$rs = $db->Query($SQL);


	echo "<strong>Přihláška k vaší akci byla smazána.</strong>";
	die;
} else {
	$SQL = "SELECT * FROM mc_akce_prihlasky WHERE id_a = $id_a";
	$rs = $db->Query($SQL);
	if ($db->getNumRows($rs) == 1){
		$edit = "edit";
		for ($i = 1; $i < $poc_z ; $i++){
			${$pol[$i]} =  trim($db->getResult($rs, 0, $pol[$i]));
			if (${$pol[$i]} <> ""){
				${"ch_".$pol[$i]} = 1;
			} else {
				${"ch_".$pol[$i]} = 0;
				${$pol[$i]} = $text[$i];
			}
		}
        $date_end = $calendar->write_date($date_end, "", "full");
		$date_view = $calendar->write_date($date_view, "", "full");
	} else {
		$edit = "ins";
		for ($i = 1; $i < $poc_z ; $i++){
			${"ch_".$pol[$i]} = 1;
			${$pol[$i]} = $text[$i];
		}
		//JHE: inkrementace datumu o 1 den, aby byla prihlaska viditelna i v den konani akce
        $date_view = date("d.m.Y 00:00:00", strtotime("+1 day", strtotime($date_view)));
	}
}

?>

<script language="JavaScript">
<!--
function povolit(polozka) {
	if (document.getElementById('ch_'+polozka).checked == true) {
		document.getElementById(polozka).disabled = false;
	} else {
		document.getElementById(polozka).disabled = true;
	}
}
--> </script>

<h4>Přidání přihlášky k akci: <?PHP echo $nazev;?></h4>
- v tomto rozhraní vám umožníme, abyste si sami vložili na web Mensy funkční přihlášku na vaši akci. <br>
Položky, které chcete mít v přihlášce, nechte zaškrtnuté, text u nich si můžete změnit,<br>
ostatní odškrtněte (zešediví a jsou neaktivní)<br><span style="color: #8b0000">Pokud chcete, aby Vám přišel opis přihlášek na Váš email, musí zůstat položka "e-mail" na druhé pozici.</span><br>

<form action="<?PHP echo $PHP_SELF;?>?men=<?PHP echo $men;?>" name="ins_action" method="post">
<input type="hidden" name="id_a" value="<?PHP echo $id_a;?>">
<input type="hidden" name="id_parent" value="<?PHP echo $id_parent;?>">
<input type="hidden" name="edit" value="<?PHP echo $edit;?>">
<table border="0" cellspacing="1" cellpadding="2">
<?PHP for ($i = 1; $i < 16; $i++){
			if (@${"ch_".$pol[$i]} == 1){
				$checked = " checked";
				$disabled = "";
			} else {
				$checked = "";
				$disabled = " disabled";
			}
			echo '<tr><td><input type="Checkbox" name="ch_'.$pol[$i] .'" id="ch_'.$pol[$i] .'" value="1" '.$checked.' onclick="povolit(\''.$pol[$i].'\');"> </td>
		<td><input type="text" name="'.$pol[$i].'" id="'.$pol[$i].'" value="'.@${$pol[$i]}.'" size="50"'.$disabled.'></td>
	</tr>';
		}?>
	<tr class="table_separator"><td colspan="2"><input type="Checkbox" name="ch_send_email" id="ch_send_email" value="1"<?PHP if (@${"ch_".$pol[16]} == 1){ echo " checked";}?> onclick="povolit('send_email');"> Přeji si odesílat kopie přihlášek na email: <br>
				<input type="text" name="send_email" id="send_email" value="<?PHP echo $send_email;?>" size="50"<?PHP if (@${"ch_".$pol[17]} <> 1){ echo " disabled";}?>></td>
	<tr class="table_separator"><td colspan="2"><input type="Checkbox" name="ch_kapacita" id="ch_kapacita" value="1"<?PHP if (@${"ch_".$pol[17]} == 1 &&  $kapacita >0){ echo " checked";}?> onclick="povolit('kapacita');" style="align:right;"> Maximální počet přihlášených "nad čarou": 			<input type="text" name="kapacita" id="kapacita" value="<?PHP echo $kapacita;?>" size="2"<?PHP if (@${"ch_".$pol[17]} <> 1 &&  $kapacita >0){ echo " disabled";}?>> <small>- datum a čas ve formátu dd.mm.rrrr hh:mm</small><br>
				<small>přihlášení přes kapacitu se budou moci přihlásit, ale  budou pod čarou.</small></td>
	</tr>
	<tr class="table_separator"><td colspan="2"><input type="Checkbox" name="ch_date_end" id="ch_date_end" value="1"<?PHP if (@${"ch_".$pol[18]} == 1 &&  $date_end >0){ echo " checked";}?> onclick="povolit('date_end');" style="align:right;"> Přihlašování bude ukončeno od: 			<input type="text" name="date_end" id="date_end" value="<?PHP echo $date_end;?>" size="20"<?PHP if (@${"ch_".$pol[18]} <> 1 &&  $date_end <>""){ echo " disabled";}?>> <small>- datum a čas ve formátu dd.mm.rrrr hh:mm</small><br>
				<small>od tohoto data a času se již nebude možné přihlásit.</small></td>
	</tr>
	<tr class="table_separator"><td colspan="2"><input type="Checkbox" name="ch_date_view" id="ch_date_view" value="1"<?PHP if (@${"ch_".$pol[19]} == 1 &&  $date_view >0){ echo " checked";}?> onclick="povolit('date_view');" style="align:right;"> Datum odkdy se přestane na webu zobrazovat výpis přihlášek na tuto akci: 			<input type="text" name="date_view" id="date_view" value="<?PHP echo $date_view;?>" size="20"<?PHP if (@${"ch_".$pol[19]} <> 1 &&  $date_end <>""){ echo " disabled";}?>><br>
				<small>od tohoto data a času se přestana zobrazovat seznam přihlášených (pokud je nechcete zobrazovat vůbec, nastavte prošlé datum - např. 1.1.2008).</small></td>
	</tr>
	<tr><td colspan="2" class="table_separator" align="right">
		<input type="submit" name="odeslat" value="Odeslat">
<?PHP 	if ($edit =="edit"){	?><input type="submit" name="smazat" value="Smazat přihlášku"> <?PHP } ?>
	</td></tr>
	</table>

<h2 class="no-print">V<PERSON><PERSON> přihlášených na Vámi pořádané akce</h2>
<?PHP
/**
 *
 * Změnovník
 * 2021-Sep-07, HK: Přidáno pole trida pro děti
 * 2020-May-26, JS: Zobrazuje se email, zz, formatovani.
 * 2019-Jan-08, AN: Spoluorganizátor akce má nyní také možnost mazat přihlášené účastníky.
 * 2013-Mar-15, TK: Přidány odkazy na akci.
 * 2013-Feb-13, TK: Po abnormálním histerickém výlevu Hany Kalusové přidána možnost výběru zvolit typ řazení přihlášených.
 * 2013-Feb-12, TK: Úprava výpisu emailů na čárkovaný výpis platných, úprava pořadí výpisu. Založen změnovník.
 *                  Provedeno zformátování souboru v NetBeans.
 */


// vezmi si id přihlášeného
$id_m = $a_user["id_m"];

// normalizuj id akce
if (!isset($id_a) || $id_a == NULL || $id_a == 0 || @$id_a == "") $id_a = 0;
else $id_a = (int)$id_a;


// nastava sanitizuj zpusob razeni
$razeni = "prihlaseni";
if (isset($_POST['razeni']) && $_POST['razeni'] == "jmeno") $razeni = "jmeno";


// založ názvy polí - jednou pro tabulku, jednou por výpis
$pol[1] = "jmeno";
$text[1] = "Jméno a příjmení";
$pol[2] = "email";
$text[2] = "E-mail";
$pol[3] = "telefon";
$text[3] = "Telefon";
$pol[4] = "vek";
$text[4] = "Věk";
$pol[5] = "datumnar";
$text[5] = "Datum narození";
$pol[6] = "rc";
$text[6] = "Rodné číslo";
$pol[7] = "cc";
$text[7] = "Členské číslo";
$pol[8] = "adresa";
$text[8] = "Adresa";
$pol[9] = "mesto";
$text[9] = "Město";
$pol[10] = "psc";
$text[10] = "PSČ";
$pol[11] = "variabilni";
$text[11] = "Variabilní symbol";
$pol[12] = "vol1";
$text[12] = "Libovolná položka 1";
$pol[13] = "vol2";
$text[13] = "Libovolná položka 2 (pole se zobrazí jako výběr ano/ne)";
$pol[14] = "vol3";
$text[14] = "Libovolná položka 3 (pole se zobrazí jako výběr ano/ne)";
$pol[15] = "poznamka";
$text[15] = "Poznámka";
$pol[16] = "send_email";
$text[16] = "";
$pol[17] = "kapacita";
$text[17] = "";
$poc_z = 18;


require_once("../ssl_library_new/calendar.class.l");
$calendar = new calendar($db);


// kontrola přístupu
if (access("class", $men, $a_user["id_m"], $db)) {
    $full_rights = true;
} else {
    $full_rights = false;
}


// vygeneruj výpis akcí pro menu
// POZOR: význam přepínaše byl změnan z NEzobrazovat na zobrazovat
if ((!isset($id_a) || $id_a == NULL || $id_a == 0) && !isset($jennove)) {
    $jennove = 0;
}

if (@$jennove == 1) {
    $AND_NOVE = "";
} else {
    $AND_NOVE = " AND DATEDIFF(date_start, Now()) > -62 ";
}


$str_option = "";
$test = false;
$nazevAkce = ""; // bude do nej ulozen nazev akce (pri vypisu menu)


if ($full_rights) {
    $SQL = "SELECT id_a FROM mc_akce WHERE id_a IN (SELECT DISTINCT id_a FROM www_form_1)";
} else {
    $SQL = "SELECT id_a FROM mc_akce WHERE id_a IN (SELECT DISTINCT id_a FROM www_form_1 ) AND (id_owner=$id_m OR id_editor=$id_m)";
}

$maakce = $db->Query($SQL);
if ($db->getNumRows($maakce) > 0) {
    ?>
    <form action="<?PHP echo $PHP_SELF; ?>?men=<?PHP echo $men; ?>" name="ins_action" method="post" class="no-print">
        <table border="0" cellpadding="1" cellspacing="0">
            <?php

            if ($full_rights) {
                $SQL = "SELECT id_a, nazev, typ, date_start, date_end FROM mc_akce WHERE id_a IN (SELECT DISTINCT id_a FROM www_form_1) $AND_NOVE ORDER BY date_start desc";
            } else {
                $SQL = "SELECT id_a, nazev, typ, date_start, date_end FROM mc_akce WHERE id_a IN (SELECT DISTINCT id_a FROM www_form_1 ) $AND_NOVE AND (id_owner=$id_m OR id_editor=$id_m)  ORDER BY date_start desc";
            }

            $rs = $db->Query($SQL);

            if ($db->getNumRows($rs) > 0) {


                // vygeneruj položky do menu s výčtem akcí
                while ($radek = $db->FetchArray($rs)) {
                    // neni li vybrana zadna akce, vybere se to nejnovejsi
                    if (!isset($id_a)) {
                        $id_a = $radek["id_a"];
                    }

                    if ($id_a == $radek["id_a"]) {
                        $nazevAkce = $radek["nazev"];
                        $selected = " selected";
                        if ($radek["typ"] == 'test' || $radek["typ"] == 'diag') {
                            $test = true;
                        }
                    } else {
                        $selected = "";
                    }
                    $datum = $calendar->write_date($radek["date_start"], $radek["date_end"]);
                    $str_option .= "<option value=\"" . $radek["id_a"] . "\"$selected>" . $datum . " " . $radek["nazev"] . "</option>";
                }
                ?>
                <tr>
                    <td>Vyberte akci:</td>
                    <td><select name="id_a">
                            <option value="-1" selected>Všechny termíny</option>
                            <?PHP echo $str_option; ?>
                        </select></td>
                </tr>
                <?php
                $manoveakce = true;
            } else {
                $manoveakce = false;
            }
            ?>
            <tr>
                <td></td>
                <td><input type="checkbox" name="jennove" value="1" <?PHP if (@$jennove == 1) echo "checked"; ?>>
                    zobrazovat akce starší 2 měsíce v seznamu akcí
                </td>
            </tr>

            <?php
            if ($manoveakce) {
                ?>
                <tr>
                    <td></td>
                    <td>
                        <select name="razeni">
                            <option value="jmeno" <?PHP if ($razeni == 'jmeno') echo 'selected'; ?>>řadit dle příjmení a jména účastníka</option>
                            <option value="prihlaseni" <?PHP if ($razeni == 'prihlaseni') echo 'selected'; ?>>řadit dle data přihlášení účastníka od nejnovějšího</option>
                        </select>
                    </td>
                </tr>
                <?php
            }
            ?>
            <tr>
                <td></td>
                <td>&nbsp;<br><input type="submit" name="vyber_akci" value="Zobrazit"></td>
            </tr>
        </table>
    </form>

    <?PHP
}


if ($id_a > 0) {
    $AND = " AND f.id_a=$id_a ";
    $SQL = "SELECT * FROM mc_akce_prihlasky WHERE id_a = $id_a";
    $rs = $db->Query($SQL);
    if ($db->getNumRows($rs) == 1) {
        for ($i = 1; $i < $poc_z; $i++){
            ${$pol[$i]} = trim($db->getResult($rs, 0, $pol[$i]));
            if (${$pol[$i]} <> "") {
                ${"ch_" . $pol[$i]} = 1;
            } else {
                ${"ch_" . $pol[$i]} = 0;
                ${$pol[$i]} = $text[$i];
            }
        }
    } elseif ($test == true) {
        $pol[1] = "jmeno";
        $text[1] = "Jméno a příjmení";
        $pol[2] = "email";
        $text[2] = "E-mail";
        $pol[3] = "telefon";
        $text[3] = "Telefon";
        $pol[4] = "rc";
        $text[4] = "Mobil";
        $pol[5] = "vek";
        $text[5] = "Věk";
        $pol[6] = "datumnar";
        $text[6] = "Datum narození";
        $pol[7] = "cc";
        $text[7] = "trvalá adresa";
        $pol[8] = "adresa";
        $text[8] = "ulice";
        $pol[9] = "mesto";
        $text[9] = "Město";
        $pol[10] = "psc";
        $text[10] = "PSČ";
        $pol[11] = "variabilni";
        $text[11] = "Koresp. adresa";
        $pol[12] = "vol1";
        $text[12] = "ulice";
        $pol[13] = "vol2";
        $text[13] = "město";
        $pol[14] = "vol3";
        $text[14] = "PSČ";
        $pol[15] = "poznamka";
        $text[15] = "Poznámka";

        for ($i = 1; $i < $poc_z; $i++) {
            ${"ch_" . $pol[$i]} = 1;
            ${$pol[$i]} = $text[$i];
        }
    } else {
        for ($i = 1; $i < $poc_z; $i++) {
            ${"ch_" . $pol[$i]} = 1;
            ${$pol[$i]} = $text[$i];
        }
    }
} else {
    // 2017-10-06, TK: pokud jsme tady, neco je strasne spatne a nechceme nic zobrazit
    $AND = "1 = 2 ";
}


////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// smazani prihlasky
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// TODO získávat parametry explicitně místo register_globals
// promenna $del_u prichazi z formulare zaslaneho strankou (je GET promenna, ktera se tu objevi diky fuck*** register globals).
if (isset($del_u) && strlen(@$del_u) > 0) {
    // sanitizuj parametr tim, ze se vynuti int a over, ze to je cislo.
    $del_u = (int) $del_u;
    if ($del_u <= 0){
        die("Necekana hodnota parametru del_u.");
    }

    if ($full_rights) {   // super admin, nekontroluj pristup
        $SQL = "DELETE FROM www_form_1 WHERE  id_z={$del_u}";
    } else {   // zakladatel akce dle organizatora ulozeny v tabulce prihlasenych, organizatora v tabulce akce nebo editor v tabulce akce
        $SQL = "DELETE w FROM www_form_1 w INNER JOIN mc_akce a ON w.id_a=a.id_a WHERE (w.id_org=$id_m OR a.id_owner=$id_m OR a.id_editor=$id_m) AND id_z={$del_u}";
    }

    // proved smazani
    if ($db->Query($SQL) === TRUE) {
        echo "<br><font color='red'>Daný záznam byl smazán.</font><br>";
    } else {
        echo "<br><font color='red'>Záznam se nepodařilo smazat.</font><br>";
    }

    // 2017-02-03, TK: smaz kod poukazu na test, pokud byl tento zadan - neni treba kontrolovat zda poukaz existuje, pokud ne, zmeni se 0 radek.
    $SQL = "UPDATE m_poukazy_na_test SET id_prihlasky = NULL, je_pouzity = 0 WHERE id_prihlasky = {$del_u}";

    if ($db->Query($SQL) === true) {
        echo "<!-- <font color='red'>Smazázna asociace na slevový kód.</font> -->";
    }
}


// zobraz vypis

// fragment SQL pro razeni, vybere se podle zadaneho vstupu
$razeni_sql = "
/*  pokus se vybrat druhé slovo jména a podle něj řadit (je treba pracovat na otrimovanem jmenu) */
    SUBSTRING_INDEX(jmeno_t, ' ', -1),
    /* potom rad dle krestniho jmena */
    jmeno_t ASC,
    f.datum DESC";

// pokud je jine razeni, prepis
if ($razeni == "prihlaseni") $razeni_sql = "f.datum desc";


if ($full_rights) {
    $SQL = "
    SELECT *,  DATE_FORMAT(datum, '%e.%c.%Y %k:%i') AS vlozeno, TRIM(f.jmeno) AS jmeno_t
    FROM www_form_1 f
    WHERE 1 $AND
    ORDER BY {$razeni_sql}";
} else {
    $SQL = "
    SELECT
        f.*,
        DATE_FORMAT(f.datum, '%e.%c.%Y %k:%i') AS vlozeno, TRIM(f.jmeno) AS jmeno_t
    FROM www_form_1 as f INNER JOIN mc_akce a ON f.id_a = a.id_a
    WHERE (a.id_owner=$id_m OR a.id_editor=$id_m) $AND
    ORDER BY {$razeni_sql}";
}

$ucastnici = $db->Query($SQL);
$poc_u = $db->getNumRows($ucastnici);
if ($poc_u > 0) {
    $ret = "";
    //
    $td_start = "<td><font size=\"1\">";
    $td_end = "</font></td>\n";
    // akumuluje platné emaily
    $str_emaily = Array();
    $email_platne = 0;
    $email_divne = 0;
    $email_neexistujici = 0;
    $str_emaily_zz = Array();
    $email_zz_platne = 0;
    $email_zz_divne = 0;
    $email_zz_neexistujici = 0;

    while ($row = $db->FetchArray($ucastnici)) {
        // kontrola a uložení emailu
        // zapis email pro další výpis
        if (trim($row["email"]) != "") {
            $row["email"] = trim($row["email"]);

            // jedná se o platný email?
            if (preg_match("/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/", $row["email"]) === 1) {
                $str_emaily[] = $row["email"];
                $email_platne++;
            } else {
                $str_emaily[] = "<span style='color: red;'>" . $row["email"] . "</span>";
                $email_divne++;
            }
        } else {
            $email_neexistujici++;
        }
        // emaily ZZ
        if (trim($row["email2"]) != "") {
            $row["email2"] = trim($row["email2"]);

            // jedná se o platný email?
            if (preg_match("/^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/", $row["email2"]) === 1) {
                $str_emaily_zz[] = $row["email2"];
                $email_zz_platne++;
            } else {
                $str_emaily_zz[] = "<span style='color: red;'>" . $row["email2"] . "</span>";
                $email_zz_divne++;
            }
        } else {
            $email_zz_neexistujici++;
        }


        // 2015-02-08, Tomáš Nováček: potvrzení smazání
        $ret .= "<tr><td><a href=\"" . $PHP_SELF . "?men=men15.5.0.0&del_u=" . $row["id_z"] . "&id_a=$id_a&vyber_akci=1\" onclick=\" return confirm('Opravdu chcete smazat prihlasku uzivatele " . $row["jmeno"] . " ?')\">del</a></td>";


        // vypis redku prihlasenych - vypisuj vsechny aktivni polozky dle definice prihlasky
        for ($i = 1; $i < 16; $i++) {
            $var = "ch_" . $pol[$i];

            if ($i == 2) {
                $ret .= $td_start . $row['jmeno_zz'] . $td_end;
                $ret .= $td_start . $row['email2'] . $td_end;
            }

            if ($i == 4) {
                $ret .= $td_start . $row['trida'] . $td_end;
            }

            if (!isset($pol[$i]) || !isset($$var)) {
                continue;
            }

            if (${"ch_" . $pol[$i]} == 1) {
                // 2019-08-12, TK: sanitizuj hodnoty pri vypise, pokud tam nahodou bot nablil nejake hacky
                $ret .= $td_start . strip_tags($row[$pol[$i]]) . $td_end;
            }

        }
        $ret .= $td_start . $row["vlozeno"] . $td_end . "</tr>\n";
    }


    if (strLen($ret) > 0) {
        $ret1 = "<table border='1' cellspacing='0' cellpadding='2' width='95%'>" .
            "<tr><td>&nbsp;</td>";
        for ($i = 1; $i < 16; $i++) {
            $var = "ch_" . $pol[$i];

            if ($i == 2) {
                $ret1 .= $td_start . "<strong>Jméno zák. zastupce</strong>" . $td_end;
                $ret1 .= $td_start . "<strong>Email zák. zastupce</strong>" . $td_end;
            }

            if ($i == 4) {
                $ret1 .= $td_start . "<strong>Třída</strong>" . $td_end;
            }

            if (!isset($pol[$i]) || !isset($$var)) {
                continue;
            }

            if (${"ch_" . $pol[$i]} == 1) {
                $ret1 .= $td_start . "<strong>" . ${$pol[$i]} . "</strong>" . $td_end;
            }
        }
        $ret1 .= $td_start . "<strong>Vloženo</strong>" . $td_end .
            "</tr>"
            . $ret . "</table>";
    }

    $verejny_odkaz = "https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a={$id_a}";;

    echo '<div class="no-print">';
    echo "<h3>Informace o akci {$nazevAkce}</h3>";
    echo "<p>Odkaz na akci {$nazevAkce} v intranetu:
    <a target='_blank' href='/index.php?men=men15.0.0.0&id_a={$id_a}'>https://intranet.mensa.cz/index.php?men=men15.0.0.0&id_a={$id_a}</a><br>

    Odkaz na veřejnou přihlášku: <a target='_blank' href='{$verejny_odkaz}'>{$verejny_odkaz}</a></p>";

    echo "<p>Můžete <a href='/index.php?men=men15.1.0.0&edit=1&id_a={$id_a}'>editovat informace o akci</a> nebo

    <a href='/index.php?men=men15.6.0.0&edit=1&id_a={$id_a}'>změnit přihlášku</a>.</p>";


    echo "<h3>Výpis přihlášených</h3>";
    echo "<p>Počet přihlášených účastníků: {$poc_u}</p>";
    echo "</div>";
    // vypiš tabulku
    echo $ret1;

    // výpis adres
    echo '<div class="no-print">';
    echo "<h3 style='margin-top: 2em;'>E-maily přihlášených</h3>";
    echo "<p>Pokud chcete přihlášeným poslat hromadnou zprávu,
        níže jsou jejich e-mailové adresy; {$email_platne} adres je platných,
        {$email_neexistujici} adres nebylo vyplněno vůbec a 
        {$email_divne} adres v nesprávném formátu je zvýrazněno červeně).</p>
        <p><strong>S čárkou:</strong> " . implode($str_emaily, ', ') . "</p>
        <p><strong>Se středníkem:</strong> " . implode($str_emaily, '; ') . "</p>";
    echo "<h3 style='margin-top: 2em;'>E-maily zákonných zástupců přihlášených</h3>";
    echo "<p>Pokud chcete přihlášeným zákonným zástupcům poslat hromadnou zprávu,
        níže jsou jejich e-mailové adresy;
        {$email_zz_platne} adres je platných, 
        {$email_zz_neexistujici} adres nebylo vyplněno vůbec a
        {$email_zz_divne} adres v nesprávném formátu je zvýrazněno červeně).</p>
        <p><strong>S čárkou:</strong> " . implode($str_emaily_zz, ', '). "</p>
        <p><strong>Se středníkem:</strong> " . implode($str_emaily_zz, '; '). "</p>";
    echo "</div>";
}

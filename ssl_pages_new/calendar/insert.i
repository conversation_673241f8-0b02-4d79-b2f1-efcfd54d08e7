<?PHP

use exceptions\PublicException;
/**
 * Založení a<PERSON>
 *
 * Změnovník
 * 2013-Mar-25, TK: Založení změnovníku, uvádění členského čísla v seznamu spoluorganizátorů.
 * 2017-Apr-09, TK: problém s tím, že nejde editovat přihl<PERSON>š<PERSON>, komentáře
 *
 */


// z bezpečnostních důvodů je třeba za provozu určitě mít chyby zakázané
//error_reporting(E_ALL);
//ini_set("display_errors", 1);
//ini_set("html_errors", TRUE);
//error_reporting(E_ALL^E_NOTICE);


// knihovna pro standardizované zasílání e-mailů
require_once("../ssl_library_new/calendar.class.l");


// objekt kaldáře (vyrobil R. Brzuska)
// více viz knihovna: ssl_library_new/calendar.class.l
$calendar = new calendar($db);

// zpracování speciálních událostí:
// řešení opakujících se akcí, mazání akcí
if (isset($odeslat_all)) { // editovat vsechny budouci terminy / smazu stare a nahradim novymi
    $odeslat = "odeslat";
    $edit = 2;
    $calendar->del_item($id_a, $id_parent);
    $id_a = 0;
} elseif (isset($delete_all)) {
    $calendar->del_item($id_a, $id_parent);
    $edit = 2;
    header("Location: index.php?men=men15.2.0.0");
} elseif (isset($delete)) {
    $calendar->del_item($id_a, -1);
    header("Location: index.php?men=men15.2.0.0");
}

try {
    if (isset($odeslat)) {
        // zpracování zaslaných dat.
        // zalozeni nove akce
        // echo "<h1>Zpracovani zalozeni akce</h1>";

        $arr_item = array();

        // centrální ID akce, PK do tabulky
        $arr_item["id_a"] = $id_a;

        //typ editace / 0- jednorazova akce, 1-pravidelna akce jen aktualni termin, 2-pravidelna akce vsechny terminy
        $arr_item["typ"] = $typ;

        $date_start = DateTime::createFromFormat('Y-m-d\TH:i', $date_start);
        $date_end = DateTime::createFromFormat('Y-m-d\TH:i', $date_end);

        if(!$date_start){
            $arr_item["date_start"] = '';
            $arr_item["date_end"] = '';
        } else {
            if($date_end
                && ($date_end->diff($date_start)->invert < 1)
                && $date_end->format(DateTime::ATOM) !== $date_start->format(DateTime::ATOM)
            )
                throw new PublicException('Datum ukončení nemůže být za datem začátku.');

            // parsování datumu
            $arr_item["date_start"] = $date_start->format(DateTime::ATOM);

            // pokud není zadán konec, je akce "bodová"
            if (!$date_end) {
                $arr_item["date_end"] = $date_start->format(DateTime::ATOM); //nezadany konec -> stejny jako zacatek
            } else {
                $arr_item["date_end"] = $date_end->format(DateTime::ATOM);
            }
        }

        // ocekava, ze hodnoty zaslane formularem budou v promennych -> register globals :(
        $arr_item["date_pre"] = addslashes($date_pre);
        $arr_item["nazev"] = addslashes($nazev);
        $arr_item["perex"] = addslashes($perex);
        $arr_item["popis"] = addslashes($popis);
        $arr_item["nazev_en"] = addslashes($nazev_en);
        $arr_item["popis_en"] = addslashes($popis_en);
        $arr_item["place"] = addslashes($place);
        $arr_item["city"] = addslashes($city);
        $arr_item["id_area"] = $id_area;
        $arr_item["area_name"] = $area_name;
        $arr_item["url"] = addslashes($url);
        $arr_item["id_owner"] = $id_m;
        $arr_item["id_editor"] = $id_editor;
        $arr_item["telefon"] = $telefon;
        $arr_item["email"] = $email;
        $arr_item["typ"] = $typ;
        $arr_item["ms"] = $ms ?? '';
        $arr_item["sig"] = $sig ?? '';
        $arr_item["dm"] = $dm ?? '';
        $arr_item["mensa"] = $mensa ?? '';
        $arr_item["test"] = $test;
        $arr_item["termin"] = $termin;
        $arr_item["den"] = $den;
        $arr_item["perioda"] = $perioda;
        $arr_item["hodina"] = $hodina;
        $arr_item["generovat_do"] = $generovat_do;
        $arr_item["prihlaska"] = $prihlaska;

        $odkaz = "https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a={$id_a}";

        // nastavení příznaku přihlášky - pro testování automaticky
        if ($typ == "test" || $test <> 0) {
            $prihlaska = 1;
            $odkaz = "https://intranet.mensa.cz/prihlaska/test_iq.php?&id_a={$id_a}";
        }

    // pošli notifikaci člověku zodpovědnému za aktivity
    // 2023-04-11: bylo presunuto primo do calendar.class.l:908

        // předej objektu informace o akci pro založení
        $id_a = $calendar->edit_item($arr_item);

        // pokud je přihláška, přejdi na stránku editace přihlášky
        if ($prihlaska == 1) {
            header("Location: index.php?men=men15.6.0.0&id_a=$id_a");
            die;
        } else {
            header("Location: index.php?men=men15.2.0.0");
            die;
        }

    } elseif (isset($id_a) && $id_a != 0) {
        // zobraz data existujici akce
        // echo "<h1>Zobrazeni akce</h1>";

        $calendar->get_action($id_a);
        $i = 0;
        $id_parent = $calendar->actions[$i]["id_parent"];
        $date_start = DateTime::createFromFormat('Y-m-d H:i:s', $calendar->actions[$i]["date_start"]);
        $date_end = DateTime::createFromFormat('Y-m-d H:i:s', $calendar->actions[$i]["date_end"]);
        //@$date_end_sign = $calendar->write_date($calendar->actions[$i]["date_end_sign"], "", "full");
        @$date_pre = $calendar->actions[$i]["date_pre"];
        @$nazev = $calendar->actions[$i]["nazev"];
        @$perex = $calendar->actions[$i]["perex"];
        @$popis = $calendar->actions[$i]["popis"];
        @$nazev_en = @$calendar->actions[$i]["nazev_en"];
        @$popis_en = @$calendar->actions[$i]["popis_en"];
        @$place = $calendar->actions[$i]["place"];
        @$city = $calendar->actions[$i]["city"];
        @$id_area = $calendar->actions[$i]["id_area"];
        @$area_name = $calendar->actions[$i]["area_name"];
        @$url = $calendar->actions[$i]["url"];
        @$id_editor = $calendar->actions[$i]["id_editor"];
        @$id_m = $calendar->actions[$i]["id_owner"];
        @$id_owner = $calendar->actions[$i]["id_owner"];
        @$telefon = $calendar->actions[$i]["telefon"];
        @$email = $calendar->actions[$i]["email"];
        @$typ = $calendar->actions[$i]["typ"];
        @$ms = $calendar->actions[$i]["ms"];
        @$sig = $calendar->actions[$i]["sig"];
        @$dm = $calendar->actions[$i]["dm"];
        @$mensa = $calendar->actions[$i]["mensa"];
        @$test = $calendar->actions[$i]["test"];
        @$den = $calendar->actions[$i]["den"];
        @$perioda = $calendar->actions[$i]["perioda"];
        @$hodina = $calendar->actions[$i]["hodina"];
        @$generovat_do = 1;
        @$termin = 1;
        @$prihlaska = $calendar->actions[$i]["prihlaska"];
        if ($id_editor == 0) {
            $id_editor = $id_owner;
        }


    } else {
        // vygeneruje podklady pro prázdný formulář
        // echo "<h1>Zobrazeni formulare</h1>";

        $id_m = $a_user["id_m"];
        $SQL = "SELECT email, mobil FROM m_members WHERE id_m = $id_m";
        $rs = $db->Query($SQL);
        $id_a = 0;
        $date_start = "";
        $date_end = "";
        $date_pre = "";
        $date_end_sign = "";
        $nazev = "";
        $perex = "";
        $popis = "";
        $nazev_en = "";
        $popis_en = "";
        $place = "";
        $city = "";
        $id_area = "";
        $area_name = "";
        $url = "";
        $id_editor = $id_m;
        $telefon = $db->getResult($rs, 0, "mobil");
        $email = $db->getResult($rs, 0, "email");
        $typ = 0;
        $ms = 0;
        $sig = 0;
        $dm = 0;
        $mensa = 0;
        $test = 0;
        $termin = 1;
        $den = 1;
        $perioda = "m1";
        $hodina = "";
        $generovat_do = 1;
        $prihlaska = 0;
    }
} catch (PublicException $e) {
?>
    <span class="alert alert-danger">
        <?= $e->getMessage(); ?>
    </span>
<?php
}

$STR_ALL = "0all";
$arr_typy["test"] = "Testování IQ";
$arr_typy["ms"] = "setkání místní skupiny";
$arr_typy["sig"] = "setkání SIGu";
$arr_typy["dm"] = "akce Dětské Mensy";
$arr_typy["diag"] = "diagnostický den";
$arr_typy["mensa"] = "celomensovní akce";
$arr_typy["other"] = "ostatní";


if (access("class", $men, $a_user["id_m"], $db)) {
    $full_rights = false; // xxx
} else {
    $full_rights = false;
}




?>
<script language="JavaScript">
    <!--
    function vymen_div() {
        if (document.getElementById('termin1').checked == true) {
            document.getElementById('tr_0').style.display = 'list-item';
            document.getElementById('tr_1').style.display = 'list-item';
            document.getElementById('tr_5').style.display = 'list-item';
            document.getElementById('tr_2').style.display = 'none';
            document.getElementById('tr_3').style.display = 'none';
            document.getElementById('tr_4').style.display = 'none';

        } else {
            document.getElementById('tr_0').style.display = 'none';
            document.getElementById('tr_1').style.display = 'none';
            document.getElementById('tr_5').style.display = 'none';
            document.getElementById('tr_2').style.display = 'list-item';
            document.getElementById('tr_3').style.display = 'list-item';
            document.getElementById('tr_4').style.display = 'list-item';
        }
    }
    --> </script>








<h4>Vložení nové akce:</h4>
<form action="<?PHP echo $PHP_SELF; ?>?men=<?PHP echo $men; ?>" name="ins_action" method="post">
    <input type="hidden" name="id_a" value="<?PHP echo $id_a; ?>">
    <input type="hidden" name="id_parent" value="<?= isset($id_parent) ? $id_parent : ''; ?>">
    <input type="hidden" name="edit" value="<?= isset($edit) ? $edit : ''; ?>">
    <table border="0" cellspacing="1" cellpadding="2" class="table_formular">
        <?PHP
        if ($termin == 1) {
            $checked1 = " checked";
            $checked2 = "";
            $style1 = " style=\"display: list-item\"";
            $style2 = " style=\"display:none;\"";
        } else {
            $checked2 = " checked";
            $checked1 = "";
            $style2 = " style=\"display: list-item\"";
            $style1 = " style=\"display:none;\"";
        }
        ?>
        <tr>
            <td colspan="2" class="table_separator">Termín:</td>
        </tr>
        <tr>
            <td colspan="2"><input type="Radio" value="1" onclick="vymen_div()"<?PHP echo $checked1; ?> name="termin"
                                   id="termin1">&nbsp;Jednorázová akce:
            </td>
        </tr>
        <tr id="tr_0"<?PHP echo $style1; ?>>
            <td>Datum začátku:</td>
            <td><input type="datetime-local" name="date_start" value="<?= $date_start ? $date_start->format('Y-m-d H:i') : ''; ?>" size="20">
                <small>- datum a čas ve formátu dd.mm.rrrr hh:mm</small>
            </td>
        </tr>
        <tr id="tr_1"<?PHP echo $style1; ?>>
            <td>Datum ukončení:</td>
            <td><input type="datetime-local" name="date_end" value="<?= $date_end ? $date_end->format('Y-m-d H:i') : ''; ?>" size="20">
                <small>- datum a čas ve formátu dd.mm.rrrr hh:mm</small>
            </td>
        </tr>
        <tr id="tr_5"<?PHP echo $style1; ?>>
            <td>Předběžná akce:</td>
            <td><input type="text" name="date_pre" value="<?PHP echo $date_pre; ?>" size="20">
                <small>- toto datum můžete zadat v libovolném tvaru - např. "druhá půlka listopadu". Pokud zadáváte
                    předběžnou akci, vyplňte ale datum začátku akce nějakým fiktivním datumem, aby se akce správně
                    časově zařadila.
                </small>
            </td>
        </tr>

        <tr>
            <td colspan="2"><input type="Radio" value="2" onclick="vymen_div()"<?PHP echo $checked2; ?> name="termin"
                                   id="termin2">&nbsp;Pravidelná akce:
            </td>
        </tr>
        <tr id="tr_2"<?PHP echo $style2; ?>>
            <td>Pokaždé v :</td>
            <td><select name="perioda">
                    <option value="m1"<?PHP if ($perioda == "m1") {
                        echo " selected";
                    } ?>>první
                    </option>
                    <option value="m2"<?PHP if ($perioda == "m2") {
                        echo " selected";
                    } ?>>druhý
                    </option>
                    <option value="m3"<?PHP if ($perioda == "m3") {
                        echo " selected";
                    } ?>>třetí
                    </option>
                    <option value="m4"<?PHP if ($perioda == "m4") {
                        echo " selected";
                    } ?>>čtvrtý
                    </option>
                    <option value="m5"<?PHP if ($perioda == "m5") {
                        echo " selected";
                    } ?>>poslední
                    </option>
                    <option value="w0"<?PHP if ($perioda == "w0") {
                        echo " selected";
                    } ?>>každý
                    </option>
                    <option value="w1"<?PHP if ($perioda == "w1") {
                        echo " selected";
                    } ?>>každý lichý
                    </option>
                    <option value="w2"<?PHP if ($perioda == "w2") {
                        echo " selected";
                    } ?>>každý sudý
                    </option>
                </select>
                <select name="den">
                    <option value="1"<?PHP if ($den == "1") {
                        echo " selected";
                    } ?>>pondělí
                    </option>
                    <option value="2"<?PHP if ($den == "2") {
                        echo " selected";
                    } ?>>úterý
                    </option>
                    <option value="3"<?PHP if ($den == "3") {
                        echo " selected";
                    } ?>>středa
                    </option>
                    <option value="4"<?PHP if ($den == "4") {
                        echo " selected";
                    } ?>>čtvrtek
                    </option>
                    <option value="5"<?PHP if ($den == "5") {
                        echo " selected";
                    } ?>>pátek
                    </option>
                    <option value="6"<?PHP if ($den == "6") {
                        echo " selected";
                    } ?>>sobota
                    </option>
                    <option value="7"<?PHP if ($den == "7") {
                        echo " selected";
                    } ?>>neděle
                    </option>
                </select> v měsíci.
            </td>
        </tr>
        <tr id="tr_3"<?PHP echo $style2; ?>>
            <td>Hodina:</td>
            <td><input type="text" name="hodina" value="<?PHP echo $hodina; ?>" size="5" maxlength="5"> - čas ve formátu
                hh:mm
            </td>
        </tr>
        <tr id="tr_4"<?PHP echo $style2; ?>>
            <td>Generovat dopředu na:</td>
            <td><select name="generovat_do">
                    <option value="1"<?PHP if ($generovat_do == "1") {
                        echo " selected";
                    } ?>>1 měsíc
                    </option>
                    <option value="2"<?PHP if ($generovat_do == "2") {
                        echo " selected";
                    } ?>>2 měsíce
                    </option>
                    <option value="3"<?PHP if ($generovat_do == "3") {
                        echo " selected";
                    } ?>>3 měsíce
                    </option>
                    <option value="4"<?PHP if ($generovat_do == "4") {
                        echo " selected";
                    } ?>>4 měsíce
                    </option>
                    <option value="5"<?PHP if ($generovat_do == "5") {
                        echo " selected";
                    } ?>>5 měsíců
                    </option>
                    <option value="6"<?PHP if ($generovat_do == "6") {
                        echo " selected";
                    } ?>>6 měsíců
                    </option>
                </select></td>
        </tr>
        <tr>
            <td colspan="2" class="table_separator">Místo:</td>
        </tr>
        <tr>
            <td>Místo:</td>
            <td><input type="text" name="place" value="<?PHP echo $place; ?>" size="50"></td>
        </tr>
        <tr>
            <td>Město:</td>
            <td><input type="text" name="city" value="<?PHP echo $city; ?>" size="50"></td>
        </tr>
        <tr>
            <td valign="top">Dosah:</td>
            <td><select name="id_area[]" multiple size="4">
                    <option value="0">Celá ČR</option>
                    <?PHP
                    $sql = "SELECT * FROM m_list_kraj";
                    $kraje = $db->Query($sql);
                    while ($radek = $db->FetchArray($kraje)) { ?>
                        <option value="<?PHP echo $radek["id_kraj"]; ?>"><?PHP echo $radek["kraj_name"]; ?></option>
                    <?PHP } ?>
                </select><br>
                jiný: <input type="text" name="area_name" value="<?PHP echo $area_name; ?>">
            </td>
        </tr>
        <tr>
            <td colspan="2" class="table_separator">Akce:</td>
        </tr>
        <tr>
            <td>Název akce:</td>
            <td><input type="text" name="nazev" value="<?PHP echo $nazev; ?>" size="50"></td>
        </tr>
        <tr>
            <td>Zkrácený popis akce:
                <small>pro vložení na titulní stránku, do přehledu akcí</small>
            </td>
            <td><textarea name="perex" rows="6" cols="40"><?PHP echo $perex; ?></textarea></td>
        </tr>
        <tr>
            <td>Popis akce:</td>
            <td><textarea name="popis" rows="18" cols="70"><?PHP echo $popis; ?></textarea></td>
        </tr>

        <tr>
            <td>www stránky akce:</td>
            <td><input type="text" name="url" value="<?PHP echo $url; ?>" size="50"></td>
        </tr>
        <tr>
            <td>Název anglicky:</td>
            <td><input type="text" name="nazev_en" value="<?PHP echo $nazev_en; ?>" size="50"></td>
        </tr>
        <tr>
            <td>Popis anglicky:</td>
            <td><textarea name="popis_en" rows="2" cols="40"><?PHP echo $popis_en; ?></textarea></td>
        </tr>
        <tr>
            <td colspan="2" class="table_separator">Organizátor:</td>
        </tr>
        <tr>
            <td valign="top">Organizátor:</td>
            <?PHP
            if ($full_rights) { ?>
                <td><select name="id_m">
                        <?PHP if ($all == 1) {
                            $JOIN = " WHERE m.disable='N'";
                            $odkaz = "<a href=\"" . $PHP_SELF . "?men=men15.1.0.0&edit=$edit&id_a=$id_a\">Zobraz pouze organizátory</a>";
                        } else {
                            $JOIN = " INNER JOIN mc_prava AS p ON m.id_m=p.id_m WHERE m.disable='N' GROUP BY m.id_m ";
                            $odkaz = "<a href=\"" . $PHP_SELF . "?men=men15.1.0.0&edit=$edit&id_a=$id_a&all=1\">Zobraz všechny uživatele</a>";
                        }
                        $sql = "SELECT m.id_m, m.jmeno, m.prijmeni FROM m_members AS m $JOIN ORDER BY m.prijmeni, m.jmeno";
                        $tmp = $db->Query($sql);
                        while ($radek = $db->FetchArray($tmp)) { ?>
                            <option value="<?PHP echo $radek["id_m"]; ?>"<?PHP if ($radek["id_m"] == $id_owner) {
                                echo " selected";
                            } ?>><?PHP echo $radek["prijmeni"] . " " . $radek["jmeno"] . " (" . $radek["id_m"] . ")"; ?></option>
                        <?PHP } ?>
                    </select> <?PHP echo $odkaz; ?>
                </td>
                <?PHP
            } else { ?>
                <td><input type="hidden" name="id_m" value="<?PHP echo $id_m; ?>">
                    <?PHP if ($id_m == $a_user["id_m"]) {
                        echo $a_user["jmeno"] . " " . $a_user["prijmeni"];
                    } else {
                        $SQL = "SELECT jmeno, prijmeni FROM m_members WHERE id_m = $id_m";
                        $rs = $db->Query($SQL);
                        $r = $db->FetchArray($rs);
                        echo $r["jmeno"] . " " . $r["prijmeni"];
                    } ?></td>
                <?PHP
            } ?>
        </tr>


        <tr>
            <td>Kontaktní email:</td>
            <td><input type="text" name="email" value="<?PHP echo $email; ?>" size="50"></td>
        </tr>
        <tr>
            <td>Kontaktní telefon:</td>
            <td><input type="text" name="telefon" value="<?PHP echo $telefon; ?>" size="50"></td>
        </tr>





        <tr>
            <td>Spoluorganizátor:</td>
            <td><select name="id_editor">
                    <?PHP
                    $sql = "SELECT m.id_m, m.jmeno, m.prijmeni, m.clen_cislo
		FROM mensaweb.m_members AS m
		WHERE m.disable='N' AND prizpevky>=(year(now())-1)
		ORDER BY m.prijmeni, m.jmeno";

                    $tmp = $db->Query($sql);

                    while ($radek = $db->FetchArray($tmp)) { ?>
                        <option value="<?PHP echo $radek["id_m"]; ?>"<?PHP if ($radek["id_m"] == $id_editor) {
                            echo " selected";
                        } ?>><?PHP echo "{$radek['prijmeni']} {$radek['jmeno']} ({$radek['clen_cislo']})"; ?></option>
                    <?PHP } ?>
                </select>
            </td>
        </tr>






        <tr>
            <td colspan="2" class="table_separator">Zatřídění:</td>
        </tr>
        <?PHP
        $sql = "SELECT t.*, p.id_m FROM mc_typy_akci AS t INNER JOIN mc_prava AS p ON (t.id_t = p.id_t AND p.id_m = $id_m) GROUP BY t.typ ORDER BY t.typ ";
        $tmp = $db->Query($sql);
        $options = "";
        while ($radek = $db->FetchArray($tmp)) {
            if ($radek["typ"] == $typ) {
                $selected = " selected";
            } else {
                $selected = "";
            }
            $options .= "<option value=\"" . $radek["typ"] . "\"" . $selected . ">" . $arr_typy[$radek["typ"]] . "</option>";
        }
        ?>
        <tr>
            <td>Typ akce:</td>
            <td><select name="typ">
                    <option value="0" selected>nezatříděné</option>
                    <?PHP echo $options; ?>
                </select></td>
        </tr>
        <?PHP
        $sql = "SELECT t.*, p.id_m FROM mc_typy_akci AS t LEFT OUTER JOIN mc_prava AS p ON (t.id_t = p.id_t AND p.id_m = $id_m) WHERE typ LIKE 'ms' ORDER BY t.typ_nazev";
        $tmp = $db->Query($sql);
        $options = "";
        $prava_all = false;
        while ($radek = $db->FetchArray($tmp)) {
            if ($radek["typ_nazev"] == $STR_ALL && $radek["id_m"] == $id_m) { //kazdy typ akce ma na prvnim miste na seznamu polozku all
                $prava_all = true;
            } elseif ($prava_all || $radek["id_m"] == $id_m) {
                if ($radek["id_t"] == $ms) {
                    $selected = " selected";
                } else {
                    $selected = "";
                }
                $options .= "<option value=\"" . $radek["id_t"] . "\"" . $selected . ">" . $radek["typ_nazev"] . "</option>";
            }
        }
        if (strlen($options) > 0) {
            ?>
            <tr>
                <td>Pořádáno místní skupinou:</td>
                <td><select name="ms">
                        <option value="0" selected>ne</option>
                        <?PHP echo $options; ?>
                    </select></td>
            </tr>
        <?PHP }
        $sql = "SELECT t.*, p.id_m FROM mc_typy_akci AS t LEFT OUTER JOIN mc_prava AS p ON (t.id_t = p.id_t AND p.id_m = $id_m) WHERE typ LIKE 'sig' ORDER BY t.typ_nazev";
        $tmp = $db->Query($sql);
        $options = "";
        $prava_all = false;
        while ($radek = $db->FetchArray($tmp)) {
            if ($radek["typ_nazev"] == $STR_ALL && $radek["id_m"] == $id_m) { //kazdy typ akce ma na prvnim miste na seznamu polozku all
                $prava_all = true;
            } elseif ($prava_all || $radek["id_m"] == $id_m) {
                if ($radek["id_t"] == $sig) {
                    $selected = " selected";
                } else {
                    $selected = "";
                }
                $options .= "<option value=\"" . $radek["id_t"] . "\"" . $selected . ">" . $radek["typ_nazev"] . "</option>";
            }
        }
        if (strlen($options) > 0) {
            ?>
            <tr>
                <td>Pořádáno SIGem:</td>
                <td><select name="sig">
                        <option value="0" selected>ne</option>
                        <?PHP echo $options; ?>
                    </select></td>
            </tr>
        <?PHP }
        $sql = "SELECT t.*, p.id_m FROM mc_typy_akci AS t LEFT OUTER JOIN mc_prava AS p ON (t.id_t = p.id_t AND p.id_m = $id_m) WHERE typ LIKE 'dm' ORDER BY t.typ_nazev";
        $tmp = $db->Query($sql);
        $options = "";
        $prava_all = false;
        while ($radek = $db->FetchArray($tmp)) {
            if ($radek["typ_nazev"] == $STR_ALL && $radek["id_m"] == $id_m) { //kazdy typ akce ma na prvnim miste na seznamu polozku all
                $prava_all = true;
            } elseif ($prava_all || $radek["id_m"] == $id_m) {
                if ($radek["id_t"] == $dm) {
                    $selected = " selected";
                } else {
                    $selected = "";
                }
                $options .= "<option value=\"" . $radek["id_t"] . "\"" . $selected . ">" . $radek["typ_nazev"] . "</option>";
            }
        }
        if (strlen($options) > 0) {
            ?>
            <tr>
                <td>Pořádáno Dětskou Mensou:</td>
                <td><select name="dm">
                        <option value="0" selected>ne</option>
                        <?PHP echo $options; ?>
                    </select></td>
            </tr>


        <?PHP }
        //
        //
        //
        //
        // seznam typu testovani
        $sql = "SELECT t.*, p.id_m FROM mc_typy_akci AS t LEFT OUTER JOIN mc_prava AS p ON (t.id_t = p.id_t AND p.id_m = $id_m)
            WHERE typ LIKE 'test' ORDER BY t.typ_nazev";
        $tmp = $db->Query($sql);
        $options = "";
        $prava_all = false;
        while ($radek = $db->FetchArray($tmp)) {
            if ($radek["typ_nazev"] == $STR_ALL && $radek["id_m"] == $id_m) { //kazdy typ akce ma na prvnim miste na seznamu polozku all
                $prava_all = true;
            } elseif ($prava_all || $radek["id_m"] == $id_m) {
                if ($radek["id_t"] == $test) {
                    $selected = " selected";
                } else {
                    $selected = "";
                }
                $options .= "<option value=\"" . $radek["id_t"] . "\"" . $selected . ">" . $radek["typ_nazev"] . "</option>";
            }
        }

        if (strlen($options) > 0) {
            ?>
            <tr>
                <td>Testování:</td>
                <td><select name="test">
                        <option value="0" selected>ne</option>
                        <?PHP echo $options; ?>
                    </select></td>
            </tr>


        <?PHP }
        $sql = "SELECT t.*, p.id_m FROM mc_typy_akci AS t LEFT OUTER JOIN mc_prava AS p ON (t.id_t = p.id_t AND p.id_m = $id_m) WHERE typ LIKE 'mensa' ORDER BY t.typ_nazev";
        $tmp = $db->Query($sql);
        $options = "";
        $prava_all = false;
        while ($radek = $db->FetchArray($tmp)) {
            if ($radek["typ_nazev"] == $STR_ALL && $radek["id_m"] == $id_m) { //kazdy typ akce ma na prvnim miste na seznamu polozku all
                $prava_all = true;
            } elseif ($prava_all || $radek["id_m"] == $id_m) {
                if ($radek["id_t"] == $mensa) {
                    $selected = " selected";
                } else {
                    $selected = "";
                }
                $options .= "<option value=\"" . $radek["id_t"] . "\"" . $selected . ">" . $radek["typ_nazev"] . "</option>";
            }
        }
        if (strlen($options) > 0) {
            ?>
            <tr>
                <td>Celomensovní akce:</td>
                <td><select name="mensa">
                        <option value="0" selected>ne</option>
                        <?PHP echo $options; ?>
                    </select></td>
            </tr>
        <?PHP } ?>


        <tr>

            <?PHP if (isset($id_a) && $id_a > 0 && $edit == 2){ // editace pravidelne akce?>
                <td colspan="2" class="table_separator" align="right">
                    <input type="submit" name="odeslat" value="Změnit jen aktuální termín">
                    <input type="submit" name="odeslat_all" value="Změnit všechny budoucí termíny">
                    <br> <input type="submit" name="delete" value="Smazat jen aktuální termín">
                    <input type="submit" name="delete_all" value="Smazat všechny budoucí termíny">
                </td>
                <?PHP
            } elseif (isset($id_a) && $id_a > 0 && $edit == 1){  // editace jednorazove akce?>


        <tr>
            <td></td>
            <td><input type="Checkbox" name="prihlaska"<?PHP if ($prihlaska == 1) {
                    echo " checked";
                }; ?> value="1"> chci připojit k akci přihlášku (netýká se testování, tam nabíhá automaticky)
            </td>
        </tr>
        <td colspan="2" class="table_separator" align="right">
            <input type="submit" name="odeslat" value="Vložit jako novou akci"
                   onclick="document.ins_action.id_a.value='';">
            <input type="submit" name="odeslat" value="Změnit">
            <input type="submit" name="delete" value="Smazat">
        </td>
    <?PHP } else { ?>
        <tr>
            <td></td>
            <td><input type="Checkbox" name="prihlaska"<?PHP if (($prihlaska ?? 0) == 1) {
                    echo " checked";
                } ?> value="1"> chci připojit k akci přihlášku (netýká se testování, tam nabíhá automaticky)
            </td>
        </tr>
        <td colspan="2" class="table_separator" align="right">
            <input type="submit" name="odeslat" value="Odeslat">
        </td>
    <?PHP } ?>
        </tr>
    </table>
</form>
</p>

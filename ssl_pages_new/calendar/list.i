<?PHP 
require_once("../ssl_library_new/calendar.class.l");

$calendar = new calendar($db);
$id_m = $a_user["id_m"];

if(access("delete", $men, $a_user["id_m"], $db)){
	$full_rights = true;
	$WHERE = " WHERE ";
} else { 
	$full_rights = false;
	$WHERE = " WHERE (id_owner=$id_m OR id_editor=$id_m)   AND ";
} 
if (strlen(@$old)==0){
	$old = 0;
}
if($old == 1){ // zobrazeni starsich akci
	$WHERE .= " date_end < Now()";
	$zobraz_old = "<a href=\"".$PHP_SELF."?men=men15.2.0.0\">Zobrazit aktuální akce</a>";
} else { // zobrazeni novych akci
	$WHERE .= " date_end > Now()";
	$zobraz_old = "<a href=\"".$PHP_SELF."?men=men15.2.0.0&old=1\">Zobrazit starší akce</a>";
}

$SQL = "SELECT * FROM mc_akce ".$WHERE." ORDER BY date_start";
$rs = $db->Query($SQL);

$STR_ALL = "0all";
	$arr_typy["test"] = "Testování IQ";
	$arr_typy["ms"] = "setkání místní skupiny";
	$arr_typy["sig"] = "setkání SIGu";
	$arr_typy["dm"] = "akce Dětské Mensy";
	$arr_typy["diag"] = "diagnostický den";
	$arr_typy["mensa"] = "celomensovní akce";
	$arr_typy["other"] = "ostatní";
	
$poc = $db->getNumRows($rs); 
$ret1 = "";
$ret2 = "";
$ret = "<table border=\"0\" cellspacing=\"1\" cellpadding=\"2\" class=\"table_formular\">";
	$ret .= "<tr><td colspan=5 align=\"right\">".$zobraz_old."</td></tr>";
if ($poc > 0){ 
	while ($radek = $db->FetchArray($rs)){
		if (strlen($radek["date_pre"]) > 0){
			$datum = "předběžné datum: ".$radek["date_pre"];
		} else {
			$datum = $calendar->write_date($radek["date_start"], $radek["date_end"]);
		}
		if ($radek["prihlaska"] == 1){
			$txt_prihl = "změnit přihlášku";
		} else {
			$txt_prihl = "vložit přihlášku";
		}
		if ($radek["id_parent"] == -1){
			//jednorazova akce
			$row = "<tr><td>".$datum."</td>";
			$row .= "<td><a href=\"/index.php?men=men15.0.0.0&id_a=".$radek["id_a"]."\">".$radek["nazev"]."</a></td>";
			$row .= "<td>".$radek["city"]."</td>";
			$row .= "<td><a href=\"/index.php?men=men15.1.0.0&edit=1&id_a=".$radek["id_a"]."\">edit</a></td>";
			 
				$row .= "<td><a href=\"/index.php?men=men15.6.0.0&edit=1&id_a=".$radek["id_a"]."\">$txt_prihl</a></td>"; 
//			$row .= "<td><a>del</a></td>";
			$row .= "</tr>";
			$ret1 .= $row;
		} else {  //pravidelna akce
			$row = "<tr><td>".$datum."</td>";
			$row .= "<td><a href=\"/index.php?men=men15.0.0.0&id_a=".$radek["id_a"]."\">".$radek["nazev"]."</a></td>";
			$row .= "<td>".$radek["city"]."</td>";
			$row .= "<td><a href=\"/index.php?men=men15.1.0.0&edit=2&id_a=".$radek["id_a"]."\" alt=\"editovat všechny termíny pravidelné akce\">edit</a></td>";
			 		$row .=  "<td><a href=\"/index.php?men=men15.6.0.0&edit=1&id_a=".$radek["id_a"]."\">$txt_prihl</a></td>"; 
			$row .= "</tr>";
			$ret2 .= $row;
		}
	}
	$ret .= "<tr><td colspan=5 class=\"table_separator\">Jednorázové akce:</td></tr>".$ret1;
	$ret .= "<tr><td colspan=5 class=\"table_separator\">Pravidelné akce:</td></tr>".$ret2."</table>";
} else {
	$ret .= "<tr><td colspan=5 class=\"table_separator\">Nemáte zadané žádné akce</td></tr></table>"; 
}
?>

<h4>Výpis mých akcí:</h4><p>
<?PHP 

echo $ret;
?>
</p>

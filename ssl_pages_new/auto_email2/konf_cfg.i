<h1>P<PERSON><PERSON><PERSON><PERSON> konferencí</h1>
<?php
/*
  Strán<PERSON> s nastavením odběru konferencí.
    originální jméno souboru: konf_cfg.i

  Změnovník
    2013-Jan-12, TK: Zavedení změnovníku a přidání popisu konferencí.

    2013-Feb-27, TK: Jméno databáze přesunuto do proměnné - konstanta nevyhovuje,
        nelze se na ní odkázat v řetězci - a pretty print.
        (Obojí jsou úpravy pro sandbox).

    2013-Mar-1, VK:
      - setřídění konferencí podle názvu a ošetření ID konference pro update stavu v databázi
      - přidání celkového počtu příspěvků v konferenci
      - přidání počtu příspěvků v dané konferenci za poslední měsíc

    2013-Mar-3, VK:
      - vý<PERSON>ěna celkového počtu příspěvků v konferenci za počet za poslední rok

    2013-Mar-8, PM:
      - doplnění linku na webové stránky

    2013-Apr-16, VK:
      - přidána třída konference_class.php zapouzdřující změny a statistiku přihlašování konferencí
      - uložení změn konferencí běžných uživatelů převedeno do procedury ulozeniDat();
      - funkce isAdmin převedena do třídy konference_class
      - na konci stránky přidán výpis statistiky

    2013-Apr-19, TK:
        kosmeticke zmeny ve vypise a slouceni ruznych verzi kodu.
    2013-Jun-24, VK:
        - převedení většiny selectů do konference_class.php
        - přidána proměnná $MENU_ID přepínající menu mezi vývojářským koutkem a produkční verzí
        - přidán výpis statistik přihlášení a odhlášení od dané konference

 */

// výběr databáze
//$DATABASE_NAME = "test"; // pro pouziti ve vyvojarskem koutku
$DATABASE_NAME = "mensaweb"; // pro pouziti v ostrem provozu
// výběr menu
$MENU_ID=$_REQUEST['men'];   // Vývojářský koutek

// nahraje definice konferencí
// nejsou uloženy v db. ale jako prosté pole v PHP
require_once '../ssl_pages_new/auto_email2/konference.config.i';
require_once 'konference_class.php';             // třída statistik přihlašování konferencí




// hodnoty přejaté z jiných skriptů
global $konference; // definice konferencí
global $a_user;     // definice uživatele, poskytne jádro intranetu
global $db;         // databázový objekt, poskytne jádro intranetu

// Vytvoření objektu a nasetování globálních hodnot do objektu konference
$o_konference=new konference($db,$DATABASE_NAME,$a_user,$konference);




// zapni pro nováčky plné logování chyb
error_reporting (-1);
ini_set("display_errors", 1);
ini_set("html_errors", TRUE);






/**
 * Porovnání řetězů použité pro seřazní pole konferencí
 *
 * Řetězce jsou ve windows kódování, převádím je na ascii,
 * protože jinak se bdou háčkovaná písnema řadit až na konec neb
 * strcasecmp neumí s češtinou pracovat.
 *
 * Není to perfektní ale dotačující.
 *
 * <AUTHOR> Kubeš
 * @see definice funkce pro použití při řazení pole: usort
 * @example usort ($konfgrp, "cmp");
 *
 * @param string $a
 * @param string $b
 * @return int
 */
function cmp($a, $b)
{
    return 0 <= strcasecmp(iconv('utf-8', 'US-ASCII//TRANSLIT', $a['name']), iconv('utf-8', 'US-ASCII//TRANSLIT', $b['name']));
}







///////////////////////////////////////////////////////////////////////////////
// ULOZENI DAT
// uloží změny a zapíše je do statistik
$o_konference->zapisZmenu();

///////////////////////////////////////////////////////////////////////////////
// ULOZENI DAT - ADMINISTRACE
// banování a povolování
echo $o_konference->setBlok(isset($_POST['kick'])?$_POST['kick']:"",isset($_POST['idk'])?(int)$_POST['idk']:"");
echo $o_konference->unsetBlok(isset($_POST['unban'])?$_POST['unban']:"",isset($_POST['idk'])?(int)$_POST['idk']:"");









///////////////////////////////////////////////////////////////////////////////
///////////////////////////////////////////////////////////////////////////////
//ZOBRAZENI STRANKY
$ukonf = array(
    KONF_MEN => array(),
    KONF_MS => array(),
    KONF_SIG => array(),
);

$total_users=$o_konference->getTotalUsers();    // získá celkový počet členů v konferencích
$total_konf=$o_konference->getPocetUserConf();  // získá počet uživatelů v každé konferenci
$konerence_pocet_celkem=$o_konference->getPocetPrispevku(); // získá počet příspěvku v každé konferenci celkem
$konference_pocet_rok=$o_konference->dejPocetMailuDoba("INTERVAL 1 YEAR"); //Získání počtu příspěvků v konferencích za poslední rok
$konference_pocet_mesic=$o_konference->dejPocetMailuDoba("INTERVAL 1 MONTH");   // Získání počtu příspěvků v konferencích za poslední měsíc






/*
 * výběr statistik přihlášení a odhlášení k konference
 * $_GET['stat']=y je poslední rok, m= poslední měsíc, d=poslední den
 * vrací pole $zmeny=array(id_konf, pocetplus, pocetminus)
 */
// defualt je za mesic
if(!isset($_GET['stat'])){
    $dobastat='m';
}
elseif(  strlen($_GET['stat']) !=1){
    $dobastat='m';
}
else{
    $dobastat=$_GET['stat'];
}








$zmeny=$o_konference->dejPocetZmen($dobastat);  // získání statistik změn přihlášení do konferencí za dané období
$admkonf = array();
foreach ($konference as $id => $konf)
{
    //  $konf['idcko'] je pomocná proměnná pro výpis seznamu konferencí pro nepromíchání id při sortování seznamu konferencí
    $konf['idcko']=$id;
    //  $konf['rok_prispevku']  je index pole pro přilinkování údaje o počtu příspěvků za poslední rok do řádku dané konference do pole se seznamem konferencí
    $konf['rok_prispevku']=isset($konference_pocet_rok[$id])?$konference_pocet_rok[$id]:0;
    //  $konf['mesic_prispevku']  je index pole pro přilinkování údaje o počtu příspěvků za poslední měsíc do řádku dané konference do pole se seznamem konferencí
    $konf['mesic_prispevku']=isset($konference_pocet_mesic[$id])?$konference_pocet_mesic[$id]:0;
    $konf['banned'] = false;
    if (isset($total_konf[$id]))
    {
        if ($konf['type'] == KONF_MEN)
        {
            $konf['usersCount'] = $total_users - $total_konf[$id];
        }
        else
        {
            $konf['usersCount'] = $total_konf[$id];
        }
    }
    else
    {
        $konf['usersCount'] = 0;
    }
    switch ($konf['type']) {
        case KONF_MEN:
            $konf['checked'] = true;
            break;
        case KONF_MS:
        case KONF_SIG:
            $konf['checked'] = false;
            break;
        default:
            continue 2;
    }

    $konf['zmenaplus']=isset($zmeny[$id]['pocetplus'])?$zmeny[$id]['pocetplus']:0;
    $konf['zmenaminus']=isset($zmeny[$id]['pocetminus'])?$zmeny[$id]['pocetminus']:0;

    $ukonf[$konf['type']][$id] = $konf;
    if ($o_konference->is_admin($konf))
    {
        $admkonf[$id] = $konf;
    }
}



$q_user_config = $db->query("
    SELECT id_konf, banned
    FROM {$DATABASE_NAME}.m_ae_konfcfg
    WHERE id_m='{$a_user['id_m']}'");

while ($cfgline = $db->FetchAssoc($q_user_config)) {
    if ($cfgline['banned'])
    {
        //uzivatel je z konference zablokovan
        if (isset($konference[$cfgline['id_konf']]))
        {
            $ukonf[$konference[$cfgline['id_konf']]['type']][$cfgline['id_konf']]['banned'] = true;
        }
    }
    else
    {
        //uzivatel ma ke konferenci pozitivni konfiguracni radek; tzn vypnuta mensovni konference nebo zapnuta MS/SIG
        if (isset($konference[$cfgline['id_konf']]))
        {
            $ukonf[$konference[$cfgline['id_konf']]['type']][$cfgline['id_konf']]['checked'] =
                $konference[$cfgline['id_konf']]['type'] == KONF_MEN ? false : true;
        }
    }
}
$grp_names = array(
    KONF_MEN => 'Oficiální konference',
    KONF_MS => 'Konference místních skupin',
    KONF_SIG => 'Konference SIGů',
);





////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// vypis stranky
?>







<p><span  style='font-weight: bold;'>Konference jsou místem pro oznamování akcí zájmových i místních skupin. Přihlaste se, abyste se dozvěděli o všech akcích, které Vás mohou zajímat.</span> Mensa pečlivě dbá, aby konference byly využívány pouze k účelu, pro který jsou založeny, nemusíte se proto obávat nevyžádaných zpráv. Po dokončení výběru nezapomeňte svojí akci uložit.
</p>


<p>V intranetu registrujeme celkem <span style="color:#339"><?php echo $total_users; ?></span> uživatelů s aktivním profilem a platným e-mailem
    (zahrnuje i hosty a členy ostatních národních Mens).</p>


<p>Statistiky přihlášení a odhlášení v konferencích si můžete zobrazit za
    <a href="index.php?men=<?php echo $MENU_ID; ?>&amp;stat=y" title="Statistika přihlášení a odhlášení v konferencích za poslední rok">poslední rok</a>,
    <a href="index.php?men=<?php echo $MENU_ID; ?>&amp;stat=m" title="Statistika přihlášení a odhlášení v konferencích za poslední měsíc">poslední měsíc</a>,
    <a href="index.php?men=<?php echo $MENU_ID; ?>&amp;stat=w" title="Statistika přihlášení a odhlášení v konferencích za poslední týden">poslední týden</a> nebo
    <a href="index.php?men=<?php echo $MENU_ID; ?>&amp;stat=d" title="Statistika přihlášení a odhlášení v konferencích za poslední den">poslední den</a>.
</p>





<!-- vypis konferenci k prihlaseni -->
<form method="post" action="">

    <?php

    // výpis
    foreach ($ukonf as $type => $konfgrp)
    {
        // serad album podle nazvu konference
        usort ($konfgrp, 'cmp');

        echo '<h3 style="margin-top: 2em;">' . htmlspecialchars($grp_names[$type]) . '</h3>';

        // vlož soubory s popisem
        if ($grp_names[$type] == 'Konference SIGů')
            include ('../ssl_pages_new/auto_email2/info_sig.php');
        if ($grp_names[$type] == 'Konference místních skupin')
            include ('../ssl_pages_new/auto_email2/info_ms.php');

        /*
         * $o_konference->getIntervalStatistiky() vrací text s informací o intervalu statistiky změn
         */
        echo '
        <table><tr>
        <td>&nbsp;</td>
        <th>Konference</th>
        <th>Link</th>
        <th style="text-align: right;">Odebírá<br>členů</th>
        <th style="text-align: right;">Změna odběru za<br>'.$o_konference->getIntervalStatistiky()
        .'</th>
        <th style="text-align: right;">Příspěvků<br>posl.&nbsp;rok</th>
        <th style="text-align: right;">Příspěvků<br>posl.&nbsp;měsíc</th>
        <th>Popis</th></tr>';




        foreach ($konfgrp as $id => $konf)
        {
            $checked = !$konf['banned'] && $konf['checked'] ? 'checked' : '';
            $banned   = $konf['banned'] ? 'disabled' : '';
            $banned_l = $konf['banned'] ? 'style="color:#999"' : '';

            /*
            $konf['idcko'] je použité místo ID proto, aby bylo možno setřídit podle názvu konference
            a nevázat se při tom na pořadí v HTML ale na id konference
            */
            echo "
            <tr>
            <td><input
                type='checkbox'
                {$checked}
                {$banned}
                name='ckonf[{$konf['idcko']}]'
                id='ckonf{$konf['idcko']}'
                value='{$konf['idcko']}'>
            </td>

            <td><label for='ckonf[{$konf['idcko']}]' {$banned_l}>" . htmlspecialchars($konf['name']) . "</label></td>

            <td style='text-align:left'>" . (isset($konf['link']) ? $konf['link'] : '') . '</td>

            <td style="text-align:right; padding-right:1em;">' . $konf['usersCount'] . '</td>' .

            '<td style="text-align:right; padding-right:1em;">+' . $konf['zmenaplus'] . '/-' . $konf['zmenaminus'] . '</td>' .
            "<td style='text-align:right;  padding-right:1em;'>
            <a target='_blank' href='./index.php?men=men20.3.0.0&filter=kon{$konf['idcko']}'>{$konf['rok_prispevku']}</td>" .

            "<td style='text-align:right;  padding-right:1em;'>
            <a target='_blank' href='./index.php?men=men20.3.0.0&filter=kon{$konf['idcko']}'>{$konf['mesic_prispevku']}</a></td>" .

            '<td style="text-align:left">' . (isset($konf['popis']) ? $konf['popis'] : '') . '</td>

            </tr>';
        }
        echo '</table>';
        echo '<p>&nbsp;</p>';
    }
    ?>

    <p style="background-color: limegreen; padding: 1em; text-align: center;">
        <input type="submit" name="submitted" value="Uložit">
    </p>
</form>







<?php
//funkce spravcu konferenci
if (!empty($admkonf))
{
    ?>
    <h3 style="margin-top: 3em;">Jste správcem následujících konferencí</h3>
    <table style="border: 0;">
        <tr>
            <th>Název konference</th>
            <th>Zablokovat uživatele<br>(zadejte e-mail)</th>
            <th>Odblokovat uživatele<br>(zadejte e-mail)</th>
        </tr>



    <?php
    foreach ($admkonf as $id => $konf)
    {
        echo '
        <tr>
          <td>' . htmlspecialchars($konf['name']) . '</td>
          <td>
            <form method="post" action="">
              <input type="text" name="kick">
              <input type="hidden" name="idk" value="' . $id . '">
              <input type="submit" value="Vyhodit">
            </form>
          </td>
          <td>
            <form method="post" action="">
              <input type="text" name="unban">
              <input type="hidden" name="idk" value="' . $id . '">
              <input type="submit" value="Odblokovat">
            </form>
          </td>
        </tr>';



        echo '<tr>';
        echo "<td colspan=3>";
        $tmp = $db->Query("
             SELECT
                m.id_m, m.jmeno, m.prijmeni
             FROM
                {$DATABASE_NAME}.m_members m
             WHERE
                m.disable='N'
                AND m.prizpevky >= (year(now())-1)
                AND m.id_m IN
                    (SELECT id_m FROM {$DATABASE_NAME}.m_ae_konfcfg
                    WHERE id_konf=" . $id . ")
            ORDER BY
                m.prijmeni, m.jmeno");
        while ($row = $db->FetchArray($tmp))
        {
            echo "<a target='_blank' href=\"./index.php?men=men4.1.0.0&s_content=view.i&id_m=" . $row["id_m"] . "\">" . $row["jmeno"] . " " . $row["prijmeni"] . "</a><br>";
        }
        echo "</td>";
        echo '</tr>';
    }
}
?>
    </table>





<h3 style="margin-top: 3em;">Podrobný výpis statistiky o přihlašování</h3>
<?php if(true){ ?>
<table>
    <tr><td colspan="3">
            <form method="post" action="">
                <label>Období: </label><select name="doba">
                    <option value="0"<?php echo(!isset($_POST['doba']) || $_POST['doba']==0)? ' selected="selected"': ""; ?>>Posledních 24 hodin (default)</option>
                    <option value="1"<?php echo(isset($_POST['doba']) && $_POST['doba']==1)? ' selected="selected"': ""; ?>>Poslední týden</option>
                    <option value="2"<?php echo(isset($_POST['doba']) && $_POST['doba']==2)? ' selected="selected"': ""; ?>>Poslední měsíc</option>
                    <option value="3"<?php echo(isset($_POST['doba']) && $_POST['doba']==3)? ' selected="selected"': ""; ?>>Poslední rok</option>
                </select>
                <label>Stav: </label><select name="priznak">
                    <option value="0"<?php echo(!isset($_POST['priznak']) || $_POST['priznak']==0)? ' selected="selected"': ""; ?>>Přihlášení i odhlášení (default)</option>
                    <option value="1"<?php echo(isset($_POST['priznak']) && $_POST['priznak']==1)? ' selected="selected"': ""; ?>>Přihlášení</option>
                    <option value="2"<?php echo(isset($_POST['priznak']) && $_POST['priznak']==2)? ' selected="selected"': ""; ?>>Odhlášení</option>
                </select>
                <input type="submit" name="substat" value="Nová statistika">
            </form>
        </td>
    </tr>
    <?php
    echo $o_konference->getStat(isset($_POST['doba'])?$_POST['doba']:'',isset($_POST['priznak'])?$_POST['priznak']:'');
    ?>
</table>
<?php } ?>










<p>&nbsp;</p>

<p>Nastavení příjmu obecných zpráv naleznete v rubrice <a href="index.php?men=men20.1.0.0">příjem zpráv</a>.</p>


<p>Nápovědu v popisu funkčnosti naleznete v <a href="/document.php?men=men14.3.0.0&id_c=232" title="nápověda systému zpráv">zadávací dokumentaci</a>.</p>







<p style="text-align:right; margin-top:8em;"><em>Stránku naprogramoval Richard Ejem a upravovali Vladimír Kapic a Tomáš Kubeš,
poslední úprava 27. června 2013.</em></p>
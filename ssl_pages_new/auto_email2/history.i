<?php

use helpers\Html;

require_once 'konference.config.i';
require_once 'config.i';

$q_kraje = $db->query("SELECT id_kraj, kraj_abbr FROM m_list_kraj WHERE kraj_name NOT LIKE '---%' ORDER BY id_kraj"); //podminkou "not like" se vyradi kraj "----neuveden----"
$kraje = array();
while ($kraj = $db->FetchAssoc($q_kraje)){
  $kraje[$kraj['id_kraj']] = $kraj['kraj_abbr']; 
} 
if (isset($_GET['detail'])) {
//DETAIL ZPRAVY
?><p><a href="index.php?men=men20.3.0.0">Zpět</a></p><?php
$idmail = (int)$_GET['detail'];

$zprava = $db->query(
    "SELECT
      m_ae_mails.id_mail,
      CONCAT(m_members.prijmeni, ' ', m_members.jmeno) AS jmeno,
      m_members.id_m,
      m_members.email,
      DATE_FORMAT(m_ae_mails.cas, '%e.%c.%Y %k:%i') AS datum,
      m_ae_mails.typ,
      m_ae_mails.kraje,
      m_ae_mails.pro_uziv,
      m_ae_mails.konference,
      m_ae_types.type_name,
      m_ae_mails.subject,
      m_ae_mails.text_body,
      m_ae_mails.html_body
    FROM
      m_ae_mails
      LEFT JOIN m_members ON m_ae_mails.id_m=m_members.id_m
      LEFT JOIN m_ae_types ON m_ae_mails.typ=m_ae_types.id_type
    WHERE id_mail=$idmail");
if ($db->getNumRows($zprava) == 0){
?>
<p class="error">Zpráva neexistuje.</p>
<?php
  return;
}
$zprava = $db->FetchAssoc($zprava);

if ($zprava['typ'] === null){ //konference
  $subtyp = $konference[$zprava['konference']]['name'];
  $typ = "KONFERENCE / $subtyp";
} else { //obecna
  $subtyp = $zprava['type_name'];
  $typ = "OBECNÁ / $subtyp";
}
echo "<table class='mailzprava_meta'>
      <tr><th>Od:</th><td>".htmlspecialchars($zprava['jmeno']).' &lt;<a href="/index.php?men=men4.1.0.0&amp;s_content=view.i&amp;id_m='.$zprava['id_m'].'">'.htmlspecialchars($zprava['email'])."</a>&gt;</td></tr>
      <tr><th>Datum:</th><td>".htmlspecialchars($zprava['datum'])."</td></tr>
      <tr><th>Typ:</th><td>$typ</td></tr>";
if ($zprava['typ'] !== null){
  $kr = array();
  $uz = array();
  for ($i = 0; $i < 32; $i++){
    if ($zprava['kraje'] & pow(2, $i)){
      $kr[] = $kraje[$i];
    }
    if ($zprava['pro_uziv'] & pow(2, $i)){
      $uz[] = $uzivatele[$i];
    }
  }
  $kr = implode(', ', $kr);
  $uz = implode(', ', $uz); 
  echo "<tr><th>Do krajů:</th><td>$kr</td></tr>
        <tr><th>Uživatelům:</th><td>$uz</td></tr>";
}
echo "<tr><th>Předmět:</th><td>".htmlspecialchars($zprava['subject'])."</td></tr>
      </table>";

$body = (empty($zprava['text_body']) && !empty($zprava['html_body']))
    ? strip_tags($zprava['html_body'], '<br />')
    : htmlspecialchars($zprava['text_body']);

$body = Html::deduplicateLineBreakTags($body);

// ------------------------------------------------------------------------------------------------------------------
// --- Prevod odkazu na aktivni hyperlinky --------------------------------------------------------------------------
// --- Upraveno podle https://www.diginomad.cz/jak-v-php-vymenit-vsechny-url-v-textu-za-aktivni-link/ ---------------
// ------------------------------------------------------------------------------------------------------------------

// doplneni http:// pred vsechny odkazy s www, co jej nemaji
$body = str_replace( "www.", "http://www.", $body);

// smaz vznikle duplikaty
$body = str_replace( "http://http://www.", "http://www.", $body);
$body = str_replace( "https://http://www.", "https://www.", $body);

// nastavení regularniho filtru
$find_url = "/(http|https)\:\/\/[a-zA-Z0-9\-\.]+\.[a-zA-Z]{2,3}(\/\S*)?/";

// najdi vsechny url v textu
preg_match_all($find_url, $body, $url);

// vytvor ze vsech odkazu hyperlink
for ($i = 0; $i < count($url[0]); $i++) {

    $body = str_replace($url[0][$i], "<a href='".$url[0][$i]."' target='_blank'> ".$url[0][$i]." </a>" , $body);
}

$body = nl2br($body); // zalamovani radku, puvodne to bylo na razku 78, ale pralo se to s doplnovanim hyperlinku

// ---------------------------------------------------------------------------------------------------------------------

echo "<p>$body</p>";
if (!empty($zprava['html_body'])){
?>
<p>Tato zpráva byla zaslána i v HTML verzi.<br />
<a onclick="window.open('document.php?men=men20.3.0.0&html=<?php echo $zprava['id_m']; ?>', 'mensahtmlmail', 'width=800,height=600,left=50,top=50,toolbar=no,menubar=no,status=no,directories=no'); return false"
  href="document.php?men=men20.3.0.0&html=<?php echo $zprava['id_mail']; ?>" target="_blank">Zobrazit HTML verzi</a>
</p>
<?php
}
$q_atts=$db->query("SELECT id_att, filename FROM m_ae_atts WHERE id_mail={$zprava['id_mail']}");
if ($db->getNumRows($q_atts) > 0){
  echo '<p><b>Přílohy:</b><br /><br />';
  while ($att = $db->FetchAssoc($q_atts)){
    if ($att['filename']){
      echo "<a href='document.php?men=men20.3.0.0&amp;file={$att['id_att']}'>".htmlspecialchars($att['filename'])."</a><br />";
    }
  }
  echo '</p>';
}

} else {
//SEZNAM ZPRAV 
if (isset($_GET['filter'])){
  $filter = $_GET['filter'];
  $filter_class = substr($filter,0, 3);
  if (strlen($filter) > 3){
    $filter_id = (int)substr($filter, 3);
  }
} else {
  $filter = null;
}

?>
<form method="get" action="">
<input type="hidden" name="men" value="<?php echo htmlspecialchars($_GET['men']); ?>">
<select name="filter" onchange="this.form.submit()">
<option value="">Vše</option>
<optgroup label="Obecné">
<option value="gen" <?php if ($filter=='gen') echo 'selected'; ?>>Obecné-Vše</option>
<?php
  $q_obecne =  $db->query("SELECT id_type, type_name FROM m_ae_types");
  while ($typ = $db->FetchAssoc($q_obecne)){
  
    $sel = $filter=='gen'.$typ['id_type'] ? ' selected' : ''; 
    echo '<option value="gen'.$typ['id_type'].'"'.$sel.'>'.htmlspecialchars($typ['type_name']).'</option>';
  }
?>
<optgroup label="Konference">
<option value="kon" <?php if ($filter=='kon') echo 'selected'; ?>>Konference-Vše</option>
<?php
  foreach ($konference as $id=>$konf){
    $sel = $filter=='kon'.$id ? ' selected' : '';
    echo '<option value="kon'.$id.'"'.$sel.'>'.htmlspecialchars($konf['name']).'</option>';
  }
?>
</select>
<input type="submit" id="filtersubmit" value="ok">
<script type="text/javascript">
document.getElementById('filtersubmit').style.display='none';
</script>
</form>
<?php
  $where = '';
  if (isset($filter)){
    if ($filter_class == 'gen'){
      if (empty($filter_id)){
        $where = ' WHERE typ IS NOT NULL'; 
      } else {
        $where = " WHERE typ=$filter_id";
      }
    }
    if ($filter_class == 'kon'){
      if (empty($filter_id)){
        $where = ' WHERE konference IS NOT NULL'; 
      } else {
        $where = " WHERE konference=$filter_id";
      }
    }
  }
  //STRANKOVANI
require_once '../ssl_library_new/pager.class.php';
$per_page = 10;
if (isset($_GET['page'])){
  $page = (int)$_GET['page'];
} else {
  $page = 1;
}
list($count) = $db->FetchRow($db->query("SELECT COUNT(id_mail) FROM m_ae_mails $where"));
if ($page > ceil($count/$per_page)){
  $page = 1;
}
$pager = new pager($page, $count, $per_page);
  $q_zpravy = $db->query(
    "SELECT
      m_members.id_m,
      m_ae_mails.id_mail,
      CONCAT(m_members.prijmeni, ' ', m_members.jmeno) AS jmeno,
      m_members.email,
      DATE_FORMAT(m_ae_mails.cas, '%e.%c.%Y %k:%i') AS datum,
      m_ae_mails.typ,
      m_ae_mails.kraje,
      m_ae_mails.pro_uziv,
      m_ae_mails.konference,
      m_ae_types.type_name,
      m_ae_mails.subject,
      m_ae_mails.text_body,
      m_ae_mails.html_body
    FROM
      m_ae_mails
      LEFT JOIN m_members ON m_ae_mails.id_m=m_members.id_m
      LEFT JOIN m_ae_types ON m_ae_mails.typ=m_ae_types.id_type
    $where
    ORDER BY cas DESC
    LIMIT ".$pager->sql_limit());
  while ($zprava = $db->FetchAssoc($q_zpravy)){
    $typ = '';
    if (empty($filter)){
      if ($zprava['typ'] === null) {
        $typ = 'KONFERENCE / '; 
      } else {
        $typ = 'OBECNÁ / ';
      }
    }
    if (empty($filter_id)){
      if ($zprava['typ'] === null) {
        $typ .= htmlspecialchars($konference[$zprava['konference']]['name']);
      } else {
        $typ .= htmlspecialchars($zprava['type_name']);
      }
    } else {
      $typ = null;
    }

    $body = (empty($zprava['text_body']) && !empty($zprava['html_body']))
        ? Html::deduplicateLineBreakTags(strip_tags($zprava['html_body'], '<br />'))
        : nl2br(htmlspecialchars($zprava['text_body']));

    $body = Html::deduplicateLineBreakTags($body);

    $msg_link='index.php?men=men20.3.0.0&amp;detail='.$zprava['id_mail'];

    echo 
    "<div class='mailzprava'>
      <a class='mailzprava_expand' href='$msg_link'>Celá zpráva &gt;</a>
      <table class='mailzprava_meta'>
      <tr><th>Od:</th><td>".htmlspecialchars($zprava['jmeno']).' &lt;<a href="/index.php?men=men4.1.0.0&amp;s_content=view.i&amp;id_m='.$zprava['id_m'].'">'.htmlspecialchars($zprava['email'], ENT_IGNORE, "ISO-8859-1")."</a>&gt;</td></tr>
      <tr><th>Datum:</th><td>".htmlspecialchars($zprava['datum'])."</td></tr>".
      (empty($typ) ? '' : 
      "<tr><th>Typ:</th><td>$typ</td></tr>");
    if ($zprava['typ'] !== null){
      $kr = array();
      $uz = array();
      for ($i = 0; $i < 32; $i++){
        if ($zprava['kraje'] & pow(2, $i)){
          $kr[] = $kraje[$i];
        }
        if ($zprava['pro_uziv'] & pow(2, $i)){
          $uz[] = $uzivatele[$i];
        }
      }
      $kr = implode(', ', $kr);
      $uz = implode(', ', $uz); 
      echo "<tr><th>Do krajů:</th><td>$kr</td></tr>
            <tr><th>Uživatelům:</th><td>$uz</td></tr>";
    }
    echo "<tr><th>Předmět:</th><td><a href='$msg_link'>".htmlspecialchars($zprava['subject'])."</a></td></tr>
      </table>
      <p>".mb_substr($body, 0, 100)."</p></div>";
  }

  echo $pager->pager_html("index.php?men=men20.3.0.0".(isset($_GET['filter']) ? '&filter='.$_GET['filter'] : '')."&page=%PGID%");
?>
<?php } ?>
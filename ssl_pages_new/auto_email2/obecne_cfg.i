<h1><PERSON><PERSON><PERSON><PERSON><PERSON> obe<PERSON>n<PERSON>ch zpráv</h1>
<?php
/*
  Stránka s nastavením odběru zpráv.
  originální jméno souboru: obecne_cfg.i

  Změnovník

  2013-Apr-03, TK: Opravy chyb.

  2013-Mar-31, TK: Zavedení změnovníku,
    informace o počtech přihlášených, pretty print.

  2014-Apr-1, PM, doplněna kategorie 9 - nekomerčn<PERSON> zprávy
*/

// funkce na výpis tabulky
require_once("../ssl_library_new/draw_table_from_query.i");

// hodnoty přejaté z jiných skriptů
global $a_user;     // definice uživatele, poskytne jádro intranetu
global $db;         // databázový objekt, poskytne jádro intranetu

?>

<style>
    tr.even
    {
        background-color: #eeeeff;
    }

    td.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, td.<PERSON>pr<PERSON><PERSON>_za_m<PERSON>s<PERSON><PERSON>
    {
        text-align: right;
        padding-right: 0.5em;
    }

    td.Volitelné_zp<PERSON>, td.Obecné_zprávy
    {
        width: 20em;
    }
</style>





<script type="text/javascript">
    function chgcol(number, state){
        var tbl = document.getElementById('sgtbl');
        for (var i = 2; i < tbl.rows.length; i++){
            var input = tbl.rows[i].cells[number+2].getElementsByTagName('input');
            if (input) {
                input[0].checked = state;
            }
        }
    }
    function chgrow(number, state){
        var tbl = document.getElementById('sgtbl');
        for (var i = 2; i < tbl.rows[number+2].cells.length; i++){
            var input = tbl.rows[number+2].cells[i].getElementsByTagName('input');
            if (input) {
                input[0].checked = state;
            }
        }
    }
</script>







<form method="post" action="">
    <table id="sgtbl">
        <?php
        //nacteme pole kraju a typu z DB
        $q_kraje = $db->query("SELECT id_kraj, kraj_name FROM m_list_kraj WHERE kraj_name NOT LIKE '---%' ORDER BY id_kraj"); //podminkou "not like" se vyradi kraj "----neuveden----"
        $q_typy = $db->query("SELECT id_type, type_name, default_send FROM m_ae_types ORDER BY id_type");
        $kraje = array();
        $typy = array();



        while ($_kraj = $db->FetchAssoc($q_kraje)) {
            $kraje[] = $_kraj;
        }
        while ($_typ = $db->FetchAssoc($q_typy)) {
            $typy[] = $_typ;
        }




        //ulozime nastaveni pokud je
        if (isset($_POST['sbmt']))
        {
            if (!is_array($_POST['mtcfg']))
            {
                $_POST['mtcfg'] = array();
            }
            //ukladame pouze odchylky od vychoziho nastaveni
            $query_r = 'REPLACE INTO m_ae_memtcfg (id_m, id_kraj, id_type, checked) VALUES ';
            $query_d = 'DELETE FROM m_ae_memtcfg WHERE id_m=\'' . $a_user['id_m'] . '\' AND (';
            $upd = array();
            $del = array();
            for ($ti = 0; $ti < count($typy); $ti++)
            {
                for ($ki = 0; $ki < count($kraje); $ki++)
                {
                    $checked = in_array($typy[$ti]['id_type'] . '-' . $kraje[$ki]['id_kraj'], $_POST['mtcfg']) ? '1' : '0';
                    if ($checked == $typy[$ti]['default_send'])
                    {
                        $del[] = "(id_kraj='" . $kraje[$ki]['id_kraj'] . "' AND id_type='" . $typy[$ti]['id_type'] . "')";
                    }
                    else
                    {
                        $upd[] = "('${a_user['id_m']}', '" . $kraje[$ki]['id_kraj'] . "',  '" . $typy[$ti]['id_type'] . "', '$checked')";
                    }
                }
            }
            $error = false;
            if (count($upd) > 0)
            {
                $query_r .= implode(', ', $upd);
                $db->query($query_r);
                if ($db->error)
                    $error = true;
            }
            if (count($del) > 0)
            {
                $query_d .= implode(' OR ', $del) . ')';
                $db->query($query_d);
                if ($db->error)
                    $error = true;
            }
            if ($error)
            {
                echo '<p class="error">Při ukládání došlo k chybě.</p>';
            }
            else
            {
                echo '<p><b>Vaše nastavení bylo uloženo.</b></p>';
            }
        }









        //nacteme nastaveni
        $q_cfg = $db->query("SELECT id_kraj, id_type, checked FROM m_ae_memtcfg WHERE id_m='${a_user['id_m']}'");
        $cfg = array();
        while ($_cfg = $db->FetchAssoc($q_cfg)) {
            if (!isset($cfg[$_cfg['id_type']]))
                $cfg[$_cfg['id_type']] = array();
            $cfg[$_cfg['id_type']][$_cfg['id_kraj']] = $_cfg['checked'];
        }
        //vypiseme prvni radek (seznam kraju)
        ?><tr><td colspan="2">&nbsp;</td><?php
                for ($ki = 0; $ki < count($kraje); $ki++)
                {
            ?>
            <th style="width:25px;vertical-align:bottom"><div style="height:120px;text-align:left; writing-mode: tb-rl; filter: flipv() fliph();display:none;display:block"><?php echo htmlspecialchars($kraje[$ki]['kraj_name']); ?></div>
            <embed style="width:25px;height:120px;display:block;display:none" src="vtext-svg.php?h=120&amp;t=<?php echo htmlspecialchars(urlencode($kraje[$ki]['kraj_name'])); ?>" type="image/svg+xml">
            </th>
            <?php
        }
        ?></tr><tr><td colspan="2">&nbsp;</td><?php
        for ($ki = 0; $ki < count($kraje); $ki++)
        {
            echo "<td style=\"background-color:#99f\"><input type=\"checkbox\" onclick=\"this.onchange()\" onchange=\"chgcol($ki, this.checked);\"></td>";
        }
        ?></tr><?php
        for ($ti = 0; $ti < count($typy); $ti++)
        {
            ?><tr><?php
            echo "<th style=\"text-align:right\">" . $typy[$ti]['type_name'] . "</th>";
            echo "<td style=\"background-color:#99f\"><input type=\"checkbox\" onclick=\"this.onchange()\" onchange=\"chgrow($ti, this.checked);\"></td>";
            for ($ki = 0; $ki < count($kraje); $ki++)
            {
                $checked = isset($cfg[$typy[$ti]['id_type']][$kraje[$ki]['id_kraj']]) ? $cfg[$typy[$ti]['id_type']][$kraje[$ki]['id_kraj']] : $typy[$ti]['default_send'];
                echo '<td><input type="checkbox" name="mtcfg[]" value="' . $typy[$ti]['id_type'] . '-' . $kraje[$ki]['id_kraj'] . '" ' . ($checked ? 'checked="checked"' : '') . '></td>';
            }
            ?></tr><?php
}
        ?>
    </table>

    <p style="background-color: limegreen; padding: 1em; text-align: center;">
        <input type="submit" name="sbmt" value="Uložit">
    </p>
</form>








<h3 style="margin-top: 3em;">Statistiky</h3>
<p>Počet platných členů, kteří odebírají zprávy daného typu <b>v alespoň jednom z krajů</b> a počet zpráv rozeslaných do alespoň jednoho kraje za posledních 31 dnů.</p>


<?php
draw_table_from_query($db, "prihlaseni1", "
-- máme uložen jen počet lidí, kteří si zrušili odběr daného typu zprávy
-- vybereme ty, kteří to udělali ve všech krajích
-- a následně to odečteme od celkového počtu aktivních profilů
SELECT
    type_name AS 'Obecné zprávy',
    clenu - count( c.id_m ) AS 'Odběratelů',
    ifnull(zprav, 0) as 'Zpráv za měsíc'
FROM
    mensaweb.m_ae_types t
LEFT OUTER JOIN (
    SELECT id_m, id_type, count( checked ) AS 'odebrane'
    FROM mensaweb.m_ae_memtcfg
    WHERE checked =0
    GROUP BY id_m, id_type
)c ON ( t.id_type = c.id_type )

-- joineme se sloupcem počtu všech členů
CROSS JOIN (
    SELECT COUNT( id_m ) AS 'clenu'
    FROM mensaweb.m_members
    WHERE email LIKE '%@%'
    AND prizpevky >= ( year( now( ) ) -1 )
    AND disable = 'N'
)d

-- spojit s tabulkou zpráv kvůli počtům
LEFT OUTER JOIN
(
select
    typ, count(id_mail) as zprav
    from m_ae_mails
    where
        cas >= (now()- INTERVAL 31 DAY)
        and not typ is null
    group by typ
) e ON (c.id_type = e.typ)

WHERE
    -- neber konfernece s explicitním přihlašováním
    NOT (t.id_type =7 OR t.id_type =8 OR t.id_type =9)
    -- ber jen ty, kde jsou zrušené všechny kraje
    AND odebrane >=15
GROUP BY
    type_name
ORDER BY
    t.id_type");



//echo "<p>&nbsp;</p>";
echo "<br>";
draw_table_from_query($db, "prihlaseni2", "
-- Jak určit, zda je alespoň něco zaškrtnuto
-- pro konference, kde se lidé explicitně přihlašují?
-- v tabulce m_ae_memtcfg je zaškrtnutý příjem pro každého člena a každý kraj
-- provedeme vnější select, který redukuje počty na 1 záznam pro typ konference,
-- zobrazíme.
SELECT
    type_name as 'Volitelné zprávy',
    count( id_m ) as 'Odběratelů',
    ifnull(zprav, 0) as 'Zpráv za měsíc'

-- vyber z tabulky typu konferenci
FROM mensaweb.m_ae_types t

LEFT OUTER JOIN (
    SELECT c.id_m, c.id_type
    FROM
        mensaweb.m_ae_memtcfg c
        -- join s cleny, abychom vyhazeli neplatne
        JOIN mensaweb.m_members m
        ON (m.id_m = c.id_m)
    WHERE
        checked =1
        -- pouze aktualni clenove
        AND m.prizpevky >= ( year( now( ) ) -1 )
        AND m.disable = 'N'
    GROUP BY
        c.id_m, c.id_type
) c ON ( t.id_type = c.id_type )


-- spojit s tabulkou zpráv kvůli počtům
LEFT OUTER JOIN
(
select
    typ, count(id_mail) as zprav
    from m_ae_mails
    where
        cas >= (now()- INTERVAL 31 DAY)
        and not typ is null
    group by typ
) e ON (c.id_type = e.typ)


WHERE
    -- konfernece s explicitním přihlašováním
    t.id_type =7
    OR t.id_type =8
    OR t.id_type =9
GROUP BY
    type_name
ORDER BY
    t.id_type");
?>



<h3 style="margin-top: 3em;">Informace</h3>

<p>Přehled rozeslaných obecných zpráv naleznete na stránce
<a href="/index.php?men=men20.3.0.0&filter=gen"> Rozeslat zprávu » Historie zpráv</a>.</p>

<p>Nastavení odběru konferencích naleznete na stránce <a href="index.php?men=men20.2.0.0">Rozeslat zprávu » Příjem konferencí</a>.</p>

<p>Nápovědu a popis funkčnosti naleznete v <a href="/document.php?men=men14.3.0.0&id_c=232" title="nápověda systému zpráv">zadávací dokumentaci</a>.</p>


<p style="text-align:right; margin-top:8em;"><em>Stránku naprogramoval Richard Ejem a upravoval Tomáš Kubeš, poslední úprava 3. dubna 2013.</em></p>

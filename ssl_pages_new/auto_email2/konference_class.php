<?php

/**
 * @package mensa.cz
 * <AUTHOR> <PERSON><PERSON><PERSON><PERSON>, <EMAIL>
 * @version 1.0
 * @encoding utf-8
 *
 *  třída statistik přihlašování konferenci
 *  originální jméno souboru konference_class.php
 *
 *  Používá tabulku
 * CREATE TABLE IF NOT EXISTS `m_ae_stat` (
  `cas_zmeny` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Cas zaznamu',
  `id_konf` int(10) unsigned NOT NULL COMMENT 'ID Menena konference',
  `zmena` tinyint(1) NOT NULL DEFAULT '0' COMMENT '0 - snazam odber, 1 - prihlasen odber',
  PRIMARY KEY (`cas_zmeny`, `id_konf`)
  ) ENGINE=MyISAM  COMMENT='Statistika přihlašování odběru konferencí';
 *
 *
 * Zmenovnik
 *  2014-Apr-05, TK: Oprava celkoveho pocty clenu getTotalUsers().
 *
 *
 */

class konference {

    private $dbsource = null;                       // databázový objekt
    private $databasename = "test";                 // jméno aktuální databáze
    private $user = null;                             // objek uživatelských informací z intranetu
    private $konference = null;                       // definice konferencí
    private $prihlasene_konference = array();          // pole s přihlášenými konferencemi
    private $prihlasena_id = array();
    private $vysledek_dotazu = null;                // zdroj pro výsledek dotazu
    private $dotaz = null;                          // pole pro text dotazu
    private $pridavane = array();                     // pole přidávaných konferencí
    private $mazane = array();                        // pole mazaných konferencí
    private $delete = array();                        // zapnout mensovni konferenci nebo vypnout MS/SIG - takze odstranit radek
    private $insert = array();                        // vypnout mensovni konferenci nebo zapnout MS/SIG- takze vlozit radek
    private $zmena = array();                         // seznam změn
    private $key;                                     // pomocná proměnná pro procházení cykly
    private $val;                                     // pomocná proměnná pro procházení cykly
    private $total_users;                               // celkový počet uživatelů
    private $total_konf = array();                        // celkový počet uživatelů v jednotlivých konferencích
    private $pocet_prispevku_konf_celkem = array();           // celkový počet příspěvků v konferenci
    private $vystup = null;                            // pomocná proměnná pro výstupní hodnotu z funkce
    private $interval_statistiky=null;

    /*
     * Konstruktor
     * @param $db - databázový objekt
     * @param $database - jméno aktuální databáze - default je test
     * @param $user - uživatelské informace z intranetu
     * @param konf - definice konferencí
     */

    function __construct($db, $database, $user_in, $konf) {
        /*
         * Nasetují se zdroje dat a užýivatel
         */
        $this->dbsource = $db;
        $this->databasename = $database;
        $this->user = $user_in;
        $this->konference = $konf;
        /*
         * Načtou aktuálně vybrané konference
         */
        $this->dotaz = "SELECT id_konf, banned
                FROM {$this->databasename}.m_ae_konfcfg
                WHERE id_m='{$this->user['id_m']}'";
        $this->vysledek_dotazu = $this->dbsource->Query($this->dotaz);
        while ($this->prihlasene_konference[] = $this->dbsource->FetchAssoc($this->vysledek_dotazu));
        foreach ($this->prihlasene_konference as $this->zmena) {
            $this->prihlasena_id[] = @$this->zmena['id_konf'];
        }

        // Úklid
        $this->zmena = array();
        $this->dotaz = "";
        $db->setFreeResult($this->vysledek_dotazu);
    }

    /*
     * Funkce vrací celkový počet uživatelů konferencí
     */

    public function getTotalUsers() {
        $this->vysledek_dotazu = $this->dbsource->query("
            SELECT
             COUNT(m.id_m)
            FROM
                {$this->databasename}.m_members m
                -- toto tady nechceme, v podstate nas zajima jen pocet aktivnich profilu
                -- LEFT OUTER JOIN
                -- {$this->databasename}.m_ae_konfcfg k   ON (k.id_m=m.id_m)
            WHERE
                email LIKE '%@%'
                AND prizpevky >= (year(now())-1)
                AND disable = 'N'

            ");
        list($this->total_users) = $this->dbsource->FetchRow($this->vysledek_dotazu);
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        return $this->total_users;
    }

    /*
     * Funkce vrací počet uživatelů v jednotlivých konferencích
     * jako array(id_konf, count)
     * ziskani poctu clenu v konferenci
     * je treba sloucit s tabulkou clenu, aby bylo mozne vyradit
     * neaktivni profily
     */

    public function getPocetUserConf() {
        $this->vysledek_dotazu = $this->dbsource->query("
            SELECT
             id_konf,
                COUNT(k.id_m) AS count
            FROM
            {$this->databasename}.m_ae_konfcfg k LEFT OUTER JOIN
            {$this->databasename}.m_members m ON (k.id_m=m.id_m)
        WHERE
            m.email LIKE '%@%'
            AND m.prizpevky >= (year(now())-1)
            AND m.disable = 'N'
        GROUP BY
            id_konf
        ");

        while ($k = $this->dbsource->FetchAssoc($this->vysledek_dotazu)) {
            $this->total_konf[$k['id_konf']] = $k['count'];
        }
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        unset($k);
        return $this->total_konf;
    }

    /*
     * Funkce vrací pole s počtem emailů v  jedné konferenci
     * array(id_konf, count)
     */

    public function getPocetPrispevku() {
        $this->vysledek_dotazu = $this->dbsource->query("
            SELECT
                konference,
                count(id_mail) as pocet
            FROM
                {$this->databasename}.m_ae_mails
            GROUP BY konference
         ");

        //$konerence_pocet_celkem = array();
        while ($k = $this->dbsource->FetchAssoc($this->vysledek_dotazu)) {
            $this->pocet_prispevku_konf_celkem[$k['konference']] = $k['pocet'];
        }
        unset($k);
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        return $this->pocet_prispevku_konf_celkem;
    }

    /*
     * Funkce vrací počet emailů v jedné konferenci za časové období
     * $doba= "INTERVAL 1 MONTH"
     *          "INTERVAL 1 YEAR"
     * array(id_konf, count)
     */

    public function dejPocetMailuDoba($doba) {
        $this->vysledek_dotazu = $this->dbsource->query("
            SELECT
                konference,
                count(id_mail) as pocet
            FROM
                {$this->databasename}.m_ae_mails
            WHERE
                cas > DATE_SUB(NOW(), " . $doba . ")
            GROUP BY konference
            ");
        $this->vystup = array();
        while ($k = $this->dbsource->FetchAssoc($this->vysledek_dotazu)) {
            $this->vystup[$k['konference']] = $k['pocet'];
        }
        unset($k);    // vyčištění proměnných
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        return $this->vystup;
    }

    /*
     * Funkce vrací počet změn v jedné konferenci za časové období
     * $doba= m="INTERVAL 1 MONTH"
     *        y=  "INTERVAL 1 YEAR"
     *        w=  "INTERVAL 1 WEEK"
     *        d= "INTERVAL 24 HOUR"
     * array(id_konf, pocetplus, pocetminus)
     */

    public function dejPocetZmen($doba) {
        switch ($doba) {
            case 'y':
                $doba = "INTERVAL 1 YEAR";
                $this->interval_statistiky="poslední rok";
                break;
            case 'm':
                $doba = "INTERVAL 1 MONTH";
                $this->interval_statistiky="poslední měsíc";
                break;
            case 'w':
                $doba = "INTERVAL 1 WEEK";
                $this->interval_statistiky="poslední týden";
                break;
            case 'd':
                $doba = "INTERVAL 24 HOUR";
                $this->interval_statistiky="za 24 hod.";
                break;
        }
        $this->vysledek_dotazu = $this->dbsource->query("
            SELECT
                id_konf,
                count(zmena) as pocet
            FROM
                {$this->databasename}.m_ae_stat
            WHERE
                cas_zmeny > DATE_SUB(NOW(), " . $doba . ")
                    AND zmena=1
            GROUP BY id_konf
            ");
        $this->vystup = array();
        while ($k = $this->dbsource->FetchAssoc($this->vysledek_dotazu)) {
            $this->vystup[$k['id_konf']]['pocetplus'] = $k['pocet'];
        }
        unset($k);    // vyčištění proměnných
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        $this->vysledek_dotazu = $this->dbsource->query("
            SELECT
                id_konf,
                count(zmena) as pocet
            FROM
                {$this->databasename}.m_ae_stat
            WHERE
                cas_zmeny > DATE_SUB(NOW(), " . $doba . ")
                    AND zmena=0
            GROUP BY id_konf
            ");
        while ($k = $this->dbsource->FetchAssoc($this->vysledek_dotazu)) {
            $this->vystup[$k['id_konf']]['pocetminus'] = $k['pocet'];
        }
        unset($k);    // vyčištění proměnných
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        return $this->vystup;
    }

    /*
     * funkce vrací období, za které sjou zpracovány statistiky
     */
    public function getIntervalStatistiky(){
        return $this->interval_statistiky;
    }

    /*
     * Funkce vytvoří statistiku a zapíše změny vybraných do databáze
     */
    public function zapisZmenu() {
        if (isset($_POST['submitted'])) {
            if (empty($_POST['ckonf']))
                $_POST['ckonf'] = array();
            foreach ($this->konference as $this->idk => $this->konf) {
                // pokud je mensovní konference a není v odesílaném postu nebo je ostatní a je v odesílaném postu
                if (($this->konf['type'] == KONF_MEN) != in_array($this->idk, $_POST['ckonf'])) {
                    // pokud je mensovní, není v postu a není v starých datech
                    if ($this->konf['type'] == KONF_MEN && !in_array($this->idk, $this->prihlasena_id)) {
                        // pak se odhlašuje
                        $this->zmena[$this->idk] = 0;
                    }
                    // pokud je jiná konference a
                    else
                    if (!in_array($this->idk, $this->prihlasena_id)) {
                        //pak se přihlašuje
                        $this->zmena[$this->idk] = 1;
                    }
                    //vypnout mensovni konferenci nebo zapnout MS/SIG- takze vlozit radek
                    $this->insert[] = "(" . $this->user['id_m'] . "," . $this->idk . ")";
                } else {
                    // pokud je mensovní, je v postu a je v starých datech
                    if ($this->konf['type'] == KONF_MEN && in_array($this->idk, $this->prihlasena_id)) {
                        // pak se přihlašuje
                        $this->zmena[$this->idk] = 1;
                    }
                    // pokud je jiná konference a
                    else
                    if (in_array($this->idk, $this->prihlasena_id)) {
                        //pak se odhlašuje
                        $this->zmena[$this->idk] = 0;
                    }
                    //zapnout mensovni konferenci nebo vypnout MS/SIG - takze odstranit radek
                    $this->delete[] = $this->idk;
                }
            }
            //print_r($this->zmena);
            foreach ($this->zmena as $this->key => $this->val) {
                $this->dbsource->query($qa = "INSERT IGNORE INTO {$this->databasename}.m_ae_stat SET cas_zmeny=now(), id_konf={$this->key}, zmena={$this->val}");
            }
            if (!empty($this->insert)) {
                $this->insert_list = implode(',', $this->insert);
                $this->dbsource->query($qa = "INSERT IGNORE INTO {$this->databasename}.m_ae_konfcfg (id_m, id_konf) VALUES $this->insert_list");
            }
            if (!empty($this->delete)) {
                $this->delete_list = implode(',', $this->delete);
                $this->dbsource->query($qb = "DELETE FROM {$this->databasename}.m_ae_konfcfg WHERE id_m='{$this->user['id_m']}' AND banned = 0 AND id_konf IN($this->delete_list)");
            }
            // úklid lokálních proměnných
            $this->zmena = array();
            unset($this->insert_list, $this->delete_list);
        }
    }

    /*
     * Ověří, zda má daný člověk právo admimistrovat danou konferenci.
     *
     * @param int $konf    id konference
     * @return boolean
     */

    public function is_admin($konf) {
        return ((is_numeric($konf['admins']) && $konf['admins'] == $this->user['id_m']) || (is_array($konf['admins']) && in_array($this->user['id_m'], $konf['admins'])));
    }

    /*
     * Zablokuje uživateli vstup do konference
     *
     * @param $kick  - email uživatele intranetu
     * @param $idk - id konference
     */

    public function setBlok($kick, $idk) {
        if ($kick != "" && $o_konference->is_admin($konference[$idk])) {
            $this->vysledek_dotazu = $this->dbsource->Query("SELECT id_m FROM m_members WHERE email='" . $db->get($kick) . "'");
            if ($this->dbsource->getNumRows($this->vysledek_dotazu) == 0) {
                return'<p class="error">Uživatel s takovým e-mailem nebyl nalezen.</p>';
            } else {
                list($this->id_banned) = $this->dbsource->FetchRows($this->vysledek_dotazu);
                //$idk = $idk;
                $this->dbsource->Query("REPLACE INTO {$this->databasename}.m_ae_konfcfg(id_m, id_konf, banned) VALUES($this->id_banned, $idk, 1)");
                return '<p class="notice">Uživatel s e-mailem ' . htmlspecialchars($kick) . ' byl zablokován.</p>';
            }
            unset($this->id_banned);
            $this->dbsource->setFreeResult($this->vysledek_dotazu);
        }
    }

    public function unsetBlok($unban, $idk) {
        if ($unban != "" && $o_konference->is_admin($konference[$idk])) {
            $this->vysledek_dotazu = $this->dbsource->Query("SELECT id_m FROM m_members WHERE email='" . $this->dbsource->getEscapedString($unban) . "'");
            if ($this->dbsource->getNumRows($this->vysledek_dotazu) == 0) {
                return '<p class="error">Uživatel s takovým e-mailem nebyl nalezen.</p>';
            } else {
                list($this->id_unbanned) = $this->dbsource->FetchRow($this->vysledek_dotazu);
                //$idk = $idk;
                $this->dbsource->Query("DELETE FROM {$this->databasename}.m_ae_konfcfg WHERE id_m=$this->id_unbanned AND id_konf=$idk AND banned=1");
                return '<p class="notice">Uživatel s e-mailem ' . htmlspecialchars($unban) . ' byl odblokován.</p>';
            }
            unset($this->id_unbanned);
            $this->dbsource->setFreeResult($this->vysledek_dotazu);
        }
    }

    public function getStat($doba, $priznak) {
        error_reporting(E_ALL & ~E_NOTICE & ~E_WARNING);

        switch ($doba) {
            case 1:
                $this->interval = "INTERVAL 1 WEEK";
                break;
            case 2:
                $this->interval = "INTERVAL 1 MONTH";
                break;
            case 3:
                $this->interval = "INTERVAL 1 YEAR";
                break;
            default:
                $this->interval = "INTERVAL 24 HOUR";
                break;
        }
        switch ($priznak) {
            case 1:
                $this->prizn = " AND zmena=1";
                break;
            case 2:
                $this->prizn = " AND zmena=0";
                break;
            default:
                $this->prizn = "";
                break;
        }
        $this->vysledek_dotazu = $this->dbsource->Query("
            SELECT
                DATE_FORMAT(cas_zmeny, '%d. %m. %Y %h:%i') as cas, id_konf, zmena
                FROM {$this->databasename}.m_ae_stat
                WHERE cas_zmeny > DATE_SUB(NOW(), {$this->interval}){$this->prizn}
                    ORDER by cas_zmeny desc
         ");
        $this->vystup = '';
        while ($this->statraw = $this->dbsource->FetchAssoc($this->vysledek_dotazu)) {
            $this->vystup.='<tr><td>' . $this->statraw['cas'] . '</td><td>' . $this->konference[$this->statraw['id_konf']]['name'] . '</td><td>' . ($this->statraw['zmena'] == 1 ? 'povoleno' : 'zakázáno') . '</td></tr>';
        }




        // úklid
        unset($this->interval);
        $this->dbsource->setFreeResult($this->vysledek_dotazu);
        return $this->vystup;
    }

    public function test() {

    }

}

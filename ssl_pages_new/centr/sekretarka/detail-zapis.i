<h3>Zapisuji data</h3>
<?php

// platí: print_r($GLOBALS);
/**
 * win-1250
 *
 * detail-zapis.i
 *
 * Tento soubor zpracovava POST data na strance detail.
 * Ve sve podstate je to vyt<PERSON><PERSON> cast kodu puvodni stranky,
 * a tak zavisi na inludech a nekterych globalnich promennych
 * definovanych tamtez, tj. pri cteni pozor.
 *
 * Změnovník
 * 2015-Jan-09, TK: dopleni anulovani poplatku
 * 2014-10-05, VK: Přidání tlačítek pro tisk a email
 * 2014-01-29, TK: novy zaznam o certifikatu se ulozi jen pokud je zadana cena
 * 2012-06-19, TK: deaktivace průkazů
 * 2012-06-06, TK: Oblokuje účet v intranetu při obnovení členství.
 * 2012-03-21, TK: Upraveno odesílání debug mailu
 * 2012-02-29, TK: Založeno
 * 2025-03-12, MD: Sendgrid Mailer
 */

// soupis hlaseni pro uzivatele
// za kazdou hlasku vlozte novy klic (s retezcem textu)
// array_push($hlasky_pro_uzivatele, "text hlasky");
$hlasky_pro_uzivatele = array();

// ihned zkontroluji obdrzeni parametru
array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Obdržel jsem c_id_m = $c_id_m, n_id_m = $n_id_m.</span>");

// do teto promenne se budou nacitat (pomoci logickeho and) vysledky vsech zapisu do db.
// pokud v ni nakonci bude nula, alespon jeden zapis neprobehnul dobre.
$rs = TRUE;

// POZOR, zde uvedene hodnoty sep ouzivaji ke kontrole, zda existuje minula nebo nove platba
// nemenit !!! (do budoucna by to mely byt konstanty)
$prispevky_zaplaceno = 0;
$dm_nova_pravidla = 1; // default
$zaplaceno_do = "";

// texty dopisů
require_once("../ssl_pages_new/centr/sekretarka/detail-dopisy.i");

////////////////////////////////////////////////////////////////////////////////
// konstanty
define('LETOS', (int)Date("Y"));

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
/**
 * Tabulka:
 * id_param    bigint(20)    Ne
 * c_id_m    int(11)    Ne
 * cena    varchar(50)    Ano    NULL
 * datum_platby    date    Ano    NULL
 * datum_odeslani    date    Ano    NULL
 * poznamka    varchar(255)    Ano    NULL
 *
 * id_param je autoinkrement
 */
function uloz_certifikaty($c_id_m)
{
    global $db2, $hlasky_pro_uzivatele;

    // uloz certifikaty
    $rs = $db2->Query("SELECT * FROM mensasec.c_m_certifikaty  WHERE c_id_m = $c_id_m");
    if ($db2->getNumRows($rs) > 0) {
        array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Našel jsem v databázi údaje o " . $db2->getNumRows($rs) . " certifikátu.</span>");
        while ($platba = $db2->Fetcharray($rs)) {
            // ziskej id zaznamu (ocekava se, ze stranka byla korektne vygenerovana, takze id na strance existuje v db.)
            $id_param = $platba["id_param"];

            // snaz se z post dat vyhmatnout napln pro toto id zaznamu
            $cena = addslashes($_POST["cert_cena_" . $id_param]);
            //zde je promenna platba predefinovana, jak prasarna!
            $pocet_certifikatu = $_POST["cert_pocet_" . $id_param];
            $platba = $_POST["cert_platba_" . $id_param];
            $odeslano = $_POST["cert_odeslano_" . $id_param];
            $poznamka = addslashes($_POST["cert_pozn_" . $id_param]);

            // uprava
            if (trim($platba) != "") {
                $platba = "'" . write_date_sql($platba) . "'";
            } else {
                $platba = "NULL";
            }
            if (trim($odeslano) != "") {
                $odeslano = "'" . write_date_sql($odeslano) . "'";
            } else {
                $odeslano = "NULL";
            }

            // if value is not given, store as empty
            if (trim($pocet_certifikatu) != "") {
                // coerce to integer
                $pocet_certifikatu = (int) $pocet_certifikatu;
            } else {
                $pocet_certifikatu = "NULL";
            }

            // cena se uklada jako VARCHAR(50)
            $SQL = "UPDATE mensasec.c_m_certifikaty
                    SET cena='$cena', pocet_certifikatu=$pocet_certifikatu, datum_platby=$platba, datum_odeslani = $odeslano, poznamka = '$poznamka'
                    WHERE c_id_m = $c_id_m  AND id_param = $id_param ";
            if ($db2->Query($SQL))
            {
                array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Updatuji certifikátovou řádku $id_param.</span>");
            }
            else
            {
                array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Upravení záznamu o certifikátu $id_param selhalo!</span>");
            }
        }
    }

    // nový záznam
    $id_param = 0;
    $cena = addslashes(trim($_POST["cert_cena_" . $id_param]));
    $pocet_certifikatu = trim($_POST["cert_pocet_" . $id_param]);
    $platba = trim($_POST["cert_platba_" . $id_param]);
    $odeslano = trim($_POST["cert_odeslano_" . $id_param]);
    $poznamka = addslashes(trim($_POST["cert_pozn_" . $id_param]));


    //if (strlen($cena) > 0 || strlen($platba) > 0 || strlen($odeslano) > 0 || strlen($poznamka) > 0 ){
    //uloz novy certifikat jen pokud je uvedena cena
    if (strlen($cena) > 0) {
        if (trim($platba) != "") {
            $platba = "'" . write_date_sql($platba) . "'";
        } else {
            $platba = "NULL";
        }
        if (trim($odeslano) != "") {
            $odeslano = "'" . write_date_sql($odeslano) . "'";
        } else {
            $odeslano = "NULL";
        }
        $SQL = "INSERT INTO  mensasec.c_m_certifikaty
                    (c_id_m, cena, pocet_certifikatu, datum_platby, datum_odeslani, poznamka)
                VALUES
                    ($c_id_m , '$cena', $pocet_certifikatu , $platba, $odeslano, '$poznamka') ";
        $db2->Query($SQL);
        array_push($hlasky_pro_uzivatele, "<span>Ukládám nový certifikát.</span>");
    }
    // konec zapisu certifikatu
}

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
/**
 * Parametry
 * $email - mail, kam se ma zasilat potvrzeni o zadani platby
 *
 * Return
 * rok, do ktereho ma clen zapalceny prispevky
 *
 * Globals
 * $db - databaze mensasec
 * $old_zaplacenodo // rok posledni zname platby
 * $hlasky_pro_uzivatele // info
 *
 * Logika kodu
 *
 * 1. Zjisti ktere platby mame v db. a uloz je v poli $roky_v_db
 * Tyto platby budou smazany, pokud pri dalsim zpracovani nezjistime, ze
 * ve formulari zustaly
 *
 * 2. Projdi vsechny POST polozky s nazvem $rok_XXXX
 * Pozor používá se starý formát, kdy jsou POST proměnné přístupné přímov kódu!!!
 *
 * 3. Smaž platby za rok, který zmizel.
 */
function zapis_plateb($c_id_m, $email, $typ, $clencislo, $jmeno)
{
    global $db2, $old_zaplacenodo, $hlasky_pro_uzivatele, $cilovy_email;
    // nastaveno v globals, pouziva se na vice mistech

    $prispevky_zaplaceno = 0; //  0  zadnou platbu neevidujeme
    $old_zaplacenodo = -1; // -1 nikdy nezaplatil

    // nacti, ktere platby jiz mame v db.
    $roky_v_db = array();
    $rq = $db2->Query("SELECT rok, id_p FROM  mensasec.c_m_platby WHERE c_id_m = $c_id_m");
    if ($db2->getNumRows($rq) > 0) {
        array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Načítám z databáze " . $db2->getNumRows($rq) . " existujících plateb.</span>");
        while ($r = $db2->Fetcharray($rq)) {
            // toto pole eviduje platby na smazani
            $roky_v_db[$r["rok"]] = "NO";
            if ($r["rok"] > $old_zaplacenodo) $old_zaplacenodo = (int)$r["rok"];
        }
    }
    //array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Rok poslední již evidované platby $old_zaplacenodo.</span>");


    // pro kazdou vyplnenou polozku rok_XXXX ve formulari projdi, co v ni je
    // polozky let jsou cislovane od nuly
    $i = 0;
    while (isset($_POST["rok_" . $i])) {
        // dekoduj data
        $rok = addslashes(trim($_POST["rok_" . $i]));
        $datumplatby = trim($_POST["datumplatby_" . $i]);
        $datum_casopis = trim($_POST["datum_casopis_" . $i]);
        $hodnota1 = addslashes(trim($_POST["hodnota1_" . $i]));
        $hodnota2 = addslashes(trim($_POST["hodnota2_" . $i]));
        $hodnota3 = addslashes(trim($_POST["hodnota3_" . $i]));
        $hodnota4 = addslashes(trim($_POST["hodnota4_" . $i]));
        $pozn = addslashes(trim($_POST["pozn_" . $i]));
        if (trim($datumplatby) != "") {
            $str_datumplatby = "'" . write_date_sql($datumplatby) . "'";
        } else {
            $str_datumplatby = "NULL";
        }
        if (trim($datum_casopis) != "") {
            $str_datumcasopis = "'" . write_date_sql($datum_casopis) . "'";
        } else {
            $str_datumcasopis = "NULL";
        }
        // array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Zpracovávám platbu za rok $rok.</span>");



        // kazdou platbu se zadanym rokem zapis znovu do databaze
        if (strlen($rok) > 0) {
            if (((int)$rok < 1991) or ((int)$rok > (Date("Y") + 1))) array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Neobvyklý rok platby $rok!</span>");

            $roky_v_db[(int)$rok] = "UPD";
            $rq = $db2->Query("SELECT rok, id_p FROM  mensasec.c_m_platby WHERE c_id_m = $c_id_m  AND rok='$rok' ");

            // platba existuje a menime ji
            if ($db2->getNumRows($rq) > 0) {
                $SQL = "UPDATE mensasec.c_m_platby SET datum_prizpevky = $str_datumplatby, datum_casopis = $str_datumcasopis,
                            hodnota1 = '" . $hodnota1 . "', hodnota2 = '" . $hodnota2 . "',
                            hodnota3 = '" . $hodnota3 . "', hodnota4 = '" . $hodnota4 . "', pozn='" . $pozn . "'
                                WHERE  c_id_m = $c_id_m AND rok='" . $rok . "'";
                $db2->Query($SQL);
                //array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Upravuji platbu za rok $rok.</span>");

                // platba neexistuje a zadava se nova
            } else {
                $roky_v_db[(int)$rok] = "ADD";
                $SQL = "INSERT INTO mensasec.c_m_platby
                            (c_id_m, rok, datum_prizpevky,datum_casopis, hodnota1, hodnota2, hodnota3, hodnota4, pozn)
                        VALUES
                            ($c_id_m,  '$rok', $str_datumplatby,  $str_datumcasopis, '" . $hodnota1 . "',  '" . $hodnota2 . "',
                                 '" . $hodnota3 . "',  '" . $hodnota4 . "', '" . $pozn . "') ";
                $db2->Query($SQL);
                array_push($hlasky_pro_uzivatele, "Zapisuji novou platbu za rok $rok.");

                // posli clenovy email o zadani platby (primarne pouzij intranetovy email - ulozeny z formmulare)
                $cilovy_email = (isset($GLOBALS['email_intranet']) && $GLOBALS['email_intranet'] != '')?$GLOBALS['email_intranet']:$email;
                if (trim($cilovy_email) != "") {
                    // 2017-03-03: doplneny prefix
                    if ($typ == 1) $prefix = "";
                    else            $prefix = "dm";
                    if (!posli_email_o_zadani_platby($cilovy_email, $rok, $prefix, $clencislo, $jmeno))
                        array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Potvrzení o zadání platby se nepodařilo odeslat.</span>");
                } else
                    array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Uživatel nemá email, potvrzení o zadání platby se nepodařilo odeslat.</span>");

            }

            // uprav datum placeni prispevku NEMSIRNE DULEZITE PRO DALSI KOD
            if (((int)$hodnota1 + (int)$hodnota2 > 0) and ($prispevky_zaplaceno < (int)$rok)) $prispevky_zaplaceno = (int)$rok;

            // k této platbě nemáme rok
        } // else array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Našel jsem platbu zadanou bez roku! Přeskauji!</span>");
        $i++;
    }

    // smaz platby z databaze, ktere byly vymazany ve formulari
    foreach ($roky_v_db AS $key => $value) {
        if ($value == "NO") {
            $db2->Query("DELETE FROM mensasec.c_m_platby WHERE c_id_m = $c_id_m AND rok=$key ");
            array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Mažu platbu za rok $key.</span>");
        }
    }

    array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Dokončil jsem zpracování plateb,
        vracím: prispevky_zaplaceno = $prispevky_zaplaceno, old_zaplacenodo = $old_zaplacenodo</span>");

    return $prispevky_zaplaceno;
}

///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
// deaktivuj průkazy
/**
 * Vytáhne si z _POST, zda bylo zmáčknuto tlačítko na deaktivaci průakzu.
 * Pokud ano, zablokuje jej v db.
 */
function blokace_prukazu()
{
    // připojení k db. a hlášky o akcích, záznam o průběhu dotazů
    global $db2, $hlasky_pro_uzivatele, $rs;

    // zjisti nazvy post dat
    $klice = array_keys($_POST);

    // projdi klice a pokud tam je blokace, proved blokaci
    foreach ($klice as $klic) {
        // blokujeme
        if (strrpos($klic, "blokovat_") === 0) {
            // over delku
            if (strlen($klic) != 18) continue;

            // ziskej kod
            $qrkod = substr($klic, 9);

            // zapis do db
            if ($db2->Query("UPDATE mensasec.c_m_evidence_prukazu SET blokovana = 1 WHERE qrid = '{$qrkod}'"))
                array_push($hlasky_pro_uzivatele, "Zablokoval jsem průkaz {$qrkod}.");
            else
                array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Blokace průkazu $qrkod se nezdařila.</span>");

            // nic dalsiho uz neres
            continue;
        }

        // aktivujeme
        if (strrpos($klic, "odblokovat_") === 0) {
            // over delku
            if (strlen($klic) != 20) continue;

            // ziskej kod
            $qrkod = substr($klic, 11);

            // zapis do db
            if ($db2->Query("UPDATE mensasec.c_m_evidence_prukazu SET blokovana = 0 WHERE qrid = '{$qrkod}'"))
                array_push($hlasky_pro_uzivatele, "Aktivoval jsem průkaz {$qrkod}.");
            else
                array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Aktivace průkazu $qrkod se nezdařila.</span>");

            // nic dalsiho uz neres
            continue;
        }
    }

    return false;
}

///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
// Dekodovani dat z formulare

// vytvor zasifrovane IQ (tri hodnoty, tri ruzne testy)
$e_iq1 = $aes_crypt->encrypt($iq1);
$e_iq2 = $aes_crypt->encrypt($iq2);
$e_iq3 = $aes_crypt->encrypt($iq3);

// vytvor zasifrovany percentil
$e_per1 = $aes_crypt->encrypt($per1);
$e_per2 = $aes_crypt->encrypt($per2);
$e_per3 = $aes_crypt->encrypt($per3);

// mitch encrypt
$e_mitch_odpovedi_1 = $aes_crypt->encrypt($mitch_odpovedi_1);
$e_mitch_odpovedi_2 = $aes_crypt->encrypt($mitch_odpovedi_2);
$e_mitch_odpovedi_3 = $aes_crypt->encrypt($mitch_odpovedi_3);

$e_mitch_iq_1 = $aes_crypt->encrypt($mitch_iq_1);
$e_mitch_iq_2 = $aes_crypt->encrypt($mitch_iq_2);
$e_mitch_iq_3 = $aes_crypt->encrypt($mitch_iq_3);

// zkonvertuj datum narozeni
if (trim($datumnar) != "") {
    $str_datumnar = "'" . write_date_sql($datumnar) . "'";
    $roknar = (int)substr($str_datumnar, 1, 4);
} else {
    $str_datumnar = "NULL";
    $roknar = "";
}


// parsuj data testu
if (trim($datumtestu1) != "") {
    $str_datumtestu1 = "'" . write_date_sql($datumtestu1) . "'";
} else {
    $str_datumtestu1 = "NULL";
}
if (trim($datumtestu2) != "") {
    $str_datumtestu2 = "'" . write_date_sql($datumtestu2) . "'";
} else {
    $str_datumtestu2 = "NULL";
}
if (trim($datumtestu3) != "") {
    $str_datumtestu3 = "'" . write_date_sql($datumtestu3) . "'";
} else {
    $str_datumtestu3 = "NULL";
}

// mitch data testu
if (trim($mitch_datum_1) != "") {
    $str_mitch_datum_1 = "'" . write_date_sql($mitch_datum_1) . "'";
} else {
    $str_mitch_datum_1 = "NULL";
}
if (trim($mitch_datum_2) != "") {
    $str_mitch_datum_2 = "'" . write_date_sql($mitch_datum_2) . "'";
} else {
    $str_mitch_datum_2 = "NULL";
}
if (trim($mitch_datum_3) != "") {
    $str_mitch_datum_3 = "'" . write_date_sql($mitch_datum_3) . "'";
} else {
    $str_mitch_datum_3 = "NULL";
}

// parsuj datum zaplaceni zapisneho
if (trim($zapisne_placeno) != "") {
    $str_zapisne_placeno = "'" . write_date_sql($zapisne_placeno) . "'";
} else {
    $str_zapisne_placeno = "NULL";
}

// datum prispevku
// Notice: Undefined variable: prizpevek_placeno in /data/home/<USER>/ssl_pages_new/centr/sekretarka/detail-zapis.i on line 430
if (@trim($prizpevek_placeno) != "") {
    $str_prizpevek_placeno = "'" . write_date_sql($prizpevek_placeno) . "'";
} else {
    $str_prizpevek_placeno = "NULL";
}

// datum zruseni clenstvi
if (trim($ukonceni_clenstvi) != "") {
    $str_ukonceni_clenstvi = "'" . write_date_sql($ukonceni_clenstvi) . "'";
} else {
    $str_ukonceni_clenstvi = "NULL";
}

// clenske cislo
if (strlen(trim($clencislo)) > 0) {
    $s_clencislo = "'" . addslashes($clencislo) . "'";
} else {
    $s_clencislo = "NULL";
}

// rok ukonceni clenstvi kvuli poplatkum, 0 = neukonceno
if (trim($ukonceni_clenstvi_poplatky) != "") {
    $str_ukonceni_clenstvi_poplatky = "'" . intval($ukonceni_clenstvi_poplatky) . "'";
} else {
    $str_ukonceni_clenstvi_poplatky = "0";
}


///////////////////////////////////////////////////////
// zformátuj PSČ na na kombinaci 3 mezera 2
if (trim($psc) != "") {
    $matches = array();
    // proved trim a hledej zacatek, tri cisla, mezitim kolik chces mezer, dve cisla a konec
    // uloz, jen pokud se takovy vzor najde, jinak nech puvodni hodnotu
    if (preg_match('/^(?P<tri>\d{3})\s*(?P<dva>\d{2})$/', trim($psc), $matches) === 1)
        $psc = "{$matches['tri']} {$matches['dva']}";
    else
        array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>PSČ {$psc} má divný tvar.</span>");
}

if (trim($psc2) != "") {
    $matches = array();
    if (preg_match('/^(?P<tri>\d{3})\s*(?P<dva>\d{2})$/', trim($psc2), $matches) === 1)
        $psc2 = "{$matches['tri']} {$matches['dva']}";
    else
        array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>PSČ {$psc2} má divný tvar.</span>");
}

///////////////////////////////////////////////////////
// zformatuj telefony
if (trim($mobil) != "") {
    $matches = array();
    // proved trim vyhod 00420 nebo +420 najdi tri skupny po trech a uloze je s mezerami
    // uloz, jen pokud se takovy vzor najde, jinak nech puvodni hodnotu
    if (preg_match('/^((\+420)|(00420))?\s*(?P<jedna>\d{3})\s*(?P<dva>\d{3})\s*(?P<tri>\d{3})$/', trim($mobil), $matches) === 1)
        $mobil = "{$matches['jedna']} {$matches['dva']} {$matches['tri']}";
    else
        array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Telefonní číslo &quot;{$mobil}&quot; má divný tvar.</span>");
    //print_r($matches);
}

if (trim($telefon) != "") {
    $matches = array();
    if (preg_match('/^((\+420)|(00420))?\s*(?P<jedna>\d{3})\s*(?P<dva>\d{3})\s*(?P<tri>\d{3})$/', trim($telefon), $matches) === 1)
        $telefon = "{$matches['jedna']} {$matches['dva']} {$matches['tri']}";
    else
        array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Telefonní číslo &quot;{$telefon}&quot; má divný tvar.</span>");
}

if (trim($telefon_zam) != "") {
    $matches = array();
    if (preg_match('/^((\+420)|(00420))?\s*(?P<jedna>\d{3})\s*(?P<dva>\d{3})\s*(?P<tri>\d{3})$/', trim($telefon_zam), $matches) === 1)
        $telefon_zam = "{$matches['jedna']} {$matches['dva']} {$matches['tri']}";
    else
        array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Telefonní číslo &quot;{$telefon_zam}&quot; má divný tvar.</span>");
}

///////////////////////////////////////////////////////
// zkontroluj email
if (trim($email) != "") {
    // over, zda je email korektni
    $email = trim($email);
    if (preg_match('/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,6}$/i', $email) !== 1)
        array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Email {$email} je ve špatném formátu, je nezbytné, abyste jej opravili!</span>");
} else {
    // nema email!!
    array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Nebyl zadán žádný email!</span>");
}

///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
// Zapis hlavnich dat

// existujici clen v centralni databazi
// pokud c_id_m nebylo zadáno, má hodnotu -1
if ($c_id_m > 0) {
    $email2Update = "";
    if (isset($email2) && preg_match('/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,6}$/i', $_POST['email2']) == 1) {
        $email2Update = ", email2 = '" . addslashes($_POST['email2']) . "',email2_status= '" . addslashes($_POST['email2_status']) . "',email2_verify_time='" . addslashes($_POST['email2_verify_time']) . "',email2_verify_ip='" . addslashes($_POST['email2_verify_ip']) . "'";
    }

    // uprava zaznamu existujiciho clena cv cb.
    // zapis do CB - vytvor dotaz

    if (!isset($prizpevek)) {
        $prizpevek = "";
    }
    if (!isset($nezasilat)) {
        $nezasilat = 0;
    }

    $SQL = "UPDATE mensasec.c_m_members
        SET
            typ=$typ,
            titul = '" . addslashes($titul) . "',
            prijmeni = '" . addslashes($prijmeni) . "',
            jmeno = '" . addslashes($jmeno) . "',
            jmeno_zz = '" . addslashes(@$jmeno_zz) . "',
            titul_za_jmenem = '" . addslashes($titul_za_jmenem) . "',
            pohlavi = '" . addslashes(@$pohlavi) . "',
            clencislo = $s_clencislo,
            ulice = '" . addslashes($ulice) . "',
            byt = '" . addslashes($byt) . "',
            obec = '" . addslashes($obec) . "',
            psc = '" . addslashes($psc) . "',
            adr_pozn = '" . addslashes($adr_pozn) . "',
            ulice2 = '" . addslashes($ulice2) . "',
            byt2 = '" . addslashes($byt2) . "',
            obec2 = '" . addslashes($obec2) . "',
            psc2 = '" . addslashes($psc2) . "',
            adr_pozn2 = '" . addslashes($adr_pozn2) . "',
            telefon = '" . addslashes($telefon) . "',
            mobil = '" . addslashes($mobil) . "',
            email = '" . addslashes($email) . "',
            email_status= '" . addslashes($email_status) . "',
            email_verify_time='" . addslashes($email_verify_time) . "',
            email_verify_ip='" . addslashes($email_verify_ip) . "',
            rodne_prijmeni = '" . addslashes($rodne_prijmeni) . "',
            telefon_zam = '" . addslashes($telefon_zam) . "',
            poznamka = '" . addslashes($poznamka) . "',
            zapisne = '" . addslashes($zapisne) . "',
            prizpevek = '" . addslashes($prizpevek) . "',
            iq1 = '$e_iq1', iq2 = '$e_iq2', iq3 = '$e_iq3',
            per1 = '$e_per1', per2 = '$e_per2', per3 = '$e_per3',
            datumnar = $str_datumnar,
            datumtestu1 = $str_datumtestu1 , datumtestu2 = $str_datumtestu2, datumtestu3 =  $str_datumtestu3,
            zapisne_placeno =  $str_zapisne_placeno,
            prizpevek_placeno =  $str_prizpevek_placeno,
            ukonceni_clenstvi =  $str_ukonceni_clenstvi,
            nezasilat_casopis =  '$nezasilat',
            ukonceni_clenstvi_poplatky =  $str_ukonceni_clenstvi_poplatky
            $email2Update,
            mitch_odpovedi_1 = '{$e_mitch_odpovedi_1}', mitch_odpovedi_2 = '{$e_mitch_odpovedi_2}', mitch_odpovedi_3 = '{$e_mitch_odpovedi_3}', 
            mitch_iq_1 = '{$e_mitch_iq_1}', mitch_iq_2 = '{$e_mitch_iq_2}', mitch_iq_3 = '{$e_mitch_iq_3}',
            mitch_datum_1 = $str_mitch_datum_1 , mitch_datum_2 = $str_mitch_datum_2, mitch_datum_3 =  $str_mitch_datum_3
        WHERE c_id_m = $c_id_m";

    $rs = $rs && $db2->Query($SQL);
    array_push($hlasky_pro_uzivatele, "Upravuji záznam člena typ = $typ, c_id_m = $c_id_m v centrální databázi.");

    // pokud se otestovany rozhodl vstoupit do Mensy a bylo mu prideleno clenske cislo
    if (strlen(trim($old_clencislo)) == 0 && strlen(trim($clencislo)) > 0) {
        array_push($hlasky_pro_uzivatele, "<span>Jedná se o nového člena.</span>");
        $novy_clen = 1;
    } elseif (strlen(trim($old_clencislo)) == 0 && strlen(trim($clencislo)) == 0) {
        array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Tato osoba není členem (pouze otestovaným).</span>");
        $novy_clen = 0;

    } else {
        array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Jedná se o stávajícího člena.</span>");
        $novy_clen = 0;
    }

    ///////////////////////////////////////////////////////////////////////
    // ZRUSENI CLENSTVI - umrti
    // pokud byl pridan zaznam o ukonceni clenstvi, smaz udaje
    if (trim($ukonceni_clenstvi) != "" AND $old_ukonceni_clenstvi == "") {
        // pokud existuje intranetovy ucet, vymaz jeho obsah
        if ($id_m > 0) {
            array_push($hlasky_pro_uzivatele, "Mazu profil clena v intranetu.");
            $SQL = "UPDATE mensaweb.m_members
                    SET disable='Y', prizpevky = " . (Date("Y") - 2) . ", email='', mobil='', www='', adresa='', ulice='', psc='', mesto=''
                    WHERE id_m = $id_m";
            $rs = $rs && $db2->Query($SQL);
            array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Smazal jsem profil číslo $id_m z intranetu.</span>");
        }

        // smaz zaznam v cb.
        $rs = $rs && $db2->Query("UPDATE mensasec.c_m_members SET byt='', obec='', psc='', ulice='', byt2='', obec2='', ulice2='', psc2='', email='', poznamka = '" . addslashes($poznamka . "\n email:" . $email . "\n" . $byt . "\n" . $ulice . "\n" . $obec . "\n" . $psc . "\n" . $byt2 . "\n" . $ulice2 . "\n" . $obec2 . "\n" . $psc2) . "' WHERE c_id_m = $c_id_m  ");
        array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Mažu většinu údajů pro c_id_m = $c_id_m v centralni databázi.</span>");

        // specialni pripad - vstal z mrtvých
        // old_ukonceni_clenstvi je definovano jako hidden jako hodnota ukonceni celsntvi pri nacteni stranky
        // zde se objevi diky register globals :(
    } elseif ($old_ukonceni_clenstvi != "" AND trim($ukonceni_clenstvi) == "") {    // doslo k obnoveni clenstvi v Mense, specialni pripad
        $body = "V centrální db. došlo k obnovení zrušeného členství (vymaz z db, umrti) u osoby: id_m = $id_m.
                 Je potreba zkontrolovat, resp. vytvorit ji ucet v intranetu pokud ma uz zaplaceno na dalsi rok.";
        $mailer = new mailer\Mailer();
        $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, "<EMAIL>", "Obnoveni clenstvi, vytvor ucet v intranetu", $body);

        array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Člen c_id_m = $c_id_m vstal z mrtvých, bude mu třeba znovu vytvořit účet.</span>");
    }

    ///////////////////////////////////////////////////////////////////////
    // ZRUSENI CLENSTVI - poplatky, stadardni
    // pokud existuje zaznam o ukonceni clenstvi v cb. zablokuj ucet v intranetu
    if ((trim($str_ukonceni_clenstvi_poplatky) != "0") && ($id_m > 0)) {
        // obsah promenne se dosadi do retezce
        $SQL = "UPDATE mensaweb.m_members SET disable='Y', prizpevky=$str_ukonceni_clenstvi_poplatky, public = 'N' WHERE id_m = $id_m";
        $rs = $rs && $db2->Query($SQL);
        array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Člen c_id_m = $c_id_m ukončil členství, blokuji účet id_m = $id_m v intranetu.</span>");

        // odzrušení členství (je třeba zrušit blokaci intrnetu)
        // $str_ukonceni_clenstvi_poplatky bude nula, pokud tam je prázná hodnota (nastaveno na zacatku teto stranky)
        // $old_ukonceni_clenstvi_poplatky je definovano jako hidden jako hodnota ukonceni celsntvi poplatky pri nacteni stranky
        // zde se objevi diky register globals :(
    } elseif ((trim($str_ukonceni_clenstvi_poplatky) == "0") && ($id_m > 0) && ($old_ukonceni_clenstvi_poplatky != "0")) {
        // 2015-Jan-09, TK: dopleni anulovani poplatku
        // odblokuj intranet, jednak nastav disable na N a potom vypni blokaci pres poplatky
        // obsah promenne se dosadi do retezce
        $SQL = "UPDATE mensaweb.m_members SET disable='N', ukonceni_clenstvi_poplatky = 0 WHERE id_m = $id_m";
        $rs = $rs && $db2->Query($SQL);
        array_push($hlasky_pro_uzivatele, "<span style='color: green;'>Člen c_id_m = $c_id_m obnovil členství, odblokovávám účet id_m = $id_m v intranetu.</span>");
    }

///////////////////////////////////////////////////////////////////////////////////////////
// uplne novy clen
} else {
    // default values for undefined fields
    if (!isset($email2)) {
        $email2 = "";
    }
    if (!isset($email2_status)) {
        $email2_status = 0;
    }
    if (!isset($email2_verify_time)) {
        $email2_verify_time = "";
    }
    if (!isset($email2_verify_ip)) {
        $email2_verify_ip = "";
    }
    if (!isset($prizpevek)) {
        $prizpevek = "";
    }
    if (!isset($nezasilat)) {
        $nezasilat = "";
    }

    // novy zapis do centralni databaze
    // vsichni nove zalozeni maji nove podminky = 1
    $SQL = "INSERT INTO  mensasec.c_m_members
                (typ, prijmeni, jmeno, jmeno_zz, pohlavi, titul, clencislo, ulice, byt, obec, psc, adr_pozn,
                 ulice2, byt2,  obec2, psc2, adr_pozn2,
                 telefon, mobil, email, email_status, email_verify_time, email_verify_ip, email2, email2_status, email2_verify_time, email2_verify_ip, rodne_prijmeni, telefon_zam, zapisne, prizpevek,
                 iq1, iq2, iq3, per1, per2, per3, datumnar, datumtestu1, datumtestu2, datumtestu3,
                 zapisne_placeno, prizpevek_placeno, ukonceni_clenstvi, nezasilat_casopis, ukonceni_clenstvi_poplatky, dm_nove_podminky,
                 mitch_odpovedi_1, mitch_odpovedi_2, mitch_odpovedi_3, mitch_iq_1, mitch_iq_2, mitch_iq_3, mitch_datum_1, mitch_datum_2, mitch_datum_3)
            VALUES
                ($typ, '" . addslashes($prijmeni) . "', '" . addslashes($jmeno) . "', '" . addslashes($jmeno_zz) . "', '" . addslashes($pohlavi) . "', '" . addslashes($titul) . "', $s_clencislo ,  '" . addslashes($ulice) . "',  '" . addslashes($byt) . "',
        '" . addslashes($obec) . "', '" . addslashes($psc) . "', '" . addslashes($adr_pozn) . "', '" . addslashes($ulice2) . "',  '" . addslashes($byt2) . "','" . addslashes($obec2) . "', '" . addslashes($psc2) . "', '" . addslashes($adr_pozn2) . "',
         '" . addslashes($telefon) . "', '" . addslashes($mobil) . "', '" . addslashes($email) . "','" . addslashes($email_status) . "','" . addslashes($email_verify_time) . "','" . addslashes($email_verify_ip) . "',
            '" . addslashes($email2) . "','" . addslashes($email2_status) . "','" . addslashes($email2_verify_time) . "','" . addslashes($email2_verify_ip) . "', '" . addslashes($rodne_prijmeni) . "', '" . addslashes($telefon_zam) . "',
         '" . addslashes($zapisne) . "', '" . addslashes($prizpevek) . "',
        '$e_iq1',  '$e_iq2',  '$e_iq3', '$e_per1',  '$e_per2',  '$e_per3',
         $str_datumnar,  $str_datumtestu1,  $str_datumtestu2, $str_datumtestu3,
         $str_zapisne_placeno, $str_prizpevek_placeno, $str_ukonceni_clenstvi, '$nezasilat', $str_ukonceni_clenstvi_poplatky, 1,
         '$e_mitch_odpovedi_1',  '$e_mitch_odpovedi_2',  '$e_mitch_odpovedi_3', '$e_mitch_iq_1',  '$e_mitch_iq_2',  '$e_mitch_iq_3', $str_mitch_datum_1, $str_mitch_datum_2, $str_mitch_datum_3
         )";

    $rs = $rs && $db2->Query($SQL);
    array_push($hlasky_pro_uzivatele, "Zakládám nový záznam v centralní databázi, typ = $typ.");

    $c_id_m = $db2->getInsertId();
    $novy_clen = 1;
    $novy_otestovany = 1;
    $SEL = "UPDATE  mensasec.c_m_otestovan SET stav = 1 WHERE id=$n_id_m";
    array_push($hlasky_pro_uzivatele, "Člen označen za zpracovaného v exportu.");
    $rs = $rs && $db2->Query($SEL);
}

if (!isset($novy_otestovany)) {
    $novy_otestovany = false;
}

array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Interní příznaky: novy_clen = $novy_clen, novy_otestovany = $novy_otestovany,
        str_ukonceni_clenstvi_poplatky = $str_ukonceni_clenstvi_poplatky, typ = $typ.</span>");

/////////////////////////////////////////////////////
// nyni mame dost informaci k zapisu dalsich priznaku
// (intranetove cislo bude zapsano dodatecne)

// zjisti, zda existuje zaznam s priznaky (ulozei ne true/false)
$existuji_priznaky = ($db2->getNumRows($db2->Query("SELECT * FROM mensasec.c_m_priznaky_clenu WHERE c_id_m = '" . addslashes($c_id_m) . "'")) == 1);

$sql_pro_priznaky = $existuji_priznaky ? "UPDATE mensasec.c_m_priznaky_clenu SET c_id_m = '" . addslashes($c_id_m) . "' " : "INSERT INTO mensasec.c_m_priznaky_clenu SET c_id_m = '" . addslashes($c_id_m) . "' ";

// je nutne pridat do dotazu update c_id_m, aby bylo mozne pridavat dalsi sloupce bez nutnosti startat se o carku
// doplnit dalsi casti SQL v pripade zmemeny dat (ve formulari je ulozena puvodni hodnota)

if (!isset($nepreje_vitani_casopisem)) {
    $nepreje_vitani_casopisem = null;
}
if (trim($nepreje_vitani_casopisem) != $nepreje_vitani_casopisem_old) {
    $sql_pro_priznaky .= $nepreje_vitani_casopisem == "on" ? ", nevitat_v_casopise = '1' " : ", nevitat_v_casopise = '0' ";
}

if (!isset($nepreje_vitani_clenem)) {
    $nepreje_vitani_clenem = null;
}
if (trim($nepreje_vitani_clenem) != $nepreje_vitani_clenem_old) {
    $sql_pro_priznaky .= $nepreje_vitani_clenem == "on" ? ", nevitat_clenem = '1' " : ", nevitat_clenem = '0' ";
}

// kdyz je text, zapis datum, pokud ne, vyprazdni
if (!isset($datum_vstupu)) {
    $datum_vstupu = null;
}
if (trim($datum_vstupu) != $datum_vstupu_old) {
    $sql_pro_priznaky .= trim($datum_vstupu) != "" ? ", datum_vstupu = '" . write_date_sql($datum_vstupu) . "' " : ", datum_vstupu = NULL ";
}

if (!isset($byvale_clencislo_v_detske)) {
    $byvale_clencislo_v_detske = null;
}
if (trim($byvale_clencislo_v_detske) != $byvale_clencislo_v_detske_old) {
    $sql_pro_priznaky .= trim($byvale_clencislo_v_detske) != "" ? ", clencislo_v_dm = '" . addslashes(trim($byvale_clencislo_v_detske)) . "' " : ", clencislo_v_dm = NULL ";
}

// uloz nove zaznamy jen pokud mame neco noveho
// delka ulozeni clenskeho cisla je cca. 65 znaku, proved ulozeni jen pokud je prikaz delsi
if (strlen($sql_pro_priznaky) > 70) {
    if ($existuji_priznaky) $sql_pro_priznaky .= " WHERE c_id_m = '" . addslashes($c_id_m) . "' ";
    $vysledek_priznaky = $db2->Query($sql_pro_priznaky);
    if ($vysledek_priznaky) {
        array_push($hlasky_pro_uzivatele, "Uložení příznaků proběhlo v pořádku.");
        $existuji_priznaky = TRUE; // zaznamenej, ze jiz existuji priznaky (pro nasledujici kod)
    } else {
        array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Uložení příznaků se nezdařilo, prosím, kontaktujte správce.</span>");
        $rs = 0;
    }
} else array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Příznaky se nezměnily, nic neukládám.</span>");

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// zapis plateb - zaroven zjisteni prispevku
$prispevky_zaplaceno = zapis_plateb($c_id_m, $email, $typ, $clencislo, $jmeno . " " . $prijmeni);

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
blokace_prukazu();

////////////////////////////////////////////////////////////////////////////////
// detska mensa
// trik: pokud je dm dle starych pravidel, napis, ze ma zaplaceno az do konce sveho detstvi
// neupdatovat tuto informaci pro lidi, kteri pozadali o ukonceni clenstvi
if (($typ == 2) && ($roknar >= (LETOS - 16)) && (strlen(trim($clencislo)) > 0) && (trim($str_ukonceni_clenstvi_poplatky) == "0")) {
    // zjisti, zda to je clen podle starych pravidel
    // pokud ano, je clenem podle veku, ale nemusi platit
    $stara_pravidla_query = $db2->Fetcharray($db2->Query("SELECT count(*) as pocet FROM mensasec.c_m_members WHERE c_id_m={$c_id_m} AND dm_nove_podminky=0"));
    if ($stara_pravidla_query['pocet'] == 1) {
        // jedine pokud je clen dm dle starych pravidel a nema ukoncene clstvi,
        // hack: prudluz mu umele dobu zaplaceni
        // jinak je tato promenna naplnen pri zpracovani plateb
        $prispevky_zaplaceno = $roknar + 16;
        $dm_nova_pravidla = 0; // jen v tomto případě má stará
        array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Jedná se o platného DM dle starých pravidel.</span>");
    } else
        array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Jedná se o platného DM dle nových pravidel.</span>");

} elseif ($typ == 2) array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Evidován jako dítě.</span>");
else {
    array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Evidován jako dospělý.</span>");
}

////////////////////////////////////////////////////////////////////////////////
// prevod informace o prispevcich do intranetu - jen pro existujici cleny
if ($id_m > 0) {
    $tmp = $db2->Query("SELECT prizpevky FROM mensaweb.m_members WHERE id_m=$id_m");
    $rok = intval(@$db2->getResult($tmp, 0, "prizpevky"));

    if ($rok != $prispevky_zaplaceno) {
        $SQL = "UPDATE mensaweb.m_members SET prizpevky = $prispevky_zaplaceno WHERE id_m = $id_m";
        $db2->Query($SQL);
        array_push($hlasky_pro_uzivatele, "Upravuji informaci o zplacení příspěvků v intranetové databázi na rok $prispevky_zaplaceno pro id_m = $id_m.");
    }
}

////////////////////////////////////////////////////////////////////////////////
// zalozeni nebo reaktivace uctu v intranetu
if ($prispevky_zaplaceno >= LETOS) {
    array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Eviduji aktuální platbu;
        prispevky_zaplaceno = $prispevky_zaplaceno, old_zaplacenodo = $old_zaplacenodo.</span>");

    // ma existujici id - byl jiz ulozen
    if ($id_m > 0) {
        //array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Má záznam v intranetu číslo $id_m.</span>");
        // zapiš, pokud zaplatil po delší době
        // zapis zaznam do exportu pro sekretarku
        if ($old_zaplacenodo == -1 and $prispevky_zaplaceno != 0) {
            array_push($hlasky_pro_uzivatele, "<span style='color: orange;'>Jedná se o první platbu člena číslo $clencislo,
                avšak záznam v intranetu již existuje. To je divné, prověřte, zda vše proběhlo korektně.
                Může se stát u dětí dle starých podmínek, kterým systém počítá 'fiktivní' platby, tam to neznamená problém.</span>");

        } elseif ($old_zaplacenodo == -1 and $prispevky_zaplaceno == 0) {
            array_push($hlasky_pro_uzivatele, "<span style='color: grey;'>Člen číslo $clencislo,
                má záznam v intranetu, ale nic neplatí – mělo by se vyskytovat jen u dětí dle starých podmínek.</span>");

        } elseif ($old_zaplacenodo < ($prispevky_zaplaceno - 1)) {
            $SQL = "INSERT INTO mensasec.c_m_nove
                        (c_id_m,  novy_clen, novy_ucet,   send_mail,
                         popis,
                         heslo,  vlozeno, stav)
                    VALUES
                        ($c_id_m, $novy_clen,        0,           0,
                         'bývalý člen, zaplatil po " . ($prispevky_zaplaceno - $old_zaplacenodo) . " letech',
                            '',    Now(),    0)";
            $db2->Query($SQL);
            array_push($hlasky_pro_uzivatele, "<span>Zaplatil po " . ($prispevky_zaplaceno - $old_zaplacenodo) . " letech, zapisuji záznam do exportu c_id_m = $c_id_m.</span>");
        } elseif ($old_zaplacenodo == ($prispevky_zaplaceno - 1)) {
            $db2->Query("INSERT INTO mensasec.c_m_nove
                        (c_id_m,  novy_clen, novy_ucet,   send_mail,
                         popis,
                         heslo,  vlozeno, stav)
                    VALUES
                        ($c_id_m, $novy_clen,        0,           0,
                         'současný člen, zaplatil na další rok',
                            '',    Now(),    0)");
            array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Platí kontinuálně, zapisuji záznam do exportu c_id_m = $c_id_m.</span>");
        } elseif ($old_zaplacenodo == $prispevky_zaplaceno) {
            array_push($hlasky_pro_uzivatele, "<span style='color: gray;'>Žádná nová platba nezadána.</span>");
        } else
            array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Divná kombinace původní a nové platby, prosím, kontaktujte administrátora.</span>");


        ////////////////////////////////////////////////
        // nema id, zalozi se novy zaznam v INTRANETU
    } else {
        array_push($hlasky_pro_uzivatele, "<span>Nemá záznam v intranetu.</span>");

        // vybrat prefix
        if ($typ == 1) $prefix = "";
        else            $prefix = "dm";

        // zalozeni noveho uzivatele
        $m_heslo = vygenerovat_kod(7);
        $SQL = "INSERT INTO mensaweb.m_members
                    (prijmeni, jmeno, titul,
                     tel_d, tel_p, email, mobil,
                     ulice, psc, mesto,
                     clen_cislo, heslo, date, rok_narozeni,
                     prizpevky, clen, owner, zamestnani)
                VALUES
                    ( '" . addslashes($prijmeni) . "', '" . addslashes($jmeno) . "', '" . addslashes($titul) . "',
                      '" . addslashes($telefon) . "',  '" . addslashes($telefon_zam) . "',  '" . addslashes($email) . "',  '" . addslashes($mobil) . "',
                      '" . addslashes($ulice) . "',  '" . addslashes($psc) . "',  '" . addslashes($obec) . "',
                      '$prefix" . addslashes($clencislo) . "',  old_password('{$m_heslo}'), Curdate(),   '" . addslashes($roknar) . "',
                      '" . addslashes($prispevky_zaplaceno) . "',  $typ, " . $a_user["id_m"] . " , 5)";
        echo "\n\n<!-- {$SQL} -->\n\n";

        // vytvoř účet
        if ($db2->Query($SQL)) {
            $new_id_m = $db2->getInsertId();
            // dopln id_m do cdb
            $SQL = "UPDATE mensasec.c_m_members SET id_m = $new_id_m WHERE c_id_m = $c_id_m";
            $db2->Query($SQL);
            array_push($hlasky_pro_uzivatele, "<span>Založil jsem účet v intranetu číslo id_m=$new_id_m.</span>");

            //
            $c_heslo = addslashes($aes_crypt->encrypt($m_heslo));
            if (strlen($email) == 0) {
                $send_mail = 0;
            } else {
                $send_mail = 1;
            }

            if ($typ == 1) {
                $role_id = 1;
            } else {
                $role_id = 4;
            }

            $SQL = "INSERT INTO mensasec.c_m_nove (c_id_m, novy_clen,novy_ucet,  send_mail,  popis, heslo,  vlozeno, stav)
                    VALUES ($c_id_m, $novy_clen, 1, $send_mail, 'nový účet v intranetu','$c_heslo', Now(), 0)";
            $db2->Query($SQL);
            array_push($hlasky_pro_uzivatele, "<span>Zapsal jsem člena do exportu 'nový účet v intranetu'.</span>");


            $SQL = "INSERT INTO mensaweb.m_acc_uid2role  (id_m, role_id, created, created_by) VALUES ($new_id_m, $role_id , Now(), " . $a_user["id_m"] . ")";
            $db2->Query($SQL);
            array_push($hlasky_pro_uzivatele, "<span>Zapsal jsem člena do tabulky přístupových práv.</span>");


            // pošli mail uživateli
            if (strlen($email) > 0) {
                // TK: preifx cz není nutný a je zavádějící pro zájemce o přihlášení na web MI
                if ($typ == 1) $prefix = "";
                else            $prefix = "dm";
                if ($novy_clen == 1) posli_email_novemu_clenovi($email, $prefix, $clencislo, $m_heslo, "{$jmeno} {$prijmeni}");
                else                 posli_email_novy_ucet($email, $prefix, $clencislo, $m_heslo);
            } else array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Uživatel nemá email, nemohl jsem poslat uvítací zprávu (ale chtěl jsem)!</span>");
        } else {
            // vytvoření účtu selhalo
            echo $db2->error;
            array_push($hlasky_pro_uzivatele, "<span style='color: red;'>Nemohl jsem vytvořit účet v intranetu!</span>");
        }
    }

} //if ($prispevky_zaplaceno >= LETOS){ // nezaplacene prispevky, ale novy otestovany
elseif ($novy_clen == 1) {
    $SQL = "INSERT INTO mensasec.c_m_nove
                (c_id_m, novy_clen, novy_ucet,  send_mail,  popis, heslo,  vlozeno, stav)
            VALUES
                ($c_id_m, 2, 0,0, 'nový otestovaný','', Now(), 0)";
    $db2->Query($SQL);
    array_push($hlasky_pro_uzivatele, "<span>Nový otestovaný, který zatím nezaplatil, píšu do exportu 'nový otestovaný'.</span>");
}

uloz_certifikaty($c_id_m);

//////////////////////////////////////////////////////
// uloz odkaz na intranetovy profil take do priznaku
if ($existuji_priznaky) {
    // zjisti, zda ma tento clen ulozene informace
    $id_result = $db2->Query("SELECT id_m FROM mensasec.c_m_members WHERE c_id_m = $c_id_m AND NOT id_m IS NULL");
    if ($db2->getNumRows($id_result) == 1) {
        $intranet_id_m = $db2->Fetcharray($id_result);
        $db2->Query("UPDATE mensasec.c_m_priznaky_clenu SET intranet_id_m='" . $intranet_id_m["id_m"] . "' WHERE c_id_m = $c_id_m");
    }
}

/////////////////////////////////////////////
// zprava pro uzivatele
echo "<ul>";
foreach ($hlasky_pro_uzivatele as $hlaska) echo "<li>$hlaska</li>\n";
echo "</ul>";

// vsechny zapisy prohely vporadku
if ($rs) {
    // @TODO: tady se musí objevit tlačítka na odeslání dopisu
    echo "<p>Všechny zápisy do databáze proběhly v pořádku.</p>";
} else {
    echo "<p>Nastal problém při zápisu do databáze.</p>";
}


// tady se vypisují tlačítka pro tisk výsledků testů a seznam již odeslaných výsledků
// natáhne třídu pro zpracování testování
require_once("../ssl_pages_new/centr/testujici/zpracovani_testovani_class.php");
$testovani = new zpracovani_testovani($db2, $mensasec, $a_user, $men);

//vyberou se existující výsledky testů
$data_testu = $testovani->ReadDataTestu($c_id_m);


//natáhne třídu pro logování operací s členskou databází
require_once('../ssl_library_2013/c_m_logy_members_class.php');
$logy = new c_m_logy_members($db2);
$odeslane = $logy->getClenaVysledky($c_id_m); // vybere z logu již odeslané výsledky testů
if (count($odeslane) > 0) {  // a vše zobrazí
    echo '<h3>Již zaslané výsledky testů</h3>';
    foreach ($odeslane as $key => $val) {
        echo '<p>' . $val['popis'] . ', odeslaný dne ' . $val['datumodeslani'] . ', provedl(a) ' . $val['jmeno'] . ' ' . $val['prijmeni'] . '</p>';
    }
}


/**
 * Vybere, ktere prilohy se maji k danemu mailu nabidnout.
 * @param $dospely
 * @param $clen
 * @param $radek
 * @return string
 */
function getAttDoc($dospely, $clen, $radek)
{
    // print_r(array($dospely,$clen, $radek));

    $doc = "";
    if ($dospely && $clen) {
        if (is_dir("../files/testy_email/dospely_ano/")) {
            $dir = "../files/testy_email/dospely_ano/";
        }
    }
    if ($dospely && !$clen) {
        if (is_dir("../files/testy_email/dospely_ne/")) {
            $dir = "../files/testy_email/dospely_ne/";
        }
    }
    if (!$dospely && $clen) {
        if (is_dir("../files/testy_email/dite_ano/")) {
            $dir = "../files/testy_email/dite_ano/";
        }
    }
    if (!$dospely && !$clen) {
        if (is_dir("../files/testy_email/dite_ne/")) {
            $dir = "../files/testy_email/dite_ne/";
        }
    }
    if ($odir = opendir($dir)) {
        $doc = "<p><b>Přílohy:</b> výsledek testu IQ {$radek}<br/>";
        while (($filename = readdir($odir)) !== false) {
            if ($filename != "." && $filename != "..") {
                $doc .= '<input type="checkbox" name="att' . $radek . '[]" value="' . $dir . $filename . '" checked="checked">' . $filename . '<br/>';
            }
        }
        closedir($odir);
        $doc .= '</p>';
    }
    return $doc;
}

/**
 * Rozhodne, zda dotycny ma nebo nema narok na clenstvi
 * @param array $raven_iqs
 * @param array $raven_percentiles
 * @param array $mitch_iqs
 * @return bool
 */
function mensa_ano_ne($raven_iqs, $raven_percentiles, $mitch_iqs)
{
    return mensa_ano_ne_raven($raven_iqs, $raven_percentiles) || mensa_ano_ne_mitch($mitch_iqs);
}

/**
 * Rozhodne, zda dotycny ma nebo nema narok na clenstvi (Raven)
 * @param array $raven_iqs
 * @param array $raven_percentiles
 * @return bool
 */
function mensa_ano_ne_raven($raven_iqs, $raven_percentiles)
{
    foreach ($raven_iqs as $raven_iq) {
        if (intval($raven_iq) >= 130) {
            return true;
        }
    }

    foreach ($raven_percentiles as $raven_percentile) {
        if (in_array($raven_percentile, array('99', '100', '99-100', '99 - 100'))) {
            return true;
        }
    }

    return false;
}

/**
 * Rozhodne, zda dotycny ma nebo nema narok na clenstvi (Mitch)
 * @param array $mitch_iqs
 * @return bool
 */
function mensa_ano_ne_mitch($mitch_iqs)
{
    foreach ($mitch_iqs as $mitch_iq) {
        if (preg_match("/^více (jak)|(než) (1[3-9]\d)$/", $mitch_iq) || intval($mitch_iq) >= 130) {
            return true;
        }
    }

    return false;
}

/**
 * Rozhodne dle typu clena, do jake slozky s prilohami pro e-mail se bude koukat.
 * Toto vrati jako parametry pro funkci pripojujici prilohy.
 *
 * Rozhoduje se podle toho, zda prosel (alespon 1 test) nebo neprosel.
 *
 * A podle veku pri testovani.
 *
 * @param $typ z tabulky c_m_members 1 - dospely, 2 dites
 * @param $mensa TRUE - ma narok na clenstvi, false nema narok na clenstvi
 * @return array $dospely (0, 1), prosel(0, 1)
 */
function attachement_config($typ, $mensa)
{
    // print_r(array($typ, $mensa));
    if ($typ == 1) return array(TRUE, $mensa);
    if ($typ == 2) return array(false, $mensa);
}

/**
 * Rozhodne, na ktery e-mail se ma posilat vysledek.
 * @param string $str_datumnar
 * @param string $email
 * @param int $email_status
 * @param string $email_zz
 * @param int $email_zz_status
 * @return bool
 */
function select_email($str_datumnar, $email, $email_status, $email_zz, $email_zz_status)
{
    // ve formatu '2000-01-01 00:00:00'
    $nar = date_create_from_format("'Y-m-d H:i:s'", $str_datumnar);

    $now = new DateTime();
    $vek = $now->diff($nar)->y;

    if ($vek < MAX_VEK_DOSPELI_EMAIL) {
        // posli ZZ
        return ($email_zz_status == 1 && strlen($email_zz) > 0) ? $email_zz : false;
    } else {
        // posli primo otestovanemu
        return ($email_status == 1 && strlen($email) > 0) ? $email : false;
    }
}

// formular pro zobrazeni knofliku na tisk/email vysledku
echo '<form action="./index.php?men=' . $men . '" name="prn_action" method="post" target="blank">
    <input type="hidden" name="c_id_m" value="' . $c_id_m . '">';

// zkontroluj, zda je e-mail overeny a vyber na jaky e-mail posilat
if (!isset($email2)) {
    $email2 = "";
}
if (!isset($email2_status)) {
    $email2_status = 0;
}

$target_email = select_email($str_datumnar, $email, $email_status, $email2, $email2_status);
echo "<input type='hidden' name='target_email' value='{$target_email}'><p>Cílový e-mail: $target_email</p>";

$je_clen = mensa_ano_ne(
    array($iq1, $iq2, $iq3),
    array($per1, $per2, $per3),
    array($mitch_iq_1, $mitch_iq_2, $mitch_iq_3)
);

if ($data_testu['datum_testu1']) {
    echo '<p>Raven test ze dne ' . $data_testu['datum_testu1'] . ' <button name="akce" value="TiskRaven1">Tisk</button>' . ' <button name="akce" value="PDFRaven1">Tisk PDF</button> ' . ($target_email ? '<button name="akce" value="EmailRaven1">E-mail</button>' : '') . '</p>';

    if ($target_email) {
        $attParametry = attachement_config($typ, $je_clen);
        echo getAttDoc($attParametry[0], $attParametry[1], 1);
    }
}

if ($data_testu['datum_testu2']) {
    echo '<p>Raven test ze dne ' . $data_testu['datum_testu2'] . ' <button name="akce" value="TiskRaven2">Tisk</button>' . ' <button name="akce" value="PDFRaven2">Tisk PDF</button> ' . ($target_email ? '<button name="akce" value="EmailRaven2">E-mail</button>' : '') . '</p>';

    if ($target_email) {
        $attParametry = attachement_config($typ, $je_clen);
        echo getAttDoc($attParametry[0], $attParametry[1], 2);
    }
}

if ($data_testu['datum_testu3']) {
    echo '<p>Raven test ze dne ' . $data_testu['datum_testu3'] . ' <button name="akce" value="TiskRaven3">Tisk</button>' . ' <button name="akce" value="PDFRaven3">Tisk PDF</button> ' . ($target_email ? '<button name="akce" value="EmailRaven3">E-mail</button>' : '') . '</p>';

    if ($target_email) {
        $attParametry = attachement_config($typ, $je_clen);
        echo getAttDoc($attParametry[0], $attParametry[1], 2);
    }
}

if ($data_testu['mitch_datum_1']) {
    echo '<p>Mitch test ze dne ' . $data_testu['mitch_datum_1'] . ' <button name="akce" value="TiskMitch1">Tisk</button>' . ' <button name="akce" value="PDFMitch1">Tisk PDF</button> ' . ($target_email ? '<button name="akce" value="EmailMitch1">E-mail</button>' : '') . '</p>';

    if ($target_email) {
        $attParametry = attachement_config($typ, $je_clen);
        echo getAttDoc($attParametry[0], $attParametry[1], 1);
    }
}

if ($data_testu['mitch_datum_2']) {
    echo '<p>Mitch test ze dne ' . $data_testu['mitch_datum_2'] . ' <button name="akce" value="TiskMitch2">Tisk</button>' . ' <button name="akce" value="PDFMitch2">Tisk PDF</button> ' . ($target_email ? '<button name="akce" value="EmailMitch2">E-mail</button>' : '') . '</p>';

    if ($target_email) {
        $attParametry = attachement_config($typ, $je_clen);
        echo getAttDoc($attParametry[0], $attParametry[1], 2);
    }
}

if ($data_testu['mitch_datum_3']) {
    echo '<p>Mitch test ze dne ' . $data_testu['mitch_datum_3'] . ' <button name="akce" value="TiskMitch3">Tisk</button>' . ' <button name="akce" value="PDFMitch3">Tisk PDF</button> ' . ($target_email ? '<button name="akce" value="EmailMitch3">E-mail</button>' : '') . '</p>';

    if ($target_email) {
        $attParametry = attachement_config($typ, $je_clen);
        echo getAttDoc($attParametry[0], $attParametry[1], 2);
    }
}

echo '</form>';

////////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
// debug - posli email o transkaci do logu
$header = "From: detail-zapis.i <<EMAIL>>
Return-Path: <EMAIL>
Content-Type:text/plain;charset=\"utf-8\"";
$body = "Datum: " . date("Y-m-d H:i.s") . "\nCíl: c_id_m=$c_id_m, clencislo=$clencislo\nZapsal: {$a_user['jmeno']} {$a_user['prijmeni']} (id_m={$a_user['id_m']})\n\nZáznam operací:\n\n";

foreach ($hlasky_pro_uzivatele as $hlaska) $body .= strtr($hlaska, array("<span style='color: gray;'>" => "\t", "</span>" => "", "<span>" => "")) . "\n";
// neposílat standardně, bylo by to zbytečné roznášení dat
//$body .= "\n\ndebug_cdb_novy_zaznam = $debug_cdb_novy_zaznam\n\ndebug_intranet_novy_zaznam = $debug_intranet_novy_zaznam";

$mailer = new mailer\Mailer();
echo $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, '<EMAIL>', mime_header_encode("Zápis do cdb. pro c_id_m=$c_id_m", "utf-8"), $body, $header) ?
    "<p>Odeslal jsem záznam o transakci.</p>" : "<p>Odeslání záznau o transakci se nezdařilo.</p>";

///////////////////////////////////////////////////////////////
// konec
// vrati $c_id_m
return $c_id_m;

<?php

use Mpdf\Mpdf;
use Mpdf\Utils\UtfString;

// vyhledavani v matici
function searchMatrix($id, $array, $klic)
{
    foreach ($array as $key => $val) {
        if ($val[$klic] === $id) {
            return $key;
        }
    }
    return null;
}


if (isset($vypis)) {
    if ($vypis == "export") {
        require_once("../ssl_library_new/database2.class.l");
        require_once("../ssl_library_new/database.class.l");
        require_once('../ssl_pages_new/centr/sekretarka/certifikat_tisk_form.php');

        $db = new database;
        $db->open();

        $db2 = new database2;
        $db2->open();

        $rc = \crypto\CryptoFactory::getAES();

        $query = "
                    SELECT
                        c.c_id_m                as 'id',
                        c.pocet_certifikatu     as 'pocet_certifikatu',
                        m.titul                 as 'titul',
                        m.jmeno                 as 'jmeno',
                        m.prijmeni              as 'prijmeni',
                        m.titul_za_jmenem       as 'titul_za_jmenem',
                        DATE_FORMAT(m.datumnar, '%e. %c. %Y') as 'datum_narozeni',
                        DATE_FORMAT(NOW(), '%e. %c. %Y') as 'datum_tisku',
                        m.byt                   as 'byt',
                        m.ulice                 as 'ulice',
                        m.psc                   as 'PSC',
                        m.obec                  as 'mesto'
                    FROM 
                        mensasec.c_m_certifikaty c
                    LEFT JOIN
                        mensasec.c_m_members m ON (m.c_id_m = c.c_id_m)
                    ";
        switch ($men) {
            case "men19.2.18.0":
                $query .= "
                    WHERE
                        (c.datum_odeslani IS NULL or c.datum_odeslani = '')
                        AND (c.datum_platby > '2013-01-01' OR c.datum_platby IS NULL)
                        AND c.id_param > 6100
                        AND NOT (c.cena IS NULL OR c.cena = '' OR c.cena = 0)
                    ORDER BY
                        c.id_param ASC
                    ";
                break;
            case "men19.2.2.0":
                $query .= "
                    WHERE
                        c.c_id_m = {$c_id_m}
                    ORDER BY
                        c.id_param ASC
                    LIMIT
                        1
                    ";
                break;
            case "men19.1.0.0":
                $query .= "
                    WHERE
                    
                    ";
                break;

        }
        $tmp = $db2->Query($query);
        while ($row = $db2->FetchArray($tmp)) {
            $tisk[] = $row;
        }
        if (isset($_POST["format"])) {
            $papir = $_POST["format"];
        } else {
            $papir = "A4";
        }

        $mpdf = new Mpdf();
        $mpdf->useAdobeCJK = true;

        if (isset($_POST["volba_1"]) && ($_POST["volba_1"] == "certifikat" || $_POST["volba_1"] == "certifikat_PDF")) {
            $query = "
            SELECT
                c.c_id_m as 'id',
                m.iq1,
                m.iq2,
                m.iq3,
                m.per1,
                m.per2,
                m.per3,
                m.datumtestu1,
                m.datumtestu2,
                m.datumtestu3,
                m.mitch_iq_1,
                m.mitch_iq_2,
                m.mitch_iq_3,
                m.mitch_datum_1 as datumtestu4,
                m.mitch_datum_2 as datumtestu5,
                m.mitch_datum_3 as datumtestu6
            FROM 
                mensasec.c_m_certifikaty c
            LEFT JOIN
                mensasec.c_m_members m ON (m.c_id_m = c.c_id_m)
            WHERE
            ";
            switch ($men) {
                case "men19.2.18.0":
                    $query .= "
                        (c.datum_odeslani IS NULL or c.datum_odeslani = '')
                        AND (c.datum_platby > '2013-01-01' OR c.datum_platby IS NULL)
                        AND c.id_param > 6100
                        AND NOT (c.cena IS NULL OR c.cena = '' OR c.cena = 0)
                    ORDER BY
                        c.id_param ASC
                     ";
                    break;
                case "men19.2.2.0":
                    $query .= "
                        c.c_id_m = {$c_id_m}
                    ORDER BY
                        c.id_param ASC
                    LIMIT
                        1
                    ";
                    break;
            }
            $tmp = $db2->Query($query);
            while ($row = $db2->FetchArray($tmp)) {
                $iq[] = $row;
            }
            $query = "
                    SELECT
                        *
                    FROM
                        mensaweb.m_prevod_iq
                ";
            $tmp = $db2->Query($query);
            while ($row = $db2->FetchArray($tmp)) {
                $prevod[] = $row;
            }
            $j = 0;
            foreach ($iq as $r) {
                // DESIFROVANI
                $iq[$j]["iq1"] = str_replace(" ", "", $rc->decrypt($r["iq1"]));
                $iq[$j]["iq2"] = str_replace(" ", "", $rc->decrypt($r["iq2"]));
                $iq[$j]["iq3"] = str_replace(" ", "", $rc->decrypt($r["iq3"]));
                $iq[$j]["per1"] = str_replace(" ", "", $rc->decrypt($r["per1"]));
                $iq[$j]["per2"] = str_replace(" ", "", $rc->decrypt($r["per2"]));
                $iq[$j]["per3"] = str_replace(" ", "", $rc->decrypt($r["per3"]));
                $iq[$j]["mitch_iq_1"] = str_replace(" ", "", $rc->decrypt($r["mitch_iq_1"]));
                $iq[$j]["mitch_iq_2"] = str_replace(" ", "", $rc->decrypt($r["mitch_iq_2"]));
                $iq[$j]["mitch_iq_3"] = str_replace(" ", "", $rc->decrypt($r["mitch_iq_3"]));

                // hledani maximalniho IQ
                if (empty($iq[$j]["per1"]) && empty($iq[$j]["per2"]) && empty($iq[$j]["per3"])) {
                    // pokud jsou prazdna vsechna policka percentilu, prozkoumej hodnoty IQ a vyber nejvyssi.
                    $iq[$j]["max_iq"] = array($iq[$j]["iq1"], $iq[$j]["iq2"], $iq[$j]["iq3"], $iq[$j]["mitch_iq_1"], $iq[$j]["mitch_iq_2"], $iq[$j]["mitch_iq_3"]);
                    $iq[$j]["max"] = max($iq[$j]["max_iq"]);
                    $iq[$j]["max_iq_pozice"] = array_keys($iq[$j]["max_iq"], $iq[$j]["max"]);

                    // ziskej datum a zkonvertuj na tiskove hodnoty
                    $tisk[$j]["datum_testu"] .= date("j. n. Y", strtotime($iq[$j]["datumtestu" . (($iq[$j]["max_iq_pozice"][0]) + 1)]));
                    $tisk[$j]["IQ1"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "IQ_otestovany")]["IQ_CZ"]);
                    $tisk[$j]["IQ2"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "IQ_otestovany")]["IQ_C"]);
                    $tisk[$j]["IQ3"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "IQ_otestovany")]["IQ_CZ"]);
                    $tisk[$j]["percentil"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "IQ_otestovany")]["percentil"]);

                } elseif (!empty($iq[$j]["per1"]) || !empty($iq[$j]["per2"]) || !empty($iq[$j]["per3"])) {
                    // pokud je alespon jeden percentil neprazdny, prozkoumej percenitl
                    $iq[$j]["max_per"] = array($iq[$j]["per1"], $iq[$j]["per2"], $iq[$j]["per3"]);
                    $iq[$j]["max"] = max($iq[$j]["max_per"]);
                    $iq[$j]["max_per_pozice"] = array_keys($iq[$j]["max_per"], $iq[$j]["max"]);

                    // ziskej datum a zkonvertuj na tiskove hodnoty
                    $tisk[$j]["datum_testu"] .= date("j. n. Y", strtotime($iq[$j]["datumtestu" . (($iq[$j]["max_per_pozice"][0]) + 1)]));
                    $tisk[$j]["IQ1"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "percentil_otestovany")]["IQ_CZ"]);
                    $tisk[$j]["IQ2"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "percentil_otestovany")]["IQ_C"]);
                    $tisk[$j]["IQ3"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "percentil_otestovany")]["IQ_CZ"]);
                    $tisk[$j]["percentil"] .= ($prevod[searchMatrix($iq[$j]["max"], $prevod, "percentil_otestovany")]["percentil"]);
                } else {
                    die("<span style=\"color: #F00;\">CHYBA V PREVODU VYSLEDKU 1! Nepodařilo se vůbec získat vstupní hodnotu.</span>");
                }
                if ($tisk[$j]["IQ1"] == "" || $tisk[$j]["IQ2"] == "" || $tisk[$j]["IQ3"] == "" || $tisk[$j]["percentil"] == "") {
                    die("<span style=\"color: #F00;\">CHYBA V PREVODU VYSLEDKU 2! Získaná hodnota IQ je prázdná.</span>");
                }
                $j++;
            }

            // VYTISK JEDNOHO KUSU
            switch ($men) {
                case "men19.2.2.0":
                    $tisk[0]["pocet_certifikatu"] = 1;
                    break;
            }
            if (isset($_POST["volba_2"]) && ($_POST["volba_2"] == "1")) {
                $mpdf = certifikat($mpdf, $tisk);
            } elseif (isset($_POST["volba_2"]) && ($_POST["volba_2"] == "2")) {
                $mpdf = new Mpdf(['format' => [176, 250]]);
                $mpdf->useAdobeCJK = true;
                $mpdf = certifikat_new($mpdf, $tisk);
            }
        } elseif (isset($_POST["volba_1"]) && ($_POST["volba_1"] == "adresy")) {
            $hodnoty = array((int)$_POST["zacatek_x"], (int)$_POST["zacatek_y"], $_POST["rozmer_x"], $_POST["rozmer_y"], $_POST["mezera_x"], $_POST["mezera_y"]);
            $mpdf = etiketa($mpdf, $papir, $tisk, $hodnoty, $men);
        }
       $mpdf->SetAuthor(UtfString::strcode2utf('Mensa &#268;esko'));
       $mpdf->SetCreator(UtfString::strcode2utf('Mensa &#268;esko'));
    }
}
    function pocet_lidi($men)
    {
        switch ($men) {
            case "men19.2.18.0":
            case "men19.1.0.0":
                $vice = 1;
                break;
            default:
                $vice = 0;
                break;
        }
        return $vice;
    }
    function certifikaty ($men, $c_id_m, $n_id_m)
    {
        ?>
        <div style="float: left;">
            <form name="form_tisk_ceritifkatu" method="post" action="export.php" class="form-container">
                <input type="hidden" class="volba_1" name="volba_1" value="certifikat">
                <input type="hidden" id="volba_2" class="volba_2" name="volba_2" value="2">
                <input type="hidden" value="<?php echo $men ?>" name="men">
                <input type="hidden" value="<?php echo $c_id_m ?>" name="c_id_m">
                <input type="hidden" value="<?php echo $n_id_m ?>" name="n_id_m">
                <button type="submit" name="Tisk" value="Tisk">Tisk certifikát<?php echo((pocet_lidi($men) == 1) ? "u" : "ů"); ?></button>
            </form>
        </div>
        <div style="float: left; width: 1%;">
            &nbsp;
        </div>
        <?php
    }

function certifikatyPDF ($men, $c_id_m, $n_id_m)
{
    ?>
    <div style="float: left;">
        <form name="form_tisk_ceritifkatu" method="post" action="export.php" class="form-container">
            <input type="hidden" class="volba_1" name="volba_1" value="certifikat_PDF">
            <input type="hidden" id="volba_2" class="volba_2" name="volba_2" value="2">
            <input type="hidden" value="<?php echo $men ?>" name="men">
            <input type="hidden" value="<?php echo $c_id_m ?>" name="c_id_m">
            <input type="hidden" value="<?php echo $n_id_m ?>" name="n_id_m">
            <button type="submit" name="Tisk" value="Tisk">Tisk certifikát<?php echo((pocet_lidi($men) == 1) ? 'u' : 'ů'); ?> v PDF</button>
        </form>
    </div>
    <div style="float: left; width: 1%;">
        &nbsp;
    </div>
    <?php
}

    function certifikaty_manual ()
    {
        require_once('../ssl_pages_new/centr/sekretarka/certifikat_tisk_form.php');
        
        if (isset($_POST["volba_2"]) && ($_POST["volba_2"] == "manual")) {
            $data = array();
            $data["titul"] = htmlspecialchars($_POST['titul'] ?? '');
            $data["jmeno"]  = htmlspecialchars($_POST['jmeno'] ?? '');
            $data["prijmeni"] = htmlspecialchars($_POST['prijmeni'] ?? '');
            $data['titul_za_jmenem'] = htmlspecialchars($_POST['titul_za_jmenem'] ?? '');
            $data["datum_narozeni"] = (new DateTimeImmutable($_POST['datum_narozeni'] ?? ''))->format('d. m. Y');
            $data["datum_tisku"] = (new DateTimeImmutable())->format('d. m. Y');
            $data["datum_testu"] = (new DateTimeImmutable($_POST['datum_testu'] ?? ''))->format('d. m. Y');
            $data["IQ1"] = $_POST['IQ1'] ?? '';
            $data["IQ2"] = $_POST['IQ2'] ?? '';
            $data["IQ3"] = $_POST['IQ1'] ?? '';
            $data['percentil'] = $_POST['percentil'] ?? '';
            $tisk = array($data);

            $mpdf = new Mpdf(['format' => [176, 250]]);
            $mpdf->useAdobeCJK = true;
            $mpdf->SetAuthor(UtfString::strcode2utf('Mensa &#268;esko'));
            $mpdf->SetCreator(UtfString::strcode2utf('Mensa &#268;esko'));
            $mpdf = certifikat_new($mpdf, $tisk);

            ob_clean();
            $mpdf->Output("tisk_certifikaty.pdf",'D');
            exit();   
        }
        ?>
        <style>
            .modal-content-manual {
                background-color: #fefefe;
                margin: auto;
                padding: 20px;
                border: 1px solid #888;
                width: 900px;
            }
        </style>
    <div style="float: left;">       
        <button id="tisk_certifikat_manual" name="Tisk_man" value="Tisk_man">Tisk certifikátu manuálně</button>
        <div id="colorbox_certifikat_manual" class="modal">
            <div class="modal-content-manual">
                <form name="form_tisk_ceritifkatu_2" method="post" action="" class="form-container">
                    <div style="position: relative;">
                        <table>
                            <tr>
                                <td>Titul</td>
                                <td>Jméno</td>
                                <td>Přijmení</td>
                                <td>Titul za jménem</td>
                            </tr>
                            <tr>
                                <td><input type="text" name="titul" placeholder="Bc."></td>
                                <td><input type="text" name="jmeno" placeholder="Adam"></td>
                                <td><input type="text" name="prijmeni" placeholder="První"></td>
                                <td><input type="text" name="titul_za_jmenem" placeholder="DrSc."></td>
                            </tr>
                            <tr>
                                <td>Datum narození</td>
                                <td>Datum testu</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td><input type="date" name="datum_narozeni" placeholder="0001-1-1"></td>
                                <td><input type="date" name="datum_testu" placeholder="1000-10-10"></td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>IQ (Česká stupnice)</td>
                                <td>IQ (Cattellova stupnice)</td>
                                <td>Percentil</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td><input type="text" name="IQ1" placeholder="138"></td>
                                <td><input type="text" name="IQ2" placeholder="138"></td>
                                <td><input type="text" name="percentil" placeholder="98"></td>
                                <td>&nbsp;</td>
                            </tr>
                        </table>
                        <input type="hidden" class="volba_1" name="volba_1" value="certifikat">
                        <input type="hidden" class="volba_2" name="volba_2" value="manual">
                        <button type="submit" name="Tisk" value="Tisk">Tisk</button>
                        <button type="close_manual" id="close_manual">Zavřít</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div style="float: left; width: 1%;">
        &nbsp;
    </div>
    <script>
        var modal_man = document.getElementById("colorbox_certifikat_manual");
        var btn_man = document.getElementById("tisk_certifikat_manual");
        var btn_close = document.getElementById("close_manual");
        btn_man.onclick = function () {
            modal_man.style.display = "block";
        }
        btn_close.onclick = function () {
            modal_man.style.display = "none";
            return false;
        }
        window.addEventListener('click', function (event) {
            if (event.target == modal_man) {
                modal_man.style.display = "none";
            }
        })
    </script>
    <?php
}

function etikety($men, $c_id_m, $n_id_m)
{
    ?>
    <style>

        * {
            box-sizing: border-box;
        }

        .modal {
            display: none; /* Hidden by default */
            position: fixed; /* Stay in place */
            z-index: 1; /* Sit on top */
            padding-top: 100px; /* Location of the box */
            left: 0;
            top: 0;
            width: 100%; /* Full width */
            height: 100%; /* Full height */
            overflow: auto; /* Enable scroll if needed */
            background-color: rgb(0, 0, 0); /* Fallback color */
            background-color: rgba(0, 0, 0, 0.4); /* Black w/ opacity */
        }

        .modal-content {
            background-color: #fefefe;
            margin: auto;
            padding: 20px;
            border: 1px solid #888;
            width: 500px;
        }

        .papir {
            width: 360px;
            height: 182px;
            border-top: 2px solid #000;
            border-left: 2px solid #000;
            border-bottom: 2px dashed #000;
            border-right: 2px dashed #000;
            overflow: hidden;
            box-sizing: border-box;
            background-color: #bbb;
        }

        .etiketa {
            width: 150px;
            height: 50px;
            border: 1px solid #000;
            position: absolute;
            text-align: center;
            vertical-align: middle;
            line-height: 50px;
            background-color: #ddd;
        }

        #sirka {
            position: absolute;
            top: 25px;
            width: 100px;
            left: 75px;
            color: #F00;
        }

        #vyska {
            position: absolute;
            top: 65px;
            width: 130px;
            left: -25px;
            transform: rotate(-90deg);
            color: #00F;
        }

        .pozice {
            position: absolute;
            left: 250px;
            width: 50px;
            height: 1.2em;
        }

        #horizontalni_mezera {
            position: absolute;
            width: 125px;
            color: #0A0;
            left: 175px;
            top: 25px;
        }

        #vertikalni_mezera {
            position: absolute;
            color: #A0A;
            top: 100px;
            width: 125px;
            left: -40px;
            transform: rotate(-90deg);
        }

        .horizontalni_pozice {
            color: #ffa500;
        }

        .vertikalni_pozice {
            color: #808000;
        }
    </style>
    <div style="float: left;">
        <button id="myBtn">Tisk etiket<?php echo((pocet_lidi($men) == 1) ? "" : "y"); ?></button>
        <button id="myBtn">Tisk etiket<?php echo((pocet_lidi($men) == 1) ? "" : "y"); ?></button>
        <div id="myModal" class="modal">
            <div class="modal-content">
                <form name="form_tisk_adres" method="post" action="export.php">
                    <div style="position: relative;">
                        <div class="papir">
                            <span id="sirka">ŠÍŘKA [mm]</span>
                            <span id="vyska">VÝŠ?KA [mm]</span>
                            <span id="horizontalni_mezera">MEZERA [mm]</span>
                            <span id="vertikalni_mezera">MEZERA [mm]</span>
                            <div class="etiketa"
                                 style="top: 50px; left: 50px; border-top: 3px solid #F00; border-left: 3px solid #00F;">
                                <span class="horizontalni_pozice">1</span>&nbsp;<span class="vertikalni_pozice">1</span>
                            </div>
                            <div class="etiketa" style="top: 50px; left: 210px;">
                                <span class="horizontalni_pozice">2</span>&nbsp;<span class="vertikalni_pozice">1</span>
                            </div>
                            <div class="etiketa" style="top: 110px; left: 50px;">
                                <span class="horizontalni_pozice">1</span>&nbsp;<span class="vertikalni_pozice">2</span>
                            </div>
                            <div class="etiketa" style="top: 110px; left: 210px;">
                                <span class="horizontalni_pozice">2</span>&nbsp;<span class="vertikalni_pozice">2</span>
                            </div>
                            <div style="position: absolute; top: 50px; left: 202px; width: 10px; height: 5px; border-top: 2px solid #0A0; background-color: #0A0;">
                                &nbsp;
                            </div>
                            <div style="position: absolute; top: 102px; left: 50px; width: 5px; height: 10px; border-left: 2px solid #A0A; background-color: #A0A;">
                                &nbsp;
                            </div>
                        </div>
                        <br>
                        <span class="horizontalni_pozice">Počátek tisku horizontálně</span> <input type="text" value="1"
                                                                                                   name="zacatek_x"
                                                                                                   class="pozice"
                                                                                                   required><br>
                        <span class="vertikalni_pozice">Počátek tisku vertikálně</span> <input type="text" value="1"
                                                                                               name="zacatek_y"
                                                                                               class="pozice"
                                                                                               required><br>
                        <span style="color: #F00;">Šířka </span><input type="text" name="rozmer_x" class="pozice"
                                                                       required><br>
                        <span style="color: #00F;">Výška </span><input type="text" name="rozmer_y" class="pozice"
                                                                       required><br>
                        <span style="color: #0A0;">Horizontální mezera </span><input type="text" name="mezera_x"
                                                                                     value="0" class="pozice"
                                                                                     required><br>
                        <span style="color: #A0A;">Vertikální mezera </span><input type="text" name="mezera_y" value="0"
                                                                                   class="pozice" required><br>
                        <br>
                        <span>Formát papíru </span>
                        <select name="format" class="pozice">
                            <option value="A0">A0</option>
                            <option value="A1">A1</option>
                            <option value="A2">A2</option>
                            <option value="A3">A3</option>
                            <option value="A4" selected="selected">A4</option>

                        </select>
                        <br>
                        <input type="hidden" class="volba_1" name="volba_1" value="adresy">
                        <input type="hidden" value="<?php echo $men ?>" name="men">
                        <input type="hidden" value="<?php echo $c_id_m ?>" name="c_id_m">
                        <input type="hidden" value="<?php echo $n_id_m ?>" name="n_id_m">
                        <br>
                        <button type="submit" name="Tisk" value="Tisk">Tisk</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <div style="float: left; width: 1%;">
        &nbsp;
    </div>
    <script>
        var modal = document.getElementById("myModal");
        var btn = document.getElementById("myBtn");
        btn.onclick = function () {
            modal.style.display = "block";
        }
        window.addEventListener('click', function (event) {
            if (event.target == modal) {
                modal.style.display = "none";
            }
        })
    </script>
    <?php
}

?>

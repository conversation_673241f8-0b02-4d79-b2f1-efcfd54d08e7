<?php

/**
 * Novy certifikat
 * @param $mpdf
 * @param $tisk
 * @return mixed
 */
function certifikat_new($mpdf, $tisk)
{
    $rozmer_papiru = array(210, 297);
    $mpdf->SetTitle("Certifikát");
    $obsahPdf = '

<!DOCTYPE html>
<html lang="cs" xml:lang="cs" xmlns="http://www.w3.org/1999/xhtml">
    <head>
<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap" rel="stylesheet">
        <title>Certifikát</title>
        <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
        <style media="print, screen">
            /* definice papiru pro PDF */
            @page {
                size:           ' . ($rozmer_papiru[0]) . 'mm ' . ($rozmer_papiru[1]) . 'mm;
                margin-top:     0;
                margin-right:   0;
                margin-bottom:  0;
                margin-left:    0;
            }
       /* reset vsech defaultnich margins */
            div.text {
                position: absolute;
                left: 50%;
                margin-top: auto;
                margin-left: 0;
                margin-right:0;
                margin-bottom: 0;
                font-family: "Montserrat";
            }
            div.text2 {
                position: absolute;
                left: 50%;
                margin-top: auto;
                margin-left: 0;
                margin-right: 0;
                margin-bottom: 0;
                font-family: "Montserrat";
            }
            div.hlavni {
                position: relative;
                top: 0;
                left: 15mm;
                width: 180mm;
                height: 250mm;
            }
            div.jmeno {
                font-size: 50pt;
                position: absolute;
                left: 10mm;
                top: 70mm;
                width: 156mm;
                height: 20mm;
                text-align: center;
            }
            div.datum_narozeni {
                font-size: 21pt;
                position: absolute;
                top: 96mm;
                left: 68mm;
                width: 40mm;
                height: 8mm;
                text-align: center;
            }
            div.datum_testu {
                font-size: 12pt;
                position: absolute;
                top: 188mm;
                left: 11mm;
                width: 30mm;
                height: 6mm;
                text-align: left;
            }
            div.datum_tisku {
                font-size: 12pt;
                position: absolute;
                top: 188mm;
                left: 129mm;
                width: 30mm;
                height: 6mm;
                text-align: right;
            }
            div.IQ1 {
                font-size: 21pt;
                position: absolute;
                top: 140mm;
                left: 56mm;
                width: 20mm;
                height: 8mm;
                text-align: left;
            }
            div.IQ2 {
                font-size: 21pt;
                position: absolute;
                top: 154mm;
                left: 56mm;
                width: 20mm;
                height: 8mm;
                text-align: left;
            }
            div.IQ3 {
                font-size: 21pt;
                position: absolute;
                top: 140mm;
                left: 84mm;
                width: 30mm;
                height: 8mm;
                text-align: right;
            }
            div.percentil {
                font-size: 21pt;
                position: absolute;
                top: 154mm;
                left: 84mm;
                width: 30mm;
                height: 8mm;
                text-align: right;
            }
            body {
                margin: 0;
                padding: 0;
                background-image: url(/home/<USER>/ssl_pages_new/centr/sekretarka/certificate.jpg);	
		background-size: contain;

            }

        </style>
    </head>
<body background="/home/<USER>/ssl_pages_new/centr/sekretarka/certifikat.jpg">
';

    $j = 1;

    foreach ($tisk as $row) {
        if ($row['pocet_certifikatu'] == NULL) {
            $opakovani = 1;
        } else {
            $opakovani = $row['pocet_certifikatu'];
        }
        for ($i = 1; $opakovani >= $i; $i++) {
            $mpdf->WriteHTML($obsahPdf);
            $mpdf->SetDefaultBodyCSS('background-image-resize', 6);

            $mpdf->WriteHTML("<div class='hlavni'>\n");
            $mpdf->WriteHTML("    <div class='jmeno'>     <div class='text'>" . $row["titul"] . " " . $row["jmeno"] . " " . $row["prijmeni"] . (!empty($row['titul_za_jmenem']) ? (", " . $row['titul_za_jmenem']) : "") . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='datum_narozeni'><div class='text2'>" . $row["datum_narozeni"] . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='datum_tisku'><div class='text'>" . $row["datum_tisku"] . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='datum_testu'><div class='text'>" . $row["datum_testu"] . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='IQ1'>       <div class='text2'>" . htmlspecialchars($row["IQ1"]) . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='IQ2'>       <div class='text2'>" . htmlspecialchars($row["IQ2"]) . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='IQ3'>       <div class='text2'>" . htmlspecialchars($row["IQ3"]) . "</div></div>\n");
            $mpdf->WriteHTML("    <div class='percentil'> <div class='text2'>" . htmlspecialchars($row["percentil"]) . "</div></div>\n");
            $mpdf->WriteHTML("</div>\n");
            $mpdf->WriteHTML('</body></html>');
            if ($j != array_sum(array_column($tisk, 'pocet_certifikatu')) && ($row['pocet_certifikatu'] != NULL)) {
                $mpdf->AddPage();
            }
            $j++;
        }
    }
    return $mpdf;
}
?>

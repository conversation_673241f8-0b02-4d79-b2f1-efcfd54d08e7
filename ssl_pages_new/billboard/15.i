<!-- <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->
<?PHP
require_once("../ssl_library_new/calendar.class.l");



/* zakladni nastaveni */
$calendar = new calendar($db);
if (!isset($mesic)) $mesic = date("m");
if (!isset($rok)) $rok = date("Y");
$par_menu = "?men=" . $men . "&";



/**
 * Funkce pro vytvoreni odkazu na vypisu akci, na ktere je uzivatel prihlasen.
 * @param $key
 * @param $value
 * @return string
 */
function format($key, $value)
{
    if ($key == 'Link')
        return "<a target='_new' href='/index.php?men=men15.0.0.0&&id_a={$value}'>Link</a>";
    return $value;
}
?>

<table cellpadding="0" cellspacing="0" width="100%" border="0">       
    <tr>
        <td valign="top" style="padding-right: 5px;">
            <h1><PERSON><PERSON><PERSON><PERSON><PERSON> akcí</h1>

            <?PHP
            $id_a = @$_GET["id_a"];

            if (strlen($id_a) > 0) {
                // vypis akce
                $str = $calendar->get_action($id_a);
                echo $str;

            } else {
                // zadna akce neni zvolena
                echo "<p>Vyberte akci v tabulce vpravo.<br><br><br><br></p>";

                // vypis akce na ktere je uzivatel prihlasen
                require_once("../ssl_library_new/draw_table_from_query.i");

                echo("<h3>Výpis přihlášek na akce za poslední rok využívajících e-mail: {$a_user['email']}</h3>");
                draw_table_from_query($db, "me_prihlasky",
                    "SELECT w.Jmeno AS 'Jméno', a.Nazev AS 'Název Akce', a.Date_Start AS 'Datum Akce', a.id_a AS 'Link'
                              FROM `www_form_1` AS w JOIN mc_akce AS a ON w.id_a = a.id_a
                              WHERE w.email = '{$a_user['email']}' AND a.Date_Start > DATE_SUB(NOW(),INTERVAL 1 YEAR)
                              ORDER BY a.Date_Start DESC, w.`id_z` DESC", 'format');
            }


            if (!isset($s_month)) {
                // dekoduj mesic, pokud byl predan
                $s_month = date("m");
            }
            ?>
        </td>


        <td width="150" valign="top">
            <?PHP
            // vypis tabulku klandere v pravem sloupci
            $calendar->get_calendar_table($rok, $mesic, "czech", true, @$s_day, @$s_week, @$s_month, $par_menu, "blue_new", "230");
            ?>
        </td>
    </tr>
</table>

<?php use helpers\Html; ?>

<?php /** @var database2 $db */ ?>

<table cellpadding="5" cellspacing="0" border="0" width="100%">
    <tr>
        <td valign="top">
            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tabulka_menu">
                <tr>
                    <td width="15" height="29" style="background-color:#185284" class="tabulka_menu_nadpis">
                        &nbsp;
                    </td>
                    <td>
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td style="background-color:#185284" height="29" class="napis_menu">
                                    <p>Novinky</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>


                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <?php
                        // výzva k zaplacení
                        if ($a_user["prizpevky"] < date("Y")) {
                            echo "<p align='justify'><font color=\"#ff0000\"><strong>
                                Dovolujeme si Vás upozornit, že dosud nemáte zaplacené členské příspěvky na
                                rok " . date("Y") . ". Prosím, uhraďte je nejpozději do 28. února " . date("Y") . ".
                                Nejsnáze je uhradíte převodem na účet číslo: 2801689708/2010 (POZOR nové č. účtu!),
                                variabilní symbol: " . $a_user["clen_cislo"] . ". Podrobnější informace o výši příspěvku
                                a dalších způsobech platby naleznete na stránce
                                <a href='./index.php?men=men1.8.0.0'>Uživatelský profil » Platby</a>.
                                   </strong></font></p>
                                
                                <p align='justify' style='font-weight: bold;'>
                                Pokud byste chtěli své členství ukončit, prosím, nejpozději do 28. února " . date("Y") . "
                                    písemně požádejte o ukončení členství (elektronicky <NAME_EMAIL> nebo dopisem na adrese
                                    Mensa Česko, Španielova 1111/19, 163 00 Praha - Řepy). Prosím, uveďte důvod,
                                    který Vás k tomuto rozhodnutí vede.</p>
                                
                                    <p align='justify' style='font-weight: bold;'>
                                    V případě nezaplacení členského příspěvku zanikne Vaše členství automaticky
                                    ke konci roku " . date("Y") . ". Pokud byste však chtěli takto zaniklé členství
                                    v budoucnu obnovit, bude Mensa požadovat jednorázový
                                    obnovovací poplatek ve výši neuhrazeného členského příspěvku za rok " . date("Y") . ".
                                </p>";
                        }

                        // výzva email
                        if ($a_user["email"] == "") {
                            echo '<p align="justify" style="font-weight: bold;">
                                <font color="#FF0000">
                                Nemáte nastaven email.
                                Prosím, nastavte si jej <a href="/index.php?men=men1.6.0.0" style="color: red;">v profilu</a>.</font></p>';
                        }

                        // výzva kraj
                        if ($a_user["kraj"] == "15") {
                            echo '<p align="justify" style="font-weight: bold;">
                                <font color="#FF0000">
                                Nemáte nastaven kraj.
                                Prosím, nastavte si jej <a href="/index.php?men=men1.1.0.0" style="color: red;">v profilu</a>.  </font></p>';
                        }

                        // výzva prihlaseni do konferenci
                        $DATABASE_NAME = "mensaweb"; // pro pouziti v ostrem provozu
                        $q_pocet_konferenci = $db->query("SELECT COUNT(*) AS count
                                                          FROM {$DATABASE_NAME}.m_ae_konfcfg
                                                          WHERE id_m = {$a_user['id_m']}");
                        $r_pocet_konferenci = $db->FetchAssoc($q_pocet_konferenci);

                        // pokud je pocet odebiranych konferenci mensi nez 1, zobraz vyzvu
                        // k prihlaseni se do konferenci
                        if ($r_pocet_konferenci['count'] < 1) {
                            echo
                            '<p align="justify" style="font-weight: bold; color: red;">
                            Neodebíráte žádné zprávy z místních nebo zájmových skupin, můžete přijít o pozvánky na zajímavé akce.
                            Prosím, zkontrolujte si nastavení
                            <a href="/index.php?men=men20.2.0.0" style="color: red;">příjmu konferencí</a>.</p>';
                        }

                        ?>


                        <?php
                        //zobrazení aktualit z databáze s platným datem
                        $SQL = "SELECT html FROM mensaweb.aktuality WHERE CURRENT_DATE BETWEEN date_view_start AND date_view_end ORDER BY date_view_start DESC";
                        $news = $db->Query($SQL);

                        while ($row = $db->FetchArray($news)) {
                            echo "<p align='justify'>" . $row['html'] . "</p>\n\n";
                        }
                        ?>

                    </td>
                </tr>
            </table>


            <!--    // **********************************************************
                    //         Pomozte Mense
                    // **********************************************************  -->
            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tabulka_menu" style="margin-top:8px;">
                <tr>
                    <td width="15" height="29" style="background-color:#185284">
                        &nbsp;
                    </td>
                    <td>
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td style="background-color:#185284" height="29" class="napis_menu">
                                    <p>Pomozte Mense</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <p align="justify">
                            Prosíme, podpořte největší projekt Mensy Česko – Logickou olympiádu – snadno a rychle <a
                                    href="https://payments.mensa.cz/" target="_blank">darem pomocí platební karty</a>.
                        </p>
                    </td>
                </tr>


                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <p align="justify">
                            Hledáme dobrovolníky pro rozvoj nejrůznějších <a target="_blank"
                                                                             href="/document.php?men=men14.3.0.0&id_c=843">mensovních
                                aktivit</a>.

                            Pokud chcete být informováni o novinkách a změnách, <a href="./index.php?men=men20.2.0.0">přihlaste
                                se do konference</a> <b>SIGu Dobrovolník</b>. Mensa bude taková, jakou si ji
                            uděláme.
                        </p>
                    </td>
                </tr>


                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <p align='justify'>Nebojíte se MySQL a PHP? Prosíme, pomozte nám posunout intranet na novou
                            úroveň. Přihlaste se do <a href="./index.php?men=men20.2.0.0">konference SIG Rozvoj
                                intranetu</a>. Virtuální setkání programátorů se konají každé úterý večer.<br /></p></td>
                </tr>


            </table>


            <!--    // **********************************************************
                    //          TABULKA S NOVYMI INFORMACNIMI EMAILY
                    // ********************************************************** -->
            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tabulka_menu" style="margin-top:8px;">
                <tr>
                    <td width="15" height="29" style="background-color:#185284">
                        &nbsp;
                    </td>
                    <td>
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <td style="background-color:#185284" height="29" class="napis_menu">
                                <a href="index.php?men=men20.3.0.0">Nové zprávy</a>
                            </td>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <?php
                        require_once("../ssl_library_new/get_new_emails2.l");        //ziska informace o novych rozeslanych emailech dle X dni zpet

                        $tmp = get_new_emails2(4, $db);
                        $poc = count($tmp);

                        if ($poc > 0) {
                            for ($i = 0; $i < $poc; $i++) {
                                ?>

                                <div style="margin-bottom: 8px; clear: both;">
                                    <div style="width: 50%; float: left;">
                                        <strong><a href="index.php?men=men20.3.0.0&detail=<?php echo $tmp[$i]['id']; ?>"><?php echo $tmp[$i]['subject']; ?></a></strong>
                                    </div>

                                    <a href="index.php?men=men4.2.0.0&s_content=view.i&id_m=<?PHP echo $tmp[$i]['id_m']; ?>"><?php echo $tmp[$i]['autor']; ?></a>,
                                    <?php echo strip_tags($tmp[$i]['meta']); ?>


                                    <div style="float: right;"><?php echo $tmp[$i]['input_date2']; ?></div>
                                </div>

                                <div style="clear: both; margin-bottom: 1em;">
                                    <?php
                                    $zprava = Html::deduplicateLineBreakTags(mb_substr(str_replace("<br />\r\n<br />", "<br />", nl2br(trim($tmp[$i]['body']))), 0, 200));

                                    echo "<p>" . $zprava . "&hellip;</p>";
                                    ?>
                                </div>
                                <?php
                            }
                        }
                        ?>
                    </td>
                </tr>
            </table>

            <!--    // **********************************************************
                    //          TABULKA S NOVYMI DISKUSEMI
                    // ********************************************************** -->
            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tabulka_menu">
                <tr>
                    <td width="15" height="29" style="background-color:#185284">
                        &nbsp;
                    </td>
                    <td>
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <td style="background-color:#185284" height="29" class="napis_menu">
                                <a href="index.php?men=men5.0.0.0">Aktuální diskuse</a>
                            </td>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <p>Přehled 15 témat s nejnovějšími příspěvky.
                            Další diskuse najdete pod záložkou
                            <a href="/index.php?men=men5.0.0.0">diskusní fórum</a> v menu vlevo,
                            v diskusním fóru je více než 6&nbsp;000 příspěvků v 250 tématech.
                            <br /><br />
                            Toto fórum slouží pouze pro vnitřní diskusi na intranetu a nikdo není oprávněn
                            jakékoliv části jeho obsahu bez souhlasu všech autorů zveřejňovat mimo Mensu.
                            Neužívejte vulgarity. Autoři vulgárních příspěvků budou vystaveni postihu včetně případného
                            omezení přístupu na fórum.

                        </p>

                        <ul>
                            <?php
                            $tmp = $db->Query("SELECT phpbb_topics.* FROM phpbb_topics ORDER BY topic_last_post_time DESC LIMIT 15");                          
                            setlocale(LC_ALL, 'cs_CZ');
                            
                            while ($row = $db->FetchArray($tmp)) {
                                
                                switch ($row["topic_replies"]) {
                                    case 1:
                                        $prz = "odpověď";
                                        break;
                                    case 2:
                                    case 3:
                                    case 4:
                                        $prz = "odpovědi";
                                        break;
                                    default:
                                        $prz = "odpovědí";
                                }

                                    if ($row["topic_replies"] != 0) {
                                            echo "<li style=\"font-size:12px;padding-bottom:5px;\"><a href=\"/index.php?men=men5.3.0.0&f=" . $row["forum_id"] . "&t=" . $row["topic_id"] . '&amp;p=' . $row['topic_last_post_id'] . '#p' . $row['topic_last_post_id'] . "\">" . $row["topic_title"] . "</a> (" . $row["topic_replies"] . " $prz, poslední dne " . strftime("%e. %B %Y v %H:%M", $row["topic_last_post_time"]) . " napsal/a ".$row["topic_last_poster_name"].".) </li>";
                                            } else { 
                                                echo "<li style=\"font-size:12px;padding-bottom:5px;\"><a href=\"/index.php?men=men5.3.0.0&f=" . $row["forum_id"] . "&t=" . $row["topic_id"] . "\">" . $row["topic_title"] . "</a> (Nové téma, založil/a ".$row["topic_last_poster_name"]." " . strftime("%e. %B %Y v %H:%M", $row["topic_last_post_time"]) . ".) </li>";
                                                }                                
                            }
                            ?>
                        </ul>
                    </td>
                </tr>
            </table>

            <!--    // **********************************************************
                    //          TABULKA S NOVYMI FOTOGRAFIEMI
                    // ********************************************************** -->
            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tabulka_menu" style="margin-top:8px;">
                <tr>
                    <td width="15" height="29" style="background-color:#185284">
                        &nbsp;
                    </td>
                    <td>
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr>
                                <td style="background-color:#185284" height="29" class="napis_menu">
                                    <a href="index.php?men=men23.0.0.0">Nové fotografie</a>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <?php
                        require_once("../ssl_pages_new/photogallery2/include/constants.inc.php");
                        require_once("../ssl_pages_new/photogallery2/include/general.inc.php");

                        echo generate_new_album($db);
                        ?>
                    </td>
                </tr>
            </table>


            <!--    // **********************************************************
                    //          TABULKA S aktivitou, Tomáš Kubeš, 18. února 2012
                    // **********************************************************  -->
            <table width="100%" border="0" cellpadding="0" cellspacing="0" class="tabulka_menu" style="margin-top:8px;">
                <tr>
                    <td width="15" height="29" style="background-color:#185284">
                        &nbsp;
                    </td>
                    <td>
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <td style="background-color:#185284" height="29" class="napis_menu">
                                <p>Aktivita na intranetu Mensy</p>
                            </td>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td width="15">&nbsp;</td>
                    <td style="width:100%;padding: 10px 15px 5px 5px;">
                        <p align="justify">
                            <?php
                            $den_q = $db->Query("SELECT COUNT( * ) FROM mensaweb.`m_members_login` WHERE cas > DATE_SUB( NOW(), INTERVAL 1 DAY)");
                            $den_r = $db->FetchRow($den_q);
                            echo "Zaznamenali jsme přístupy od <strong>" . $den_r[0] . "</strong> různých členů za posledních 24 hodin, ";

                            $tyd_q = $db->Query("SELECT COUNT( * ) FROM mensaweb.`m_members_login` WHERE cas > DATE_SUB( NOW(), INTERVAL 7 DAY)");
                            $tyd_r = $db->FetchRow($tyd_q);
                            echo "<strong>" . $tyd_r[0] . "</strong> různých členů za posledních 7 dnů, ";

                            $mes_q = $db->Query("SELECT COUNT( * ) FROM mensaweb.`m_members_login` WHERE cas > DATE_SUB( NOW(), INTERVAL 31 DAY)");
                            $mes_r = $db->FetchRow($mes_q);
                            echo "<strong>" . $mes_r[0] . "</strong> různých členů za poslední měsíc ";

                            $mes_q = $db->Query("SELECT COUNT( * ) FROM mensaweb.`m_members_login` WHERE cas > DATE_SUB( NOW(), INTERVAL 61 DAY)");
                            $mes_r = $db->FetchRow($mes_q);
                            echo "<strong>" . $mes_r[0] . "</strong> různých členů za poslední dva měsíce a ";

                            $rok_q = $db->Query("SELECT COUNT( * ) FROM mensaweb.`m_members_login` WHERE cas > DATE_SUB( NOW(), INTERVAL 184 DAY)");
                            $rok_r = $db->FetchRow($rok_q);
                            echo "<strong>" . $rok_r[0] . "</strong> různých členů za poslední půlrok.";
                            ?>
                        </p>
                    </td>
                </tr>
            </table>


        </td><!-- konec centrálního sloupce -->

        <!-- pravy sloupec na desktopu -->
        <td valign="top" class="desktop-calendar">
            <?php include("../ssl_library_new/news_table.i"); ?>
        </td>
    </tr>
    <tr class="mobile-calendar">
        <!-- pravy sloupec na mobilu (radi se dolu) -->
        <td valign="top">
            <?php include("../ssl_library_new/news_table.i"); ?>
        </td>
    </tr>
</table>

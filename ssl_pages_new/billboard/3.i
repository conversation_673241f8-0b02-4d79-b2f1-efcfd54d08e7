Administrace -  do této části má přístup vybrana skupina lidí.
Je<PERSON><PERSON> výčet vidíte zde: <br><br>
<?php
$SQL = "select count(id_m) as pocet from m_members";
$tmp = $db->Query($SQL);
$pocet_clenu=$db->getResult($tmp, 0, "pocet");

$rok=date("Y");
$tmp = $db->Query("SELECT count(id_m) AS pocet FROM m_members WHERE prizpevky < $rok");
$nezaplaceno=$db->getResult($tmp, 0, "pocet");

$tmp = $db->Query("SELECT count(id_m) AS pocet FROM m_members WHERE prizpevky = $rok");
$zaplaceno=$db->getResult($tmp, 0, "pocet");

$tmp = $db->Query("SELECT count(id_m) AS pocet FROM m_members WHERE prizpevky > $rok");
$dopredu=$db->getResult($tmp, 0, "pocet");

?>
počet členů v DB: <?PHP echo $pocet_clenu ?><br>
počet nezaplacenych prispevku: <?PHP echo $nezaplaceno ?><br>
pocet zaplacených prispevku na rok <?PHP echo $rok ?>: <?PHP echo $zaplaceno ?><br>
pocet dopredu zaplacenych prispevku: <?PHP echo $dopredu ?><br>
*****************************************<br>
Statistika účtů založených předregistrací:
<table border="0">
<?PHP
$tmp = $db->Query("select CONCAT(YEAR(created), '-', MONTH(created)) as tridit, count(created) as pocet FROM m_acc_uid2role WHERE id_m=created_by  AND YEAR(created)>2004 GROUP BY tridit ORDER BY YEAR(created) asc, MONTH(created) asc");
echo $db->error;
$i = $db->getNumRows($tmp);
while($i--){
	echo "<tr><td><p>".$db->getResult($tmp, $i, "tridit")."</p></td><td><p>:".$db->getResult($tmp, $i, "pocet")."</p></td></tr>";
}
?>
</table>
<br>
*****************************************<br>
<b>ZAPLACENO DO:</b><br>
<?PHP
$SQL = "select prizpevky, count(prizpevky) as pocet from m_members GROUP BY prizpevky ORDER BY prizpevky";
$tmp = $db->Query($SQL);

$pocet = $db->getNumRows($tmp);
for ($i=0;$i<$pocet;$i++){
	echo "rok ".$db->getResult($tmp, $i, "prizpevky")." - ".$db->getResult($tmp, $i, "pocet")."<br>";
}


// phpinfo();

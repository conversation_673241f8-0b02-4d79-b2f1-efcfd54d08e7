<h1>Statistika využití intranetu</h1>
<?PHP
    /**
    Statistika využití
    především pohled na tabulku m_activity_log, kde se zapisuje každé zobrazní stránky.

    Změnovník
        2012-Jun-15, TK: Založeno

    */


    /*
    CREATE TABLE IF NOT EXISTS `m_activity_log` (
      `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT 'Primary key',
      `id_m` int(11) NOT NULL COMMENT 'odkaz do tabulky m_members',
      `time_stamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'datum a cas navstevy',
      `s1` tinyint(1) unsigned NOT NULL DEFAULT '0',
      `s2` tinyint(1) unsigned NOT NULL DEFAULT '0',
      `s3` tinyint(1) unsigned NOT NULL DEFAULT '0',
      `s4` tinyint(1) unsigned NOT NULL DEFAULT '0',
      `soubor` varchar(90) DEFAULT NULL COMMENT 'navstivena stranka',
      PRIMARY KEY (`id`)
    ) ENGINE=MyISAM  DEFAULT CHARSET=latin1 COMMENT='Záznam všech navštívených stránek' AUTO_INCREMENT=140552 ;
    */

	// funkce na výpis tabulky
	require("../ssl_library_new/draw_table_from_query.i");
?>

<style>
	tr.even
	{
		background-color: #eeeeff;
	}

	/*
	table.draw_table_from_query td
	{
		text-align: right;
		padding-right: 0.5em;
	}*/

	table.draw_table_from_query td.Počet_zobrazení, .Měsíc, .Den, .Příjem_zpráv, .Příjem_konferencí, .Rozešli, .Historie, .Celkem
	{
		text-align: right;
		padding-right: 0.5em;
	}


	table.draw_table_from_query th
	{
		text-align: center;
	}
</style>



<?PHP
echo "<p>Tato stránka byla vygenerována ".date("Y-m-d H.i:s").".</p>";

// Smaž staré záznamy, aby tabulka moc nebobtnala.
// 2 měsíce představují nějakých 130 000 záznamů.
$qr_del = $db->Query("DELETE FROM mensaweb.`m_activity_log` WHERE `time_stamp` <= DATE_SUB(now(), INTERVAL 2 MONTH)");
$smazano = (int) $db->getAffectedRows();
echo "<p>Smazal jsem {$smazano} záznamů starších než 2 měsíce (neobjeví se v další statistice).</p>";

// vypiš uživateli, z jakých záznamů se počítá statistika
$qr_min = $db->Query("SELECT min(`time_stamp`) as 'datum', count(id) as pocet FROM mensaweb.`m_activity_log`");
$prvni = $db->FetchAssoc($qr_min);
echo "<p>První nesmazaný nalezený záznam je ze dne {$prvni['datum']},
    od té doby eviduji celkem {$prvni['pocet']} zobrazní stránek intranetu.</p>";

?>










<h2>Unikátní přihlášení do intranetu</h2>
<p>Vlevo je celkový počet unikátních členů přihlášených do intranetu daný den, vpravo je, pro kolik z nich to bylo první přihlášení ve sledovaném období.</p>
<table>
<tr>
<td>
<p>Počet unikátních členů, kteří se přihlásili do intranetu daný den.</p>
<?PHP
draw_table_from_query ($db, "prihlaseni1", "
-- pocet unikatnich lidi, kteri se prihlasili do intranetu dany den
SELECT mesic as 'Měsíc', den as 'Den', count( id_m ) as 'Členů'
FROM (
-- vyber unikatni prihlaseni daneho clena v dany den
SELECT DISTINCT month( time_stamp ) AS mesic, day( time_stamp ) AS den, id_m
FROM m_activity_log ) raw_data
GROUP BY mesic, den
ORDER BY mesic ASC , den ASC");
?>
</td>
<td>
<p>Stejné jako tabulka vlevo, ale zobrazuje pouze první přihlášení člena do intranetu ve zobrazené periodě.</p>

<?PHP
draw_table_from_query ($db, "prihlaseni2", "
-- pocet unikatnich lidi, kteri se prihlasili do intranetu dany den
SELECT mesic as 'Měsíc', den as 'Den', count( id_m ) as 'První přihlášení'
FROM (
SELECT  month( ts ) AS mesic, day( ts ) AS den, id_m
FROM
(
-- vyber unikatni prihlaseni daneho clena v dany den
SELECT DISTINCT min( time_stamp ) AS ts, id_m
FROM m_activity_log
group by id_m
) raw_data
) filtered_data

GROUP BY mesic, den
ORDER BY mesic ASC , den ASC");
?>
</td>
</tr>
</table>





<p>&nbsp;</p>
<h2>Statistika přihlášek na akce</h2>
<p>Data jsou brána z přihlášek na akce v kaledáři akcí (tabulka je společná pro akce i testování).
Data se počítají za posledních 365 dnů.</p>
<?PHP
/* účast na Akcích a TESTECH, analýza tabulky www_form_1
CREATE TABLE IF NOT EXISTS `www_form_1` (
  `id_z` int(11) NOT NULL AUTO_INCREMENT,
  `jmeno` varchar(200) DEFAULT NULL,
  `vek` varchar(80) NOT NULL DEFAULT '',
  `telefon` varchar(50) NOT NULL DEFAULT '',
  `email` varchar(100) DEFAULT NULL,
  `typ_testu` varchar(100) DEFAULT NULL,
  `termin` varchar(100) DEFAULT NULL,
  `poznamka` mediumtext,
  `datum` datetime NOT NULL DEFAULT '0000-00-00 00:00:00',
  `id_a` int(11) NOT NULL DEFAULT '0',
  `id_org` int(11) NOT NULL DEFAULT '1303',
  `stav` tinyint(4) NOT NULL DEFAULT '0',
  `adresa` varchar(255) DEFAULT NULL,
  `mesto` varchar(200) DEFAULT NULL,
  `psc` varchar(100) DEFAULT NULL,
  `datumnar` varchar(100) DEFAULT NULL,
  `rc` varchar(100) DEFAULT NULL,
  `cc` varchar(100) DEFAULT NULL,
  `variabilni` varchar(100) DEFAULT NULL,
  `vol1` varchar(200) DEFAULT NULL,
  `vol2` varchar(200) DEFAULT NULL,
  `vol3` varchar(200) DEFAULT NULL,
  `ip` varchar(25) NOT NULL,
  PRIMARY KEY (`id_z`),
  KEY `id_a` (`id_a`),
  KEY `id_org` (`id_org`),
  KEY `typ_testu` (`typ_testu`),
  KEY `stav` (`stav`)
) ENGINE=MyISAM  DEFAULT CHARSET=latin1 AUTO_INCREMENT=21825 ;

Dále
mc_akce             popis akcí
mc_akce_prihlasky   popis přihlášek

SELECT DISTINCT typ FROM `mc_akce` WHERE 1
    0
    2
    diag
    dm
    mensa
    ms
    sig
    test
*/


$akci = $db->FetchAssoc($db->Query("SELECT count(id_a) AS 'pocet',
    SUM(IF(typ != 'test', 1, 0)) AS 'pocetbezt' FROM mensaweb.`mc_akce`
    WHERE date_input >= DATE_SUB(now(), INTERVAL 1 YEAR) "));

$prihlasek = $db->FetchAssoc($db->Query("SELECT count(id_a) AS 'pocet',
    SUM(IF(typ != 'test', 1, 0)) AS 'pocetbezt'  FROM mensaweb.`mc_akce`
    WHERE date_input >= DATE_SUB(now(), INTERVAL 1 YEAR) AND prihlaska = 1"));

$prihlasenych = $db->FetchAssoc($db->Query("SELECT count( * ) AS 'pocet' FROM mensaweb.`www_form_1`
    WHERE datum >= DATE_SUB(now(), INTERVAL 1 YEAR) "));

$prihlasenychMimoT = $db->FetchAssoc($db->Query("SELECT count( * ) AS 'pocet' FROM mensaweb.`www_form_1`
    WHERE datum >= DATE_SUB(now(), INTERVAL 1 YEAR)
    AND typ_testu IS NULL"));

$jmen = $db->FetchAssoc($db->Query("SELECT count(*) as 'pocet' FROM (
    SELECT DISTINCT jmeno FROM mensaweb.`www_form_1` WHERE datum >= DATE_SUB(now(), INTERVAL 1 YEAR)) a"));

$jmenMimoT = $db->FetchAssoc($db->Query("SELECT count(*) as 'pocet' FROM (
    SELECT DISTINCT jmeno FROM mensaweb.`www_form_1` WHERE datum >= DATE_SUB(now(), INTERVAL 1 YEAR)
    AND typ_testu IS NULL) a"));


$cisla = $db->FetchAssoc($db->Query("SELECT count(*) as 'pocet' FROM (
    SELECT DISTINCT cc FROM mensaweb.`www_form_1` WHERE datum >= DATE_SUB(now(), INTERVAL 1 YEAR)
    AND typ_testu IS NULL) a"));

$maily = $db->FetchAssoc($db->Query("SELECT count(*) as 'pocet' FROM (
    SELECT DISTINCT email FROM mensaweb.`www_form_1` WHERE datum >= DATE_SUB(now(), INTERVAL 1 YEAR)
    AND typ_testu IS NULL) a"));
?>

<ul>
<li>Počet vypsaných akcí za poslední rok (dle data založení akce): <?=$akci['pocet']?>,
    z toho nejsou testování IQ: <?=$akci['pocetbezt']?>.</li>

<li>Počet vypsaných akcí s přihláškou za poslední rok  (dle data založení akce): <?=$prihlasek['pocet']?>,
    z toho nejsou testování IQ: <?=$prihlasek['pocetbezt']?>.</li>

<li>Počet přihlášených na akce v posledním roce (dle data přihlášení): <?=$prihlasenych['pocet']?>,
    z toho na akce, které nejsou testování IQ: <?=$prihlasenychMimoT['pocet']?>.</li>

<li>Počet počet různých jmen v přihláškách na akce za poslední rok: <?=$jmen['pocet']?>,
    z toho na akce, které nejsou testování IQ:  <?=$jmenMimoT['pocet']?>.</li>

<li>Počet počet různých členských čísel v přihláškách na akce za poslední rok: <?=$cisla['pocet']?> (na akcích, které nejsou testováním).</li>

<li>Počet počet různých emailů v přihláškách na akce za poslední rok: <?=$maily['pocet']?> (na akcích, které nejsou testováním).</li>

</ul>




<p>&nbsp;</p>
<h2>Počty zobrazení stránek v sekci</h2>

<?PHP
echo "<p>Agregovaný počet zobrazení (myšleno jednotlivé načtení) všech stránek v dané rubrice
    (identifikované dle s1, tj. prvního čísla v adrese) od  {$prvni['datum']}.</p>

    <p><i>Poznámka: Rubrika fotogalerie zahrnuje zobrazní každé jednotlivé fotografie.
    Podrobné rozebrání na jednotlivé stránky naleznete níže.</i></p>";

draw_table_from_query ($db, "rubriky", "
SELECT
    n0.class_descr  as 'Sekce',
    if (LENGTH(pocet) > 3, CONCAT (LEFT(pocet, LENGTH(pocet)-3) ,' ', RIGHT(pocet, 3)), pocet)
                    as 'Počet zobrazení'
FROM
    (SELECT
        s1,
        count(id) as pocet
    FROM
        mensaweb.`m_activity_log`
    GROUP BY
        s1
    ORDER BY
        pocet DESC) p
    JOIN
        mensaweb.m_acc_class n0
        ON (p.s1 = n0.s1 AND 0 = n0.s2 AND 0 = n0.s3 AND 0 = n0.s4)

    ");
?>





<p>&nbsp;</p>
<h2>30 nejaktivnějších uživatelů intranetu</h2>
<?PHP
echo "<p>Počet všech stránek, které zobrazil jednotlivý uživatel od {$prvni['datum']}, zobrazuje se prvních 30 nejaktivnějších uživatelů.</p>";

draw_table_from_query ($db, "rubriky", "
SELECT
    jmeno       as 'Jméno',
    prijmeni    as 'Příjmení',
    if (LENGTH(pocet) > 3, CONCAT (LEFT(pocet, LENGTH(pocet)-3) ,' ', RIGHT(pocet, 3)), pocet)
                as 'Počet zobrazení'
FROM
    (SELECT
        id_m,
        count(id) as pocet
    FROM
        mensaweb.`m_activity_log`
    GROUP BY
        id_m
    ORDER BY
        pocet DESC
    LIMIT 0, 30) p
    JOIN
        mensaweb.m_members m
        ON (p.id_m = m.id_m)

    ");
?>




<p>&nbsp;</p>
<?PHP
echo "<h2>Využití sekce Rozesílání zpráv od {$prvni['datum']}</h2>";
//echo "<p>Pro zpřehlednění jsou dny sloučeny po dvou, například datum 1. května ukazuje sumu za 1. 5. a 2. 5.</p>";
draw_table_from_query ($db, "cas", "
SELECT
    mesic   as 'Měsíc',
    den     as 'Den',
    s20     as 'Rozešli',
    s21     as 'Příjem zpráv',
    s22     as 'Příjem konferencí',
    s23     as 'Historie',
    if (LENGTH(pocet) > 3, CONCAT (LEFT(pocet, LENGTH(pocet)-3) ,' ', RIGHT(pocet, 3)), pocet)
            as 'Celkem'
FROM
    (SELECT
        month(`time_stamp`)   as mesic,
        -- floor((day(`time_stamp`)+1)/2)*2    as den,
        day(`time_stamp`)     as den,
        sum(if(s2 = 0, 1, 0)) as s20,
        sum(if(s2 = 1, 1, 0)) as s21,
        sum(if(s2 = 2, 1, 0)) as s22,
        sum(if(s2 = 3, 1, 0)) as s23,
        count(id) as pocet
    FROM
        mensaweb.`m_activity_log`
    WHERE
        s1 = 20
    GROUP BY
        mesic, den
        -- mesic, round(den/5)
        -- week(`time_stamp`)
    ORDER BY
        mesic ASC,den ASC) p");
?>






<p>&nbsp;</p>
<h2>Počty zobrazení jednotlivých stránek</h2>
<?PHP
echo "<p>Zaznamenává se každé zobrazení kterékoliv stránky od  {$prvni['datum']},
    zobrazují se pouze stránky, které mají více než 10 zobrazení.</p>

    <p><i>Poznámka: Rubrika fotogalerie zahrnuje zobrazní každé jednotlivé fotografie.
    Podrobné rozebrání na jednotlivé stránky naleznete níže.</i></p>
    ";

draw_table_from_query ($db, "rubriky", "
SELECT
    n0.class_descr  as 'Rubrika',
    n.class_descr   as 'Stránka',
    if (LENGTH(pocet) > 3, CONCAT (LEFT(pocet, LENGTH(pocet)-3) ,' ', RIGHT(pocet, 3)), pocet)
                    as 'Počet zobrazení'
FROM
    (SELECT
        s1, s2, s3, s4,
        count(id) as pocet
    FROM
        mensaweb.`m_activity_log`
    GROUP BY
        s1, s2, s3, s4
    ORDER BY
        pocet DESC) p
JOIN
    mensaweb.m_acc_class n
    ON (p.s1 = n.s1 AND p.s2 = n.s2 AND p.s3 = n.s3 AND p.s4 = n.s4)
JOIN
    mensaweb.m_acc_class n0
    ON (p.s1 = n0.s1 AND 0 = n0.s2 AND 0 = n0.s3 AND 0 = n0.s4)
WHERE
    pocet >= 10
    ");
?>











<p style="text-align:right; margin-top:5em;"><em>Stránku naprogramoval Tomáš Kubeš 15. června 2012.</em></p>


<?PHP
//phpinfo();
//foreach ($upload as $key => $value) {
//    echo "Key: $key, value: $value"."<br>";
//}


$access = access("create", $men, $a_user["id_m"], $db);

If ($access){
        if (isset($submit)){ 
			require("../ssl_library_new/upload_file.l");
			preg_match("/\.(.*)\$/", $upload["name"], $part);
			$suffix = $part[1];
			
			$a_part["name"] = $name;
			$a_part["descr"] =$descr;
			$a_part["owner"] = $login["id_m"];
//			$a_part["date_expir"] = $to_expire;
			$a_part["sect2"] = $sect2;
			$a_part["upload_name"] = $upload["name"];
//			$a_part["long_descr"] = $long_descr;
			$a_part["id_m"] = $a_user["id_m"];

			upload_file($upload, $a_part, $db);
			
        } else {
		//require("../ssl_library/cav_get_list.l");
    ?> 
<form action="index.php" enctype="multipart/form-data" method="POST"><p><b>Vložení dokumentu</b><br>
<input type=hidden name="men"  value="<?PHP echo $men ?>"> 
<input type=hidden name="pg" value="ins"> 
<input type=hidden name=MAX_FILE_SIZE value="10000000"> 
<input type=hidden name="sect2" value="<?PHP echo substr($men,3); ?>"> 

<table cellpadding="0" cellspacing="3" border="0">
<tr>
	<td><p>Název:</p></td>
	<td align="right"><p><input type="text" name="name" size="50"></p></td>
</tr>
<tr>
	<td><p>Soubor:</p></td>
	<td align="right"><p><input name="upload" size="30" type="file"></p></td>
</tr>
<tr>
	<td colspan="2"><p>Popis:<br><textarea id="mceNoEditor" cols="53" rows="6" name="descr" class="mceNoEditor"></textarea></td>
</tr>
<tr>
	<td colspan="2" align="right"><p><input type="submit" name="submit" value=" V l o ž i t "></p></td>
</tr>
</table></p></form>
<?PHP
		}
} else {
?>
<p>&nbsp;</p><p align="center"><font color="Red"><b>Nejste oprávněni pro vkládání dokumentů</b></font></p>
<?PHP
}
?>
<?PHP
/**
    Použit na výpis všech stran se seznamem dokumentů.
    
    Změnovník:
        2012-04-23, TK: Změněno stránkování na 35 a odkaz na vložení nového dokumentu.

*/
require LIB_NEW_DIR . "/get_content.l";
require LIB_NEW_DIR . "/strankovani.l";

// TODO cleanup and move to file functions
function size_readable($size, $retstring = null)
{
    // adapted from code at http://aidanlister.com/repos/v/function.size_readable.php
    $sizes = array('B', 'kB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB');

    if ($retstring === null) {
        $retstring = '%01.0f %s';
    }

    $lastsizestring = end($sizes);

    foreach ($sizes as $sizestring) {
        if ($size < 1024) {
            break;
        }
        if ($sizestring !== $lastsizestring) {
            $size /= 1024;
        }
    }

    if ($sizestring === $sizes[0]) {
        $retstring = '%01d %s';
    } // Bytes aren't normally fractional

    return sprintf($retstring, $size, $sizestring);
}

$s_od = "2000-01-01";
$s_do = date("Y-m-d");

$zmenit = access("update", $men, $a_user["id_m"], $db);
$smazat = access("delete", $men, $a_user["id_m"], $db);

if (empty($start)) {
    $start = 0;
}

$tmp = get_content($db, $men, $a_vypis, $s_od, $s_do, 2, 0, $start, 35, $celkem);

if ($tmp == 0) {
    echo "Uvedená část neobsahuje žádné dokumenty.<br><br>";
    echo "<a href=\"index.php?men=$men&pg=ins\">Zde vložte dokumenty.</a>";
}
else {
    // pokud má povoleno měnění, přidej link na vložení dokumentu
    if ($zmenit) {
        echo "<p><a href=\"index.php?men=$men&pg=ins\">Vložit nový dokument do této sekce</a></p>";
    }


    echo "<table border=\"0\" cellpadding=\"0\" cellspacing=\"0\">";

    echo "<tr><th>Dokument</th><th>Typ</th><th>Velikost</th><th>Vložil</th><th>Aktualizováno</th>";

    if ($zmenit) {
        echo "<th>&nbsp;</th>";
    }
    if ($smazat) {
        echo "<th>&nbsp;</th>";
    }
    echo "</tr>\n";

    for ($i = 0; $i < $tmp; $i++) {
        echo "<tr class=\"tr_".($i % 2)."\">\n";

        echo "<td class=\"td_line\"><a href=\"document.php?men=$men&id_c=".$a_vypis["id_c"][$i]."\" target=\"_new\" title=\"".$a_vypis["descr"][$i]."\">".$a_vypis["name"][$i]."</a>&nbsp;</td>\n";
        echo "<td class=\"td_line\">".$a_vypis["suffix"][$i]."&nbsp;</td>\n";
        echo "<td class=\"td_line\" align=\"right\">";

        $filename = "../files/documents_content/{$a_vypis["path"][$i]}";

        if (is_file($filename) && is_readable($filename)) {
            echo size_readable(filesize("../files/documents_content/{$a_vypis["path"][$i]}"));
        }

        echo "&nbsp;</td>\n";
        echo "<td class=\"td_line\">".$a_vypis["vlozil"][$i]."&nbsp;</td>\n";
        echo "<td class=\"td_line\">".$a_vypis["date_edit2"][$i]."&nbsp;</td>\n";

        $x = 4;

        if ($zmenit) {
            echo "<td class=\"td_line\"><a href=\"index.php?men=$men&pg=upd&id_c=".$a_vypis["id_c"][$i]."\"><img src=\"images/ico_edit.gif\" alt=\"Editovat dokument\" title=\"Editovat dokument\" width=\"18\" height=\"18\" border=\"0\"></a>&nbsp;</td>";
            $x++;
        }

        if ($smazat) {
            echo "<td class=\"td_line\"><a href=\"index.php?men=$men&pg=del&id_c=".$a_vypis["id_c"][$i]."\"><img src=\"images/ico_delete.gif\" width=\"18\" height=\"18\" alt=\"Smazat dokument\" title=\"Smazat  dokument\" border=\"0\"></a>&nbsp;</td>";
            $x++;
        }
        echo "</tr>\n";
    }

    echo "<tr class=\"tr_".($i % 2)."\"><td colspan=\"".($x - 2)."\" align=\"center\">".strankovani($celkem, $start, 35, "index.php?men=$men")."</td>
    
    <td colspan='3' style='vertical-align: middle; text-align: right;'>";

    // přidej tlačítko jen pokud je změna povolená
    if ($zmenit) {
        echo "
            <a href=\"index.php?men=$men&pg=ins\">vložit dokument</a>
            <a href=\"index.php?men=$men&pg=ins\"><img src=\"images/ico_new.gif\" alt=\"Vložit dokument\" title=\"Vložit dokument\" width=\"18\" height=\"18\" border=\"0\"></a>";
    }

    echo "
    </td>
    </tr>";
    echo "</table>";
}

<?PHP
$access = access("update", $men, $a_user["id_m"], $db);

If ($access){
        if (isset($submit)){ 
			require_once("../ssl_library_new/update_content.l");
			
			$a_part["name"] = $name;
			$a_part["descr"] =$descr;
			$a_part["owner"] = $login["id_m"];
//			$a_part["date_expir"] = $to_expire;
			$a_part["sect2"] = $sect2;
			$a_part["upload_name"] = $upload["name"];
//			$a_part["long_descr"] = $long_descr;
			$a_part["id_m"] = $a_user["id_m"];
			$a_part["id_c"] = $id_c;
			$a_part["path"] = $path;
			
			if (empty($upload["name"])){
				// pouze aktualizace
				if (update_content($a_part, $db)) {
					echo "Aktualizace proběhla v pořádku.";
				} else {
					echo "Aktualizace proběhla s CHYBOU.";
				}
				
				
			} else {
				require_once("../ssl_library_new/upload_file.l");
				// uprava i souboru
				preg_match("/\.(.*)\$/", $upload["name"], $part);
				//$suffix = $part[count($part)-1];
				upload_file($upload, $a_part, $db, "update");
			}
			
			
        } else {
			require_once("../ssl_library_new/get_content.l");
			$ret = get_content($db, $men, $a_vypis, "", "", 1, $id_c);
			if (count(@$a_vypis["name"])==1) {
		
    ?> 
<form action="index.php" enctype="multipart/form-data" method="POST"><p><b>Aktualizace dokumentu</b><br>
<input TYPE=hidden name="id_c"  value="<?PHP echo $id_c ?>"> 
<input TYPE=hidden name="path"  value="<?PHP echo  $a_vypis["path"][0] ?>"> 
<input TYPE=hidden name="men"  value="<?PHP echo $men ?>"> 
<input TYPE=hidden name="pg" value="upd"> 
<input TYPE=hidden name=MAX_FILE_SIZE value="10000000"> 
<input TYPE=hidden name="sect2" value="<?PHP echo substr($men,3); ?>"> 

<table cellpadding="0" cellspacing="3" border="0">
<tr>
	<td><p>Název:</p></td>
	<td align="right"><p><input type="text" name="name" size="50" value="<?PHP echo $a_vypis["name"][0] ?>"></p></td>
</tr>
<tr>
	<td><p>Soubor:</p></td>
	<td align="right"><p><input name="upload" size="30" TYPE="file"></p></td>
</tr>
<tr>
	<td align="left" colspan="2"><p style="font-size:10px"><em>Pokud nechcete měnit soubor, ale pouze texty, nechte soubor prázdný.</em></p></td>
</tr>
<tr>
	<td colspan="2"><p>Popis:<br><textarea id="mceNoEditor" cols="53" rows="6" name="descr" class="mceNoEditor"><?PHP echo $a_vypis["descr"][0] ?></textarea></td>
</tr>
<tr>
	<td colspan="2" align="right"><p><input type="submit" name="submit" value=" A k t u a l i z o v a t "></p></td>
</tr>
</table></p></form>
<?PHP
		} else {
			echo "Požadovaný dokument neexistuje!!!";
		}

		}
} else {
?>
<p>&nbsp;</p><p align="center"><font color="Red"><b>Nejste oprávněni pro úpravu dokumentů.</b></font></p>
<?PHP
}
?>
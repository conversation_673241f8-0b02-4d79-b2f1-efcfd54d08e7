<h1>Mensa <PERSON>ko v číslech </h1>
<p>Stránka byla vygenerována na základě aktuálních dat z mensovní databáze <?PHP echo date('d. m. Y v H:i'); ?>.</p>
<?PHP
	/**
		podrobna_statistika.i
		Různé agregované informace o Mense a jejích členech

		Poslední úpravy
		    2012-Oct-06, TK: Doplněno rozložení pohlaví/věk, protože to vyžaduje KVK na dotace.
			2012-Feb-08, <PERSON><PERSON><PERSON>: založeno
	*/


	require_once("../ssl_library_new/database2.class.l");
	$db2 = new database2;
	$db2->open();


	// funkce na výpis tabulky
	require_once("../ssl_library_new/draw_table_from_query.i");


	// debug, nefunguje
	// error_reporting(E_ALL);
?>

<style>
	tr.even
	{
		background-color: #eeeeff;
	}

	table.draw_table_from_query td
	{
		text-align: right;
		padding-right: 0.5em;
	}

	table.draw_table_from_query th
	{
		text-align: center;
	}



</style>



<?PHP $look_back_interval =  3; // o kolik let zpátky budou statistiky ohledně testů ?>
<p>&nbsp;</p>
<h2>Počet administrovaných testů za poslední <?= $look_back_interval ?> roky</h2>
<p>Zahrnuje všechna uskutečněná a do databáze vyplněná testování (ať se již jedná o první, druhé nebo třetí testování dané osoby).</p>
<?PHP
draw_table_from_query ($db2, "testy", "
-- musí tam být union all, jinak vybere jen rozdílné položky (jako by tam bylo distinct)
-- dotaz funguje triviálně, převede údaje o testech 1, 2, 3 na nové řádky se stejně pojmenovnaými sloupci
-- a následně je sloučí a spočítá
SELECT
    rok                         as 'Rok',
    count(typ)                  as 'Celkem',
    concat(sum( if( typ = 1, 1, 0 )), ' (', round(sum( if( typ = 1, 1, 0 ))/count(typ)*100, 1), ' %)') as 'Dospělí',
    concat(sum( if( typ = 2, 1, 0 )), ' (', round(sum( if( typ = 2, 1, 0 ))/count(typ)*100, 1), ' %)') as 'Děti'

FROM
    (
    (SELECT typ, year(datumtestu1) as rok FROM mensasec.c_m_members WHERE year(datumtestu1)> (year(now()) - {$look_back_interval}))
    UNION ALL
    (SELECT typ, year(datumtestu2) as rok FROM mensasec.c_m_members WHERE year(datumtestu2)> (year(now()) - {$look_back_interval}))
    UNION ALL
    (SELECT typ, year(datumtestu3) as rok FROM mensasec.c_m_members WHERE year(datumtestu3)> (year(now()) - {$look_back_interval}))
    UNION ALL
    (SELECT typ, year(mitch_datum_1) as rok FROM mensasec.c_m_members WHERE year(mitch_datum_1)> (year(now()) - {$look_back_interval}))
    UNION ALL
    (SELECT typ, year(mitch_datum_2) as rok FROM mensasec.c_m_members WHERE year(mitch_datum_2)> (year(now()) - {$look_back_interval}))
    UNION ALL
    (SELECT typ, year(mitch_datum_3) as rok FROM mensasec.c_m_members WHERE year(mitch_datum_3)> (year(now()) - {$look_back_interval}))
    ) testy
GROUP BY
    rok
ORDER BY
    rok ASC");

?>





<p>&nbsp;</p>
<h2>Historická data k testování</h2>
<p>Starší čísla z databáze jsou podhodnocená a nesměrodatná, protože některé záznamy se průběžně mažou (údaje dětí),
například, pro rok 2008 je v jiných seznamech celkem 1876 testů.
Následující data nejsou vybrána z databáze, ale pocházejí z jiných zdrojů, převážně záznamů v časopise.</p>
<!--
<table>
<tr><td><b>&nbsp;</b></td><td><b>2006</b></td><td><b>2007</b></td><td><b>2008</b></td></tr>

<tr><td>Berčíková Iva</td><td>-</td><td>-</td><td>32</td></tr>
<tr><td width="200">Blumenstein Tomáš</td><td width="60">16</td><td width="60">100</td><td width="60">109</td></tr>
<tr><td>Fejfarová Helena</td><td>-</td><td>-</td><td>100</td></tr>
<tr><td>Fořtík Václav</td><td>1241</td><td> 729</td><td>1148</td></tr>
<tr><td>Frimlová Martina</td><td>-</td><td>-</td><td>24</td></tr>
<tr><td>Havlíčková Kateřina </td><td>1006</td><td> 314</td><td>-</td></tr>
<tr><td>Havlová Dana</td><td>125</td><td> 147</td><td>161</td></tr>
<tr><td>Herrmann Jan</td><td>-</td><td>-</td><td>12</td></tr>
<tr><td>Hesová Jana</td><td>95</td><td> 259</td><td>57</td></tr>
<tr><td>Hřebíčková Jana</td><td>-</td><td>-</td><td>3</td></tr>
<tr><td>Janovská Pavla</td><td>-</td><td>-</td><td>18</td></tr>
<tr><td>Kalus Čestmír</td><td>-</td><td>-</td><td>4</td></tr>
<tr><td>Kocman Tomáš</td><td>51</td><td> 16</td><td>37</td></tr>
<tr><td>Lebeda Michal</td><td>15</td><td> 5</td><td>3</td></tr>
<tr><td>Mazánek Jan</td><td>28</td><td> 15</td><td>30</td></tr>
<tr><td>Pápolová Mariana</td><td>-</td><td>-</td><td>9</td></tr>
<tr><td>Přibyl Oto</td><td>-</td><td>-</td><td>6</td></tr>
<tr><td>Svobodová Hana</td><td>-</td><td> 81</td><td>82</td></tr>
<tr><td>Šubík Pavel</td><td>-</td><td>-</td><td>7</td></tr>
<tr><td>Walla Jan</td><td>0</td><td>0</td><td width="60">3</td></tr>
<tr><td><b>CELKEM</b></td><td><b>2577</b></td><td><b>1666</b></td><td><b>1876</b></td></tr>
</table>
-->

<p><b>Počet testů celkem:</b><br>
Rok 2004:   3953 (časopis 4/2005)<br>
Rok 2006:   2577 (mensovní statistika)<br>
Rok 2007:   1666 (mensovní statistika)<br>
Rok 2008:   1876 (mensovní statistika)<br>
Rok 2009:   2416 (cdb., získáno 2012-10-06)<br>
Rok 2010:   5222 (cdb., získáno 2012-10-06)<br>
Rok 2011:	4712,	1785 (37.9 %),	2927 (62.1 %) (cdb., získáno 2014-01-26)<br>
Rok 2012:	4480,	1586 (35.4 %),	2894 (64.6 %) (cdb., získáno 2014-01-26)<br>
Rok 2013:	4544,	1775 (39.1 %),	2769 (60.9 %) (cdb., získáno 2014-01-26)<br>
Rok 2014:	4689,	1744 (37.2 %) (cdb., získáno 2019-12-14)<br>
Rok 2015:	4781,	1894 (39.6 %) (cdb., získáno 2019-12-14)<br>
Rok 2016:	8476,	2843 (33.5 %) (cdb., získáno 2019-12-14)<br>
Rok 2017:	7199,	2766 (38.4 %) (cdb., získáno 2019-12-14)<br>
Rok 2018:	6842,	2570 (37.6 %) (cdb., získáno 2019-12-14)<br>
</p>



<p>&nbsp;</p>
<h2>Počet pozitivně otestovaných za poslední <?= $look_back_interval ?> roky</h2>
<p>Statistika zahrnuje všechna uskutečněná testování. <!-- bude uzavřeno po doplnění počtu řad</p> -->

<?PHP

// IQ lze dekryptovat jedině v PHP, je třeba vybrat všechny testy
/*
$sql_iq = "
SELECT
    typ,
    -- ifnull( iq3, ifnull( iq2, iq1)) AS iq,
    iq1 as iq,
   	per1 as per,
    --    year( ifnull( datumtestu3, ifnull( datumtestu2, datumtestu1 ) ) ) AS rok
    year (datumtestu1) as rok
FROM
    mensasec.c_m_members m
WHERE
    -- year( ifnull( datumtestu3, ifnull( datumtestu2, datumtestu1 ) ) ) >= ( year( now( ) ) -5 )
    year (datumtestu1) >= ( year(now()) -5 )
ORDER BY
    rok ASC, typ ASC";
*/

$sql_iq = "
-- logika je stejná jako minule, je potřeba sloučit údaje o třech testováních
-- což je provedeno převedením všech záznamů na řádky s identicky jmenovanými sloupci
-- a jejich následné sloučení
-- je nutné srovnat v SQL, protože PHP už potom nic nerovná
SELECT * FROM
(
    (SELECT
        typ, iq1 as iq,	per1 as per, year (datumtestu1) as rok
    FROM
        mensasec.c_m_members m
    WHERE
        year (datumtestu1) >= ( year(now()) - $look_back_interval))
    UNION ALL

    (SELECT
        typ, iq2 as iq,	per2 as per, year (datumtestu2) as rok
    FROM
        mensasec.c_m_members m
    WHERE
        year (datumtestu2) >= ( year(now()) - $look_back_interval))
    UNION ALL

    (SELECT
        typ, iq3 as iq,	per3 as per, year (datumtestu3) as rok
    FROM
        mensasec.c_m_members m
    WHERE
        year (datumtestu3) >= ( year(now()) - $look_back_interval))
    UNION ALL

    (SELECT
        typ, mitch_iq_1 as iq, 0 as per, year (mitch_datum_1) as rok
    FROM
        mensasec.c_m_members m
    WHERE
        year (mitch_datum_1) >= ( year(now()) - $look_back_interval))
	UNION ALL

    (SELECT
        typ, mitch_iq_2 as iq, 0 as per, year (mitch_datum_2) as rok
    FROM
        mensasec.c_m_members m
    WHERE
        year (mitch_datum_2) >= ( year(now()) - $look_back_interval))
	UNION ALL

    (SELECT
        typ, mitch_iq_3 as iq, 0 as per, year (mitch_datum_3) as rok
    FROM
        mensasec.c_m_members m
    WHERE
        year (mitch_datum_3) >= ( year(now()) - $look_back_interval))
) testy
ORDER BY
    rok ASC";


// dekryptování IQ
$aes_crypt = \crypto\CryptoFactory::getAES();

// proveď query, dekóduj a nasčítej
$sql_qr = $db2->Query($sql_iq);
echo "Počet nalezených záznamů o testování od roku ".(date("Y")-$look_back_interval)." je ".$db2->getNumRows($sql_qr).".</p>";

$vysledek = Array ();
while ($r = $db2->FetchAssoc($sql_qr))
{
    // založ rok, pokud není řádka pro daný rok
    if (!isset($vysledek[(int) $r["rok"]])) $vysledek[(int) $r["rok"]] = Array("otestovani" => 0, "pozitivni" => 0);

    // přišti
    $vysledek[(int) $r["rok"]]["otestovani"] ++;

    // započti IQ
    // protoze tam mohou byt i stringy jako "vice nez 144", tak se pred prevedenim na cislo vyhodi neciselne znaky.
    $iq =  (int) intval(preg_replace("/[^0-9]+/", "", $aes_crypt->decrypt($r["iq"])));
    $per = (int) intval($aes_crypt->decrypt($r["per"]));

	if (
	   ($iq >= 130) or
	   ($per >= 98 and $r['typ'] == 2)
    )
        $vysledek[(int) $r["rok"]]["pozitivni"] ++;
}

echo "<table class='draw_table_from_query'>
<tr><th>Rok</th><th>Testů celkem</th><th>Pozitivních</th><th>Podíl</th><tr>
";
foreach ($vysledek as $rok => $pocty) {
    echo "<tr><td>$rok</td>
        <td class='cislo'>{$pocty['otestovani']}</td>
        <td class='cislo'>{$pocty['pozitivni']}</td>
        <td class='cislo'>" . round($pocty['pozitivni'] / $pocty['otestovani'] * 100, 1) . " %</td>
        </tr>";
}
echo "</table>";

?>



<!-- dava nejake spatne udaje a neni jasne proc
<p>&nbsp;</p>
<h2>Rozdělení pohlaví členů v roce <?PHP echo date('Y'); ?></h2>
<?PHP
 	draw_table_from_query ($db2, "pohlavi", "
		-- dotaz obsahuje mirnou upravu, ktera se snazi kompenzovat zeny s jinym prijmenim, cislo bylo stanoveno od oka, TK
		SELECT
			(sum( if( pohlavi = 'M', 1, 0 ) ) -25	) AS 'Muži',
			concat( round( ( ( 	sum( if( pohlavi = 'M', 1, 0 ) ) -25 ) / count( * ) ) *100, 1), ' %' ) AS '% M',

			(	sum( if( pohlavi = 'Z', 1, 0 ) ) +25 	) AS 'Ženy',
			concat( round( ( (	sum( if( pohlavi = 'Z', 1, 0 ) ) +25 ) / count( * ) ) *100, 1), ' %' ) AS '% Ž',

			count( * ) AS Celkem

			FROM
			(
				SELECT if( right( trim( vstup.prijmeni_c ) , 1 ) = 'á', 'Z', 'M' ) AS pohlavi
				FROM
				(
					SELECT prijmeni AS prijmeni_c
					FROM mensasec.`c_m_platni_clenove`
				)vstup
		)pohl");
?>
<p>Mensa neeviduje pohlaví svých členů, tato statistika vychází z heuristiky založené na faktu, že pokud je posledním písmenem příjmení <strong>á</strong>, jedná se prakticky vždy o ženské jméno. V současné době se však začíjaní vyskytovat i ženy s příjmením končícím na jiné písmeno než á, statistika proto mírně nadhodnocuje poměr mužů. </p>
<p><em>Zdroj dat: centrální databáze, pohled platní členové, sloupec prijmeni</em></p>
-->



<p>&nbsp;</p>
<h2>Rozložení věku členů v roce <?PHP echo date('Y'); ?></h2>
<table cellspacing="5">
<tr>
<td style="vertical-align: top;">
<p><strong>Dle data narození</strong></p>
<?PHP
draw_table_from_query ($db2, "narození", "
	-- Vypis cetnosti let narozeni
	SELECT
		ifnull(CONCAT(rok *10, ' – ', rok *10 + 9), 'neznámá') as 'Dekáda narození',
		count( rok ) as 'Členů'
	FROM
	(
		SELECT floor( year( datumnar ) / 10) AS rok
		FROM mensasec.`c_m_platni_clenove`
		WHERE NOT datumnar IS NULL
	)m
	GROUP BY rok
	ORDER BY rok ASC");
?>
</td>

<td style="vertical-align: top;">
<p><strong>Dle věku</strong></p>
<?PHP
draw_table_from_query ($db2, "vek", "
	-- Vypis cetnosti let narozeni
	SELECT
		ifnull(CONCAT(vek - 5, ' – ', vek + 4), 'neznámý') as 'Věk',
		count( vek ) as 'Členů'
	FROM
	(
		SELECT round( DATEDIFF(now(), datumnar) / 365.25, -1) AS vek
		FROM mensasec.`c_m_platni_clenove`
		WHERE NOT datumnar IS NULL
	)m
	GROUP BY vek
	ORDER BY vek DESC");
?>
</td>


<td style="vertical-align: top;">
<p><strong>Dle věku jinak</strong> (pro úřady)</p>
<?PHP

draw_table_from_query ($db2, "msmt_rozdeleni", "
SELECT
    skupina as 'Věková skupina',
    sum(IF(pohlavi=1, 1, 0)) as 'Muži',
    sum(IF(pohlavi=2, 1, 0)) as 'Ženy'
FROM
(SELECT
    pohlavi,
    CASE
        WHEN vek<=15 THEN '0 až 15 let'
        WHEN vek>15 AND vek <=18 THEN '16 až 18 let'
        WHEN vek>18 AND vek <=26 THEN '19 až 26 let'
        ELSE 'nad 26 let'
    END as skupina
FROM

-- hrubý vstup
-- nejprv odhadni pohlaví a potom spočítej věk
(SELECT
    pohlavi as pohlavi,
    round(DATEDIFF(now(), datumnar)/365.25, 1) as vek
FROM
    mensasec.`c_m_platni_clenove`) vstup) skupiny
GROUP BY
    skupina
ORDER BY
    skupina ASC
");

?>
</td>


<td style="vertical-align: top;">
<p><strong>Dle věku ještě jinak</strong></p>
<?PHP
draw_table_from_query ($db2, "kvk_rozdeleni", "
SELECT
    skupina as 'Věková skupina',
    sum(IF(pohlavi=1, 1, 0)) as 'Muži',
    sum(IF(pohlavi=2, 1, 0)) as 'Ženy'
FROM
(SELECT
    pohlavi,
    CASE
        WHEN vek<=15 THEN 'Žactvo (pod 15)'
        WHEN vek>15 AND vek <=19 THEN 'Mládež (15 až 19)'
        ELSE 'Dospělí'
    END as skupina
FROM

-- hrubý vstup
-- nejprv odhadni pohlaví a potom spočítej věk
(SELECT
    pohlavi as pohlavi,
    round(DATEDIFF(now(), datumnar)/365.25, 1) as vek
FROM
    mensasec.`c_m_platni_clenove`) vstup) skupiny
GROUP BY
    skupina
ORDER BY
    skupina DESC
");
?>
</td>
<td style="vertical-align: top;">
<p><strong>Dle pohlaví</strong></p>
<?PHP
draw_table_from_query ($db2, "pohlavi", "
SELECT
    count(case when pohlavi = 1 then 1 end) as 'Muži',
    count(case when pohlavi = 2 then 1 end) as 'Ženy'
FROM
    mensasec.`c_m_platni_clenove`
");
?>
</td>
</tr>
</table>




<p><strong>Dle data narození:</strong> Statistika seskupuje členy podle roku narození (bez uvažování dne a měsíce) bez poslední číslice, aby vznikla výše uvedená rozmezí. Rok narození členové uvdějí při testování, protože je nezbytný ke správnému vyhodnocení výsledku testu.</p>

<p><strong>Dle věku:</strong> Statistika seskupuje členy podle věku (získaného odečtením data jejich narození od současného data – funkce vrátí počet dnů, který je následně vydělen číslem 365,25 aby byl získán počet let) zaokrouhleného na desítky, aby vznikla výše uvedená rozmezí.</p>

<p><em>Zdroj dat: centrální databáze, pohled platní členové, sloupec datum narození</em></p>






<p>&nbsp;</p>
<h2>Rozdělení typu členů v roce <?PHP echo date('Y'); ?></h2>
<?PHP
draw_table_from_query ($db2, "typy_clenu", "
	SELECT
		(sum( if( typ = 1, 1, 0 ) ) 	) AS 'Dospělí',
		concat( round( ( ( 	sum( if( typ = 1, 1, 0 ) ) ) / count( * ) ) *100, 1), ' %' ) AS '% Dospělí',

		(	sum( if( typ = 2, 1, 0 ) ) 	) AS 'Děti',
		concat( round( ( (	sum( if( typ = 2, 1, 0 ) )) / count( * ) ) *100, 1), ' %' ) AS '% Děti',

		count( * ) AS Celkem

		FROM mensasec.`c_m_platni_clenove`");
?>

<p>Sloupec děti zahrnuje všechny členy Dětské Mensy (podle starých i nových pravidel).</p>
<p><em>Zdroj dat: centrální databáze, pohled platní členové, sloupec typ</em></p>



<p>&nbsp;</p>
<h2>Rozdělení počtů členů po letech k poslednímu dni roku</h2>
<?PHP
draw_table_from_query ($db2, "pocet_clenu", "
		SELECT
            YEAR(`datum`) AS 'Rok',
		    dospeli_pocet AS 'Dospělí',
		    deti_pocet AS 'Děti',
		    sum(dospeli_pocet + deti_pocet) AS 'Celkem'
		FROM `mensaweb`.`m_pocet_clenu`
		WHERE `datum` IN (
		    SELECT MAX(`datum`) FROM `mensaweb`.`m_pocet_clenu` GROUP BY YEAR(`datum`)
		    )
		GROUP BY YEAR(`datum`)
        ORDER BY YEAR(`datum`) DESC");
?>

<p><em>Zdroj dat: centrální databáze, pohled aktuální členové. Data se sbírají od 2021-02-16.</em></p>





<p>&nbsp;</p>
<h2>Rozložení členů dle výše příspěvku</h2>
<table cellspacing="5">
<tr>
<?PHP
	for ($i = date("Y"); $i >= 2012; $i--)
	{
		echo "<td style='vertical-align: top;'><p><strong>Rok $i</strong></p>";
		draw_table_from_query ($db2, "typy_clenu$i", "
			SELECT
				typ as 'Typ členství',
				count(typ) as 'Počet členů',
				concat(round(count(typ)*100/celkem, 1), '&nbsp;%') as '%'
			FROM
			(
				SELECT
					CASE (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0))
						WHEN 0		THEN 'Pouze časopis'
						WHEN 1      THEN 'Doživotní'
						WHEN 2      THEN 'Rodinný'
						WHEN 650    THEN 'Rodinný'
						WHEN 325    THEN 'Rodinný'
						WHEN 150    THEN 'Snížený'
						WHEN 75     THEN 'Snížený'
						WHEN 450    THEN 'Normální'
						WHEN 225    THEN 'Normální'
						ELSE             'Jiný'
					END  as 'typ'
				FROM mensasec.`c_m_platby`
				WHERE rok = $i
			) as typy
			CROSS JOIN
			(
				SELECT count(*) as 'celkem' FROM mensasec.`c_m_platby` WHERE rok = $i
			) as pocet
			GROUP BY typ
			ORDER BY typ");
		echo "</td>";
	}

for ($i = 2011; $i >= 2008; $i--)
	{
		echo "<td style='vertical-align: top;'><p><strong>Rok $i</strong></p>";
		draw_table_from_query ($db2, "typy_clenu$i", "
			SELECT
				typ as 'Typ členství',
				count(typ) as 'Počet členů',
				concat(round(count(typ)*100/celkem, 1), '&nbsp;%') as '%'
			FROM
			(
				SELECT
					CASE (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0))
						WHEN 0		THEN 'Pouze časopis'
						WHEN 1      THEN 'Doživotní'
						WHEN 2      THEN 'Rodinný'
						WHEN 500    THEN 'Rodinný'
						WHEN 250    THEN 'Rodinný'
						WHEN 100    THEN 'Snížený'
						WHEN 50     THEN 'Snížený'
						WHEN 350    THEN 'Normální'
						WHEN 175    THEN 'Normální'
						ELSE             'Jiný'
					END  as 'typ'
				FROM mensasec.`c_m_platby`
				WHERE rok = $i
			) as typy
			CROSS JOIN
			(
				SELECT count(*) as 'celkem' FROM mensasec.`c_m_platby` WHERE rok = $i
			) as pocet
			GROUP BY typ
			ORDER BY typ");
		echo "</td>";
	}
?>
</tr></table>

<p>Protože Mensa v databázi rozlišuje pouze dětské a dospělé členy, je typ členství odvozen z výše plateb. Statistika proto nezahrnuje děti, které vstoupily před koncem roku 2009, protože ty nemusí platit členské příspěvky (mohou však odebírat časopis, a proto se některé objeví v tomto řádku). Řádek <strong>jiný</strong> zahrnuje všechny příspěvky v odlišné výši – někteří členové nepaltí přesně a zaplatí například o něco více. Mezi doživotními členy je vedena také zakladatelka Mensy Dr. Drábková a psycholožka Mensy Dr. Jílková (psycholožka není členem doživotně, avšak v databázi pro tento případ nemáme vhodnou kolonku).</p>
<p><em>Zdroj dat: centrální databáze, tabulka plateb, výše platby</em></p>





<p>&nbsp;</p>
<h2>Počet členů v rámci rodinného příspěvku</h2>
<?PHP
draw_table_from_query($db2, "typy_clenu$i", "
    SELECT
        rok     as 'Rok',
        platici as 'Platící',
        zavisli as 'Závislí',
        platici+zavisli as 'Celkem',
        round(1+ (zavisli/platici), 2) as 'Využití'
    FROM(
    SELECT
        rok,
        sum(
        CASE
            WHEN rok < 2020 THEN
                CASE (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0))
                    WHEN 500 THEN 1
                    WHEN 250 THEN 1
                    WHEN 650 THEN 1
                    WHEN 325 THEN 1
                    ELSE 0
                END
            WHEN rok >= 2020 AND rok < 2024 THEN
                CASE (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0))
                    WHEN 700 THEN 1
                    WHEN 350 THEN 1
                    WHEN 900 THEN 1
                    ELSE 0
                END
            ELSE -- 2024 a dale
                CASE 
                    WHEN (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0)) IN (350, 700, 850, 1000) THEN 1
                    WHEN (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0)) > 1000 AND ((IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0)) - 1000) % 50 = 0 THEN 1
                    ELSE 0
                END
        END) as platici,
        sum(
        CASE (IFNULL(hodnota1, 0) + IFNULL(hodnota2, 0))
            WHEN 2 THEN 1
            ELSE 0
        END) as zavisli
    FROM        mensasec.`c_m_platby`
    WHERE   rok >= 2008
            AND rok <= (YEAR(NOW())+1)
    GROUP BY rok
    ORDER BY rok DESC) vstup");
?>

<p>Sloupec <strong>Platící</strong> udává, kolik členů rodinný příspěvek platí, sloupec <strong>Závislí</strong> udává, kolik dalších členů je registrováno v rámci rodinných příspěvků, sloupec <strong>Využití</strong> pak ukazuje, kolik členů celkem využívá jeden rodinný příspěvek. Typ členství je určován pouze na základě výše platby.</p>
<p><em>Zdroj dat: centrální databáze, tabulka plateb, výše platby</em></p>




















<p>&nbsp;</p>
<h2>Ukončení členství z vlastního rozhodnutí</h2>
<p>Pozor, liší se od záznamu v <a href="/index.php?men=men14.5.0.0">interní finanční statistice</a>, protože ten zahrnuje i výmazy, tato tabulka zahrnuje pouze ukončení.</p>
<?PHP
//echo "<p><strong>Rozložení dle data narození</strong></p>";
draw_table_from_query ($db2, "ukonceni", "
	SELECT
		ukonceni_clenstvi_poplatky as Rok,
		count( ukonceni_clenstvi_poplatky ) as 'Počet členů'
	FROM
		mensasec.`c_m_members`
	WHERE
		ukonceni_clenstvi_poplatky >=2008
	GROUP BY
		ukonceni_clenstvi_poplatky DESC");
?>
<p>Lidé, kteří se rozhodli ukončit členství aktivním rozhodnutím. Nezahrnuje členy, kteří se již nikdy neozvali nebo členy, kteří se rozhodli požádat o vymazání údajů z mensovní databáze. Data evidujeme až od roku 2009.</p>
<p><em>Zdroj dat: centrální databáze, tabulka členů, sloupec ukonceni_clenstvi_poplatky</em></p>













<p>&nbsp;</p>
<h2>Prodleva mezi testováním a vstupem sledovaná po měsících</h2>
<p>Pro každý měsíc sledujeme celkový počet vstupů (dle data zaslání přihlášky - avšak pouze pro lidi, kteří i zaplatili)
a zjišťujeme informace o délce "prodlevy" ve dnech (p.) mezi datem posledního testu a vstupem do Mensy.
Poslední dva sloupce ukázají kolik lidí vstoupilo do půl roku po posledním testu nebo naopak po delší době.</p>

<?PHP
draw_table_from_query ($db2, "rozlozeni_dle_delek_vstupu", "
SELECT
    rok as 'Rok',
    mesic as 'Měsíc',
    pocet_vstupu as 'Vstupů',

    -- rozložení vstupů
    do_pul_roku as 'Do 6 m.',
    nad_pul_roku as 'Nad 6 m.',

    -- analýza prodlev
    min_prodleva as 'Min. p.',
    -- vysekni z retezce udaj presne uprostred (to je medián !!!)
    -- seká se nadvakrát (nejprve vše po, v tom vnějším pak vše před)
    -- ještě se to převede na číslo, protože jinak to je BLOB
    CONVERT(
        SUBSTRING_INDEX(SUBSTRING_INDEX(retezec, ',',   50/100 * pocet_vstupu + 1)
        ,   ',' ,   -1), SIGNED) AS 'Medián',
    prumerna_prodleva as 'Prům. p.',
    max_prodleva as 'Max. p.'

FROM
(
    SELECT
    rok,
    mesic,
    COUNT(prodleva_dny) as pocet_vstupu,
    MIN(prodleva_dny) as min_prodleva,
    round(AVG(prodleva_dny)) as prumerna_prodleva,
    MAX(prodleva_dny) as max_prodleva,
    SUM(if(prodleva_dny <= 180, 1, 0)) AS do_pul_roku,
    SUM(if(prodleva_dny > 180, 1, 0))  AS nad_pul_roku,

    -- udela ze vsech cisle vstupu v jednom mesici jeden serazeny string
    -- to umozni se do toho stringu posleze divat
    GROUP_CONCAT(prodleva_dny ORDER BY prodleva_dny SEPARATOR ',') as retezec

    FROM
    (
        -- vypocti prodlevu od vstupu
        -- a zaroven mesic a rok vstupu
        SELECT
            -- clencislo,
            round(DATEDIFF(vstup, posledni_test)) as prodleva_dny,
            year(vstup) as rok,
            month(vstup) as mesic
        FROM
        (
            SELECT
                clenove.clencislo,
                priznaky.datum_vstupu AS vstup,
                ifnull( datumtestu3, ifnull( datumtestu2, datumtestu1 ) ) posledni_test
            FROM
                mensasec.c_m_members clenove
                LEFT OUTER JOIN mensasec.c_m_priznaky_clenu priznaky ON ( clenove.c_id_m = priznaky.c_id_m )
            WHERE
                year( datumtestu1 ) >= ( year( now() ) -3)
        ) vstup
        WHERE
            year(posledni_test) >=  ( year( now() ) -3)
            AND NOT vstup IS NULL       -- ber jen platne vstupy
            AND NOT clencislo IS NULL   -- ber jen lidi s cislem
    ) delky

    WHERE
        prodleva_dny >= 0
    GROUP BY
        rok, mesic
    ORDER BY
        rok ASC,
        mesic ASC
) retezce");
?>
<p><em>Zdroj dat: centrální databáze, tabulka členů a příznaků členů. Přihlášky se evidují od května 2010.</em></p>












<p>&nbsp;</p>
<h2>První platba v daném roce</h2>
<p>Počet záznamů v databázi členů, ke kterým evidujeme historicky první platbu (členský p. nebo časopis) daný rok. Je to asi nejlepší proxy pro nové vstupy včetně přestupů dětí z DM do M. Pozor, od 2010 se uplatňují nová pravidla pro členy DM, kteří také platí členské - počty vstupů se proto začímají výrazně lišit od statistiky dospělé Mensy v časopise. Pozor, v roce 2013 bylo zrušeno členství v DM podle starých pravidel a všem lidem byly zadány fiktivní platby, což je dalších cca. 100 plateb.</p>
<?PHP
draw_table_from_query ($db2, "prvni_platba", "
SELECT vstup AS Rok, count( c_id_m ) AS 'Nových plateb'
FROM (
    SELECT c_id_m, min( rok ) AS vstup
    FROM mensasec.c_m_platby
    WHERE hodnota1 > 0 or hodnota2 > 0
    GROUP BY c_id_m
)platby
GROUP BY rok-- having rok > 2000
ORDER BY rok ASC");
?>
<p><em>Zdroj dat: centrální databáze, tabulka plateb</em></p>











<p>&nbsp;</p>
<h2>Rozložení vstupů během roku dle dat přihlášek</h2>

<table cellspacing="5">
<tr>
<?PHP
for ($i = date("Y"); $i >= 2011; $i--)
{
	echo "<td style='vertical-align: top;'>";
	echo "<p><strong>Rok {$i}</strong></p>";
	draw_table_from_query ($db2, "histogram_vstupu_$i", "
		-- maly hack jak dostat do tabulky procentualni pocty z celku
		-- tabulka se cross joine s celkovym poctem vstoupivsich lidi (ziskanym zvlastnim dotazem), tim k sobe kazdy radek dostane celkovy pocet
		-- a je mozne spocitat procenta
		-- nevyhodou je, ze klauzule WHERE se opakuje na dvou mistech a musi byt identicka
		SELECT
			month( datum_vstupu ) as 'Měs.',
			count( * ) as 'Přihl.',
			concat(round((count(*)/celkem) *100, 1), ' %') as '%'
		FROM
			mensasec.`c_m_priznaky_clenu` CROSS JOIN
			(
				SELECT count( datum_vstupu ) AS celkem
				FROM mensasec.`c_m_priznaky_clenu`
				WHERE
					NOT month( datum_vstupu ) IS NULL
					AND year( datum_vstupu ) = {$i}
			) m
		WHERE
			NOT month( datum_vstupu ) IS NULL
			AND year( datum_vstupu ) = {$i}
		GROUP BY
			month( datum_vstupu )
		ORDER BY
			month( datum_vstupu ) ASC");
	echo "</td>";
}
?>






<!-- pro grafickou upravu je zarovnan dolu, protoze 2010 nemame cely. -->
<td style="vertical-align: bottom;">
<?PHP
echo "<p><strong>Rok 2010</strong></p>";
draw_table_from_query ($db2, "histogram_vstupu_old", "
    -- maly hack jak dostat do tabulky procentualni pocty z celku
    -- tabulka se cross joine s celkovym poctem vstoupivsich lidi (ziskanym zvlastnim dotazem), tim k sobe kazdy radek dostane celkovy pocet
    -- a je mozne spocitat procenta
    -- nevyhodou je, ze klauzule WHERE se opakuje na dvou mistech a musi byt identicka
	SELECT
		month( datum_vstupu ) as 'Měs.',
		count( * ) as 'Přihl.',
		concat(round((count(*)/celkem) *100, 1), ' %') as '%'
	FROM
		mensasec.`c_m_priznaky_clenu` CROSS JOIN
		(
			SELECT count( datum_vstupu ) AS celkem
			FROM mensasec.`c_m_priznaky_clenu`
			WHERE
				NOT month( datum_vstupu ) IS NULL
				AND year( datum_vstupu ) = 2010
		) m
	WHERE
		NOT month( datum_vstupu ) IS NULL
		AND year( datum_vstupu ) = 2010
	GROUP BY
		month( datum_vstupu )
	ORDER BY
		month( datum_vstupu ) ASC");
?>
</td>
</tr></table>

<p>Agregovaný počet lidí, kteří zaslali přihlášku do Mensy v daném měsíci (vychází z data doručení přihlášky). Informace o datu vstupu se sbírají až od poloviny roku 2010, tato položka byla zavedena spolu s vítáním členů.</p>
<p><em>Zdroj dat: centrální databáze, tabulka příznaků členů, sloupec datum_vstupu</em></p>



<p>&nbsp;</p>
<h2>Rozložení vstupů během roku dle dat plateb zápisného</h2>
<table cellspacing="5">
<tr>
<?PHP
for ($i = date("Y"); $i >= (date("Y") - 4); $i--)
{
	echo "<td style='vertical-align: top;'>";
	echo "<p><strong>Rok " . $i . "</strong></p>";
	draw_table_from_query ($db2, "histogram_vstupu_zapisne$i", "
		-- maly hack jak dostat do tabulky procentualni pocty z celku
		-- tabulka se cross joine s celkovym poctem vstoupivsich lidi (ziskanym zvlastnim dotazem), tim k sobe kazdy radek dostane celkovy pocet
		-- a je mozne spocitat procenta
		-- nevyhodou je, ze klauzule WHERE se opakuje na dvou mistech a musi byt identicka
		SELECT
			month( zapisne_placeno  ) as 'Měs.',
			count( * ) as 'Zápis.',
			concat(round((count(*)/celkem) *100, 1), ' %') as '%'
		FROM
			mensasec.`c_m_members` CROSS JOIN
			(
				SELECT count( zapisne_placeno  ) AS celkem
				FROM mensasec.`c_m_members`
				WHERE
					NOT month( zapisne_placeno  ) IS NULL
					AND year( zapisne_placeno  ) = $i
			) m
		WHERE
			NOT month( zapisne_placeno  ) IS NULL
			AND year( zapisne_placeno  ) = $i
		GROUP BY
			month( zapisne_placeno  )
		ORDER BY
			month( zapisne_placeno  ) ASC");
	echo "</td>";
}


// souhrn
echo "<td style='vertical-align: top;'>";
	echo "<p><strong>Souhrn " . (date("Y")-4) . " – " . (date("Y") - 1) . "</strong></p>";
	draw_table_from_query ($db2, "histogram_vstupu_zapisne_souhrn", "
		SELECT
			month( zapisne_placeno  ) as 'Měs.',
			round(count( * )/4) as 'Zápis./4',
			concat(round((count(*)/celkem) *100, 1), ' %') as '%'
		FROM
			mensasec.`c_m_members` CROSS JOIN
			(
				SELECT count( zapisne_placeno  ) AS celkem
				FROM mensasec.`c_m_members`
				WHERE
					NOT month( zapisne_placeno  ) IS NULL AND
					year( zapisne_placeno ) >= year( now( ) ) - 4 AND
					year( zapisne_placeno ) < year( now( ) )     		 -- nebrat soucasny rok, zkreslilo by statistiku
			) m
		WHERE
		NOT month( zapisne_placeno  ) IS NULL AND
			year( zapisne_placeno ) >= year( now( ) ) - 4 and
			year( zapisne_placeno ) < year( now( ) )     		 -- nebrat soucasny rok, zkreslilo by statistiku
		GROUP BY
			month( zapisne_placeno  )
		ORDER BY
			month( zapisne_placeno  ) ASC");
	echo "</td>";
?>
</tr></table>
<p>Spojení mezi zasláním přihlášky a zaplacením zápisného je u některých členů poněkud rozvolněné, čísla se proto nemusí úplně shodovat.</p>
<p><em>Zdroj dat: centrální databáze, tabulka členů, sloupec zapisne_placeno bez další filtrace</em></p>









<p>&nbsp;</p>
<h2>Rozložení plateb členských příspěvků během roku</h2>
<table cellspacing="5">
<tr>
<?PHP
	for ($i = date("Y"); $i >= (date("Y") - 4); $i--)
	{
		echo "<td style='vertical-align: top;'><p><strong>Rok $i</strong></p>";
		draw_table_from_query ($db2, "histogram_plateb$i", "
			SELECT
				month( datum_prizpevky ) as 'Měs.',
				count( * ) as 'Plateb',
				concat(round((count(*)/celkem) *100, 1), ' %') as '%'
			FROM
				mensasec.`c_m_platby` CROSS JOIN
				(
					SELECT count(*) AS celkem
					FROM mensasec.`c_m_platby`
					WHERE  year( datum_prizpevky ) = $i
				) celkovy_pocet
			WHERE
				year( datum_prizpevky ) = $i
			GROUP BY
				year( datum_prizpevky ) , month( datum_prizpevky )
			ORDER BY
				year( datum_prizpevky ) ASC , month( datum_prizpevky ) ASC");
		echo "</td>";
	}

	echo "<td style='vertical-align: top;'><p><strong>Souhrn " . (date("Y")-4) . " – " . (date("Y") - 1) . "</strong></p>";
	draw_table_from_query ($db2, "histogram_plateb_souhrn", "
		SELECT
			month( datum_prizpevky ) as 'Měs.',
			round(count( * )/4, 0) as 'Plateb/4',
			concat(round((count(*)/celkem) *100, 1), ' %') as '%'
		FROM
			mensasec.`c_m_platby` CROSS JOIN
				(
					SELECT count(*) AS celkem
					FROM mensasec.`c_m_platby`
					WHERE
						year( datum_prizpevky ) >= year( now( ) ) - 4 and
						year( datum_prizpevky ) < year( now( ) )     		 -- nebrat soucasny rok, zkreslilo by statistiku
				) celkovy_pocet
		WHERE
			year( datum_prizpevky ) >= year( now( ) ) - 4 and
			year( datum_prizpevky ) < year( now( ) )     		 -- nebrat soucasny rok, zkreslilo by statistiku
		GROUP BY
			month( datum_prizpevky )
		ORDER BY
			month( datum_prizpevky ) ASC");
	echo "</td>";
?>
</tr></table>

<p>Počet plateb v daném měsíci, zahrnuje všechny platby, včetně fiktivních plateb za rodinné a doživotní členy. Souhrnná tabulka záměrně uvádí počet plateb /4, aby byla hodnota srovnatelná s běžným rokem.</p>
<p><em>Zdroj dat: centrální databáze, tabulka plateb, sloupec datum_prispevky</em></p>










<?PHP

/*
	//sum(amount) over (partition by account order by bookdate) running_total

-- nejdelší jména
SELECT
    cele_jmeno, length (cele_jmeno)
FROM
    (SELECT
        trim(CAST(Binary CONCAT(jmeno, ' ', prijmeni) AS char CHARACTER SET cp1250) collate cp1250_czech_cs) as cele_jmeno
     FROM
        `c_m_platni_clenove`) cm
ORDER BY
    length (cele_jmeno) desc


	Pocet lidi se zajmem o deskove hry

SELECT count(*) FROM `m_members` m join m_zajmy z ON ( z.id_m = m.id_m) WHERE (`zajem` =17 or  `zajem` =18 ) and prizpevky >= 2010


Pocet lidi se zajmem o programovani nebo pracujicich programatoru
select count(*) from

lide kteri maji oboji:
(
SELECT DISTINCT m.id_m
FROM `m_members` m
JOIN m_zajmy z ON ( z.id_m = m.id_m )
WHERE (
`zajem` =23
)
AND prizpevky >=2010
)
UNION DISTINCT (

SELECT DISTINCT id_m
FROM `m_members`
WHERE zamestnani =15
AND prizpevky >=2010
)


    SELECT DISTINCT m.id_m FROM `m_members` m join m_zajmy z ON ( z.id_m = m.id_m) WHERE (`zajem` = 23) and prizpevky >= 2010

    SELECT  DISTINCT id_m FROM `m_members` WHERE zamestnani = 15 and prizpevky >= 2010


Pocet pracujicich programatoru

SELECT count(*) FROM `m_members` WHERE zamestnani = 15 and prizpevky >= 2010


Pracujici podle oboru

SELECT count(*) FROM `m_members` join m_osoba_obory ON ( id_osoby = id_m) WHERE (`id_oboru` =17 or `id_oboru` =16 or `id_oboru` =18 ) and prizpevky >= 2010



Nejvyssi clenske cislo:
SELECT MAX(CONVERT(clen_cislo, SIGNED INTEGER)) as clc FROM m_members WHERE clen_cislo REGEXP '^[0-9][0-9]*$'


*/

?>








<p>&nbsp;</p>
<h2>Emaily platných členů, kteří zorganizovali akci v posledních 2 letech</h2>
<p>Neberou se akce typu testování. </p>
<?PHP
	draw_table_from_query ($db2, "organizatori_akci", "
        SELECT DISTINCT m.email
        FROM mc_akce a
        JOIN m_members m ON ( a.id_owner = m.id_m )
        WHERE year( date_start ) >= (year( now() ) -1)
            AND typ != 'test'
            AND prizpevky >= (year( now() ) -1)
        ORDER BY m.email");
?>
<p><em>Zdroj dat: intranet</em></p>














<p>&nbsp;</p>
<h2>Rozložení délek jmen členů</h2>
<?PHP
	draw_table_from_query ($db2, "delky_jmen", "
		SELECT
			delka as 'Počet znaků',
			count(delka) as 'Výskytů',
			concat(round((count(*)/celkem) *100, 2), ' %') as '%'
		FROM
		(
			SELECT
				length( cele_jmeno ) AS delka
			FROM
			(
				SELECT
					CONCAT( jmeno, ' ', prijmeni) AS cele_jmeno
				FROM
					mensasec.`c_m_platni_clenove`
			) jmena
		)	delky
		-- timto prilepime ke kazdemu radku z predchozi tabulky policko s celkovym poctem clenu (vlozeny dotaz vrati prave jeden radek s jednou hodnotou)
		-- diky tomu bude mozne spocitat procenta
		CROSS JOIN
		(
			SELECT count(*) as celkem FROM mensasec.`c_m_platni_clenove`
		) celkovy_pocet
		GROUP BY
			delka
		Order by
			delka asc");
?>
<p>Bereme délku celého jména včetně mezery mezi jménem a příjmením.<br>
Poznámka: Řádově pár desíek jmen obsahuje titul přímo v kolonce jméno, což statistiku mírně zkresluje.</p>
<p><em>Zdroj dat: centrální databáze, pohled platní členové bez další filtrace</em></p>












<p>&nbsp;</p>
<h2>Četnost posledních písmen příjmení členů</h2>
<p>Vzniklo jako kontrolní pohled pro odhad pohlaví.

</p>
<?PHP
	draw_table_from_query ($db2, "pismena_jmen", "
		-- poslední písmena jmen, histogram
        SELECT
            pohlavi as 'Poslední písmeno',
            count( pohlavi ) AS počet
        FROM (
            SELECT RIGHT( RTRIM( prijmeni ) , 1 ) AS pohlavi
            FROM mensasec.`c_m_platni_clenove`) vstup
        GROUP BY pohlavi
        ORDER BY počet DESC ");
?>
<p><em>Zdroj dat: centrální databáze, pohled platní členové bez další filtrace</em></p>





<p>&nbsp;</p>
<h2>Četnost posledních tří písmen příjmení členů (devatenáct nejčetnějších)</h2>
<p>Vzniklo jako kontrolní pohled pro odhad pohlaví.</p>
<?PHP
	draw_table_from_query ($db2, "pismena_jmen", "
		-- poslední písmena jmen, histogram
        SELECT
            pohlavi as 'Poslední 3 písmena',
            count( pohlavi ) AS počet
        FROM (
            SELECT RIGHT( RTRIM( prijmeni ) , 3 ) AS pohlavi
            FROM mensasec.`c_m_platni_clenove`) vstup
        GROUP BY pohlavi
        ORDER BY počet DESC
        LIMIT 0, 19");
?>
<p><em>Zdroj dat: centrální databáze, pohled platní členové bez další filtrace</em></p>













<p style="text-align:right; margin-top:5em;"><em>Stránku naprogramoval Tomáš Kubeš 8. října 2012.</em></p>


<?PHP
	$db2->Close();
?>

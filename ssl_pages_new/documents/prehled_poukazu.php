<?php

/**
 * Created by IntelliJ IDEA.
 * User: <PERSON>
 * Date: 13.02.16
 * Time: 15:06
 */

?>

<h3 style='margin-top: 3em'><PERSON><PERSON><PERSON><PERSON> vydan<PERSON>ch poukazů na test</h3>
<p>Sloupec změna obsahuje buď čas vytvoření kuponu nebo čas, kdy se obdarovaný přihlásil na testování.</p>
<style type="text/css">
#poukazy td {
    padding-right: 1em;
}
</style>

<?php
require_once("../ssl_library_new/draw_table_from_query.i"); // funkce na výpis tabulky

$query = "
  SELECT
    t.Nazev AS 'Typ',
    CONCAT(m.jmeno, ' ', m.prijmeni, ' (', m.clen_cislo, ')') as 'Daruj<PERSON><PERSON><PERSON>',
    CONCAT(p.`jmeno_obdarovaneho`, ' ', p.`prijmeni_obdar<PERSON>neho`) as '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    CASE WHEN `id_prihlasky` IS NULL THEN 'Nepř<PERSON>l<PERSON>ý' ELSE 'Přihlášený' END as '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    CASE WHEN `id_prihlasky` IS NULL THEN p.email_pro_zaslani_kodu ELSE '-' END as 'E-mail',
    if(p.typ >= 99000, p.typ - 99000, 'zdarma') AS Cena,
    kod AS 'Kód',
    IF(je_pouzity = 1, 'Ano', 'Ne') as 'Uplatněn pro testování',
    DATE_FORMAT(prihlasit_do, '%d.%m.%Y') as 'Platnost do',
    DATE_FORMAT(zmena, '%d.%m.%Y %H:%i:%s') as 'Poslední změna'
  FROM
    `m_poukazy_na_test` p
    LEFT JOIN m_members m
    ON (p.id_m = m.id_m)
    LEFT OUTER JOIN m_typy_poukazu_na_test t ON (p.typ = t.typ)
  WHERE
      p.prihlasit_do > (date_add(now(), INTERVAL -2 YEAR))
    /* typ = 2017 -- pouze za aktualni rok urceno typem prukazu, neni treba, v tabulce jine byt nemuzou. */
  ORDER BY
    CASE WHEN `id_prihlasky` IS NULL THEN 0 ELSE 1 END, p.`prijmeni_obdarovaneho`, p.`jmeno_obdarovaneho`
 ";

$pocet = draw_table_from_query($db, "poukazy", $query);

echo "<p style='margin-top: 2em;'>$pocet záznamů</p>";
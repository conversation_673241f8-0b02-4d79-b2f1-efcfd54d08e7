<h1>Maintenance Screen for Photo Gallery</h1>
<p>For admins only, use with care!</p> 
 
 
 <h2>Exporting thumbnails to HDD</h2>
 
 <?php
    //all purpose utilities
    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");
    require_once ("../ssl_pages_new/photogallery2/include/constants.inc.php");

	///////////////////////////////////////////////////
	//saves one thumbnail to HDD
	function export_one_thumbnail ($pic_id, &$pic_data)
	{
		//paranoic check, do not do anything if fileds are empty
		if (!$pic_id or !$pic_data) return FALSE;
	
	    //generate picture file name
    	$file_name = THUMBNAIL_PATH . ((string) $pic_id) . ".jpg";

	    //check if the file exists
    	if (file_exists ($file_name))
	   	{
    		print_line ("<b>File $file_name already exists! Cannot create a file with same id!</b><br>", 2);
        	return FALSE;
	    }
		
        //open target file (for bionary writing)
		$target_file = fopen($file_name, "wb");
        if (!$target_file)
        {
            print_line ("<b>Cannot create a file $file_name!</b><br>", 2);
            return FALSE;
        }
		
        //write in the contant of the file - db should store complete JPG
        //image as a file image
        $result = fwrite ($target_file, $pic_data);

        fclose($target_file);

        return $result;
    }		
	


	///////////////////////////////////////////////////
	//saves on thumbnail and prints all messages	
	function process_thumbnail (&$db_row)
	{
		print_line("<li><p>",1);
		print_line("Exporting file id. " . $db_row[0] . ".<br>",2);	
		$result = export_one_thumbnail ($db_row[0], $db_row[1]);
		if ($result)
		{
			print_line("<b>Success</b>, exported $result bytes.",2);
			print_line("</p></li>",1);	
			return TRUE;
		}
		else
		{
			print_line("<b>Failed!</b>",2);
			print_line("</p></li>",1);	
			return FALSE;
		}
	}
	
	
	
  	////////////////////////////////////////////////////////////////////////////
  	////////////////////////////////////////////////////////////////////////////
  	////////////////////////////////////////////////////////////////////////////
	//Export thumbnails
 
 	//select all thumbnails and their data
 	$query = "SELECT picture_id, image_thumbnail FROM m_fot_Pictures ORDER BY picture_id ASC;";

	//execute
    $queryid = $db->Query($query);

	//get count
	print_line ("<h3>Exporting " . db_num_rows($queryid) . " thumbnails.</h3>");
	 
	//go through all thumbnails
	//not needed - finished
	/*
	print_line ("<ul>");
 	while ($db_row = db_fetch_array($queryid))
	{
		process_thumbnail ($db_row);
	}
	print_line ("</ul>");
	*/


	print_line ("<h3>Finished!</h3>");
?>
<?php
 //basic constants and print line function
  		require_once ("../ssl_pages_new/photogallery2/include/constants.inc.php");
		    //all purpose utilities
	    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");
		
    //stup to fake post data...
    //fake get data - if no other data were detected
    if (!isset($_GET['action']) AND !isset($_GET['album_id'])) $_GET['action']='new';
    if (!isset($_GET['action'])) $_GET['action']='edit';
    require ("../ssl_pages_new/photogallery2/album_modify.php");

?>

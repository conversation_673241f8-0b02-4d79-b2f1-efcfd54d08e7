<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
            saves uploaded pictures to database, generate description forms

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

    //all purpose utilities
    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");

    //outputs html page header
    //function output_header ($rights = AC_PRIVATE, $page_title = "", $page_description = "", $key_words = "")
    //output_header (AC_PRIVATE, "Nahrát fotografie - Dokončeno");


    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    //post data must be passed as copy!!! for each will not work otherwise!!!!!!!!
    function save_changes_for_one_picture (&$db, $file_id, $post_data)
    {
        //store values relevent for our picture
        $values = array ();

        //set default
        $values['public'] = "";

        //decode all passed data
        foreach ($post_data as $name => $value)
        {
            //split name to name - id
            //string strtok ( string str, string token )
            $type = strtok ($name, '-');
            $id   = strtok ('-');

            if ($file_id == $id)
            {
                //echo "found $type = $value for $file_id <br />";
                //keep this value - and make sure it is secure
                //$values[$type] = check_user_input($value);
                $values[$type] = $value;
            }
        }

        //decode public
        $pub = (($values['public']=='on')?"1":"0");

        //now run the query
        $query = "UPDATE m_fot_Pictures SET public='$pub' , name='" . trim(check_user_input($values['name'],50)) . "' , comment='" . trim(check_user_input($values['comment'],255)) . "' WHERE picture_id='$file_id';";
        //echo "$query";

        //execute
        $db->Query($query);

        //result
        $result = db_affected_rows();

        //fatal error?!
        if ($result > 1) die;

        //oterwise return - 1 row = OK, 0 faliure
        return $result;
    }

    ////////////////////////////////////////////////////////////////////////////

    function save_changes_for_pictures (&$db, &$post_data)
    {
        //scan values for picture-1 ... picture-5 - these hold key to pic ids
        foreach ($post_data as $name => $value)
        {
            if (strtok ($name, '-') == "picture")
            {
                $res = save_changes_for_one_picture ($db, $value, $post_data);
                if ($res)
                {
                    print_line ("<p class='message'>Popis fotografie číslo $value byl úspěšně uložen.</p>");
                }else
                {
                    print_line ("<p class='message'>Chyba při ukládání popisů k fotografii číslo $value.</p>");
                }
            }
        }

    }



?>



<div id='photo_gallery'>

<!--<h1 class='page_title'>Nahrát fotografie</h1>-->
<h1 class='page_sub_title'>Krok 3: Uložení změn</h1>

<?php
//print_r ($_POST);

//processes post data and update image rows
save_changes_for_pictures ($db, $_POST);

?>

<h2>Další kroky</h2>
<ul class='m_fot_links'>
    <?php
        $album_id = (int) $_POST['target_album_id'];
        print_line ("<li><a href='./index.php?men=men23.1.0.0&amp;album_id=$album_id'>nahrát další fotografie</a></li>",1);
        //https://intranet.mensa.cz/index.php?men=men23.2.0.0&album_id=8
        print_line ("<li><a href='./index.php?men=men23.2.0.0&amp;album_id=$album_id'>upravit toto album</a></li>",1);
        print_line ("<li><a href='./index.php?men=men23.4.0.0&amp;album_id=$album_id'>prohlédnout toto album</a></li>",1);
        print_line ("<li><a href='./index.php?men=men23.4.0.0&amp;owner=me'>přejít na seznam mých alb</a></li>",1);
    ?>
    <li><a href='./index.php?men=men23.4.0.0'>přejít na seznam alb</a></li>
    <li><a href="./index.php?men=men23.0.0.0">zpět na fotogalerii</a></li>
</ul>


</div>



<?php

    //prints footer, closes the page and stops scrip execution
    //output_footer ();

?>

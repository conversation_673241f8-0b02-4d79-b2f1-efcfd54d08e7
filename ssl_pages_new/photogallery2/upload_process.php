<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
            saves uploaded pictures to database, generate description forms

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////

    Změnovník:

    2021-11-09, HK: Přidána oprava rotace obrázku otočeného jen EXIFem

    ////////////////////////////////////////////////////////////////////////////
    */

    //all purpose utilities
    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");

    //read required low level data
    $config_array = parse_ini_file (INI_PATH, TRUE);

    //define temp dir (from ini file!)
    define ("TMP_PATH", $config_array['system']['temporary_directory']);
    @define ("THUMB_X", $config_array['thumbnails']['width']);
    @define ("THUMB_Y", $config_array['thumbnails']['height']);
    define ("THUMB_Q", $config_array['thumbnails']['quality']);
    define ("PIC_X", $config_array['photographs']['max_width']);
    define ("PIC_Y", $config_array['photographs']['max_height']);
    define ("PIC_Q", $config_array['photographs']['quality']);

	  define ("UPLOAD_DEBUG", FALSE);
	
    //outputs html page header
    //function output_header ($rights = AC_PRIVATE, $page_title = "", $page_description = "", $key_words = "")
    //output_header (AC_PRIVATE, "Nahrát fotografie - Popis fotografií", "popsat právě nahrané fotografie");




////////////////////////////////////////////////////////////////////////////////
//calls move_uploaded_file and does other checks
//parameters part of file array, tmp directory for file, desired new name of the file
function move_file_to_temp ($file_array, $tmp_directory, $new_name)
{
    //get file from upload temp to our temp
    $file_name = $tmp_directory . $new_name . ".jpg";
    $upload_ok = move_uploaded_file($file_array['tmp_name'], $file_name);
    if ($upload_ok)
    {
        //user message
        //echo ("<p>Uploaded file: " . $file_array['name'] . ", size: " . $file_array['size'] . " as: $file_name</p>");
 		if (UPLOAD_DEBUG) echo "<!-- DEBUG: move_file_to_temp (" . print_r($file_array, true) . ", $tmp_directory, $new_name) succeeded. -->\n";
        return $file_name;
    } else
    {
        echo ("<h2>Chyba: Nezdařilo se otevřít nahrávaný soubor!</h2>");
        echo ("<p>" . $file_array['name'] . " - " . UPLOAD_ERRORS[$file_array['error']] . "</p>" );
        return FALSE;
    }
}



////////////////////////////////////////////////////////////////////////////////
//check if type is correct
// returns false or true
function check_uploaded_image_file ($file_name, $user_name)
{
    //check image properties
    //list($width, $height, $type, $attr) = getimagesize("img/flag.jpg");
    //Index 2 is a flag indicating the type of the image: 1 = GIF, 2 = JPG, 3 = PNG, 4 = SWF, 5 = PSD, 6 = BMP
    $img_prop = getimagesize($file_name);
    if ($img_prop[2] != 2)
    {
        echo "<p>" . $file_array['name'] . " není jepeg obrázek!</p>";
        return FALSE;
    }

    print_line ("<p class='message'>Byl nahrán soubor $user_name, formát jpeg, velikost $img_prop[0] x $img_prop[1] pixelů.</p>");

    return TRUE;
}


////////////////////////////////////////////////////////////////////////////////
//prepares thumbnail jpeg string ready to be saved to database
//returns false on error
function generate_thumbnail ($pic_id, &$image_resource)
{

	if (UPLOAD_DEBUG) echo "<!-- DEBUG: generate_thumbnail ($pic_id, image_resource) starting. -->\n";

    $image_x = imagesx ( $image_resource);
    $image_y = imagesy ( $image_resource);

    //create canvas for thmubnail (defined size)
    $thumbnail_resource = imagecreatetruecolor(THUMB_X, THUMB_Y);

    //calculate size to copy from source image
    //depend on aspect ratios if we start from x or y
    $source_aspect_r = $image_x / $image_y;
    $thumb_aspect_r = THUMB_X / THUMB_Y;

    if ($source_aspect_r > $thumb_aspect_r)
    {
        //source image is wider than thumbnail - we will need to crop image on x axis
        //calculate resize from y
        $percent = $image_y / THUMB_Y;

        //size of x region to copy (y will be copied 100%) - round down
        $new_x = (int) floor (THUMB_X * $percent);

        //calculate x offset (round down)
        $x_offset =  (int) floor(($image_x - $new_x) / 2);

        //copy and resize
        //bool imagecopyresized ( resource dst_image, resource src_image, int dst_x, int dst_y, int src_x, int src_y, int dst_w, int dst_h, int src_w, int src_h )
        imagecopyresized ($thumbnail_resource, $image_resource,
            0, 0, $x_offset, 0, THUMB_X, THUMB_Y, $new_x, $image_y);


    } else
    {
        //source image is leaner than thumbnail - we will need to crop image on y axis
        //calculate resize from x
        $percent = $image_x / THUMB_X;

        //size of y region to copy (x will be copied 100%) - round down
        $new_y = (int) floor (THUMB_Y * $percent);

        //calculate y offset (round down)
        $y_offset =  (int) floor(($image_y - $new_y) / 2);

        //copy and resize
        imagecopyresized ($thumbnail_resource, $image_resource,
            0, 0, 0, $y_offset, THUMB_X, THUMB_Y, $image_x, $new_y);

    }

	////////////////////////
    //now save the thumbnail

    //progressive jpeg
    //If the interlace bit is set and the image is used as a JPEG image, the image is created as a progressive JPEG.
    imageinterlace($thumbnail_resource, 1);


  	////////////////////////////////////////////////////////////////////////////
 	//save thumbnail to HDD
    //generate picture file name
    $file_name = THUMBNAIL_PATH . ((string) $pic_id) . ".jpg";

    //check if the file exists
    if (file_exists ($file_name))
   	{
    	print_line ("<h3>Thumbnail file of this name already exists! Cannot create a file with same id!</h3>");
	    imagedestroy ($thumbnail_resource);
        return FALSE;
    }

    //bool imagejpeg ( resource image [, string filename [, int quality]] )
    //imagejpeg() creates the JPEG file in filename from the image image. The image argument is the return from the imagecreatetruecolor() function.
    $file_write_result = imagejpeg ($thumbnail_resource, $file_name, THUMB_Q);
	if (!$file_write_result)
	{
	 	if(UPLOAD_DEBUG) echo "<!-- DEBUG: generate_thumbnail () saving file $file_name to HDD FAILED. -->\n";
	    imagedestroy ($thumbnail_resource);
		return FALSE;
	}

    //clean
    imagedestroy ($thumbnail_resource);

    //retun thumbnail
    return TRUE;
}





////////////////////////////////////////////////////////////////////////////////
//generates iamge to be stored in db (with max size allowd in ini file
function generate_full_size_image ($pic_id, &$image_resource)
{
	if (UPLOAD_DEBUG) echo "<!-- DEBUG: generate_full_size_image ($pic_id, image_resource) starting. -->\n";

    $image_x = imagesx ($image_resource);
    $image_y = imagesy ($image_resource);

    //determine ratio in which to shrink image in both dimensions
    $x_ratio = $image_x / PIC_X;
    $y_ratio = $image_y / PIC_Y;

    //choose bigger one - shrink so that the longer dimension will fit!
    $ratio = ($x_ratio > $y_ratio)?$x_ratio:$y_ratio;

    //(do not upscale the image if it is smaller)
    $ratio = ($ratio < 1)?1:$ratio;

    //calculate new sizes
    $new_x = (int) floor ($image_x / $ratio);
    $new_y = (int) floor ($image_y / $ratio);

    //now save the the image
    //create canvas for new image
    $photo_resource = imagecreatetruecolor($new_x, $new_y);

    //copy image
    imagecopyresized ($photo_resource, $image_resource, 0, 0, 0, 0, $new_x, $new_y, $image_x, $image_y);

    //progressive jpeg type
    imageinterlace($photo_resource, 1);


  	////////////////////////////////////////////////////////////////////////////
 	//save file to HDD
    //generate picture file name
    $file_name = PHOTO_PATH . ((string) $pic_id) . ".jpg";
	if (UPLOAD_DEBUG) echo "<!-- DEBUG: generate_full_size_image ($pic_id, image_resource) saving file name $file_name to HDD. -->\n";

    //check if the file exists
    if (file_exists ($file_name))
   	{
    	print_line ("<h3>File of this name already exists! Cannot create a file with same id!</h3>");
        return FALSE;
    }

    $file_write_result = imagejpeg ($photo_resource, $file_name, PIC_Q);
	if (!$file_write_result && UPLOAD_DEBUG) echo "<!-- DEBUG: generate_full_size_image ($pic_id, image_resource) saving file $file_name to HDD FAILED. -->\n";

	/////////////////////////////////////////////////////////////////////////
    //clean canvas
    imagedestroy ($photo_resource);

    //do not return, rather save the image to HDD directly
    //return addslashes($photo_jpeg);
    //returns the result of saving
    return $file_write_result;
}


////////////////////////////////////////////////////////////////////////////////
//this function retrieves picture taken time from exif or file modification time
function get_image_time ($file_path)
{
	if (UPLOAD_DEBUG) echo "<!-- DEBUG: get_image_time ($file_path): getting file time. -->\n";

    //try to read exif
    //array exif_read_data ( string filename [, string sections [, bool arrays [, bool thumbnail]]] )
    $exif = exif_read_data ($file_path, 'FILE', TRUE, FALSE);

    //if there was no success, abort
    if (!$exif)
    {
        //input for  TIMESTAMP column 'YYYY-MM-DD HH:MM:SS'
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: get_image_time ($file_path): no EXIF found. -->\n";
        return date ("Y-m-d H:i:s", filemtime($file_path));
    }

    //print_r($exif);

    //check if file section exists
    if (!array_key_exists ("FILE", $exif))
    {
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: get_image_time ($file_path): no file section in EXIF found. -->\n";
        return date ("Y-m-d H:i:s", filemtime($file_path));
    }

    //if found make it root
    $exif = $exif["FILE"];

    //print_r($exif);

    //check if datetime key exists
    if (!array_key_exists ('FileDateTime', $exif))
    {
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: get_image_time ($file_path): no FileDateTime key found. -->\n";
        return date ("Y-m-d H:i:s", filemtime($file_path));
    }
    //echo $exif["FileDateTime"];

    //if we got here, the key exists and should contain unix timestamp
    //echo date ("Y-m-d H:i:s", $exif["FileDateTime"]);

    return date ("Y-m-d H:i:s", $exif["FileDateTime"]);
}

///////////////////////////////////////////////////////////////////////////////////////
//fixes image rotation (physically rotates it if orientation specified only by exif)
function fix_image_rotation (&$image, $filename)
{
    $exif = exif_read_data($filename);
    $orientation = $exif['Orientation'] ?? 0;
    $rotations = [0,0,0,180,180,-90,-90,90,90];
    $flips = [0,0,1,0,1,1,0,1,0];

    if (!empty($orientation)) {
        if (UPLOAD_DEBUG) echo "<!-- DEBUG: Fixing EXIF orientation " . $exif['Orientation'] . "-->\n";

        if ($rotations[$orientation])
            $image = imagerotate($image, $rotations[$orientation], 0);

        if ($flips[$orientation])
            imageflip($image, IMG_FLIP_HORIZONTAL);
    }
}

////////////////////////////////////////////////////////////////////////////////
//completely saves one image to database doing all required actions
//image state will be raw (user need to assing name and comment)
//return image id i ndb
function save_the_image_to_db (&$db, $file_name, $given_name, $target_album)
{
    //read the image
    $image = imagecreatefromjpeg($file_name);

    //test for error
    if (!$image)
    {
        //unable to open
        echo ("<h3>Otevírání souboru $file_name (" . $file_array['name'] . ") se nezdařilo!</h3>");
        return FALSE;
    }

    fix_image_rotation($image, $file_name);

    //make sure that spoiled file name will not cause sql injection...
    $given_name = trim(check_user_input ($given_name, 50));
    $target_album = (int) $target_album;

    //get modification time in form of strring
    $time = get_image_time ($file_name);

    //get public attribute - depends on target album
    $public = (string) db_one_field_query ($db, "SELECT public FROM m_fot_Albums WHERE album_id=$target_album;");

    /*
        CREATE TABLE `Pictures` (
            `picture_id` mediumint(8) unsigned NOT NULL auto_increment,
            `public` tinyint(1) NOT NULL default '0',
            `name` varchar(50) collate latin2_czech_cs default 'fotka',
            `comment` varchar(255) collate latin2_czech_cs default NULL,
            `created` datetime NOT NULL default '0000-00-00 00:00:00',
            `image_data` mediumblob NOT NULL,
            `image_thumbnail` blob NOT NULL,
            `views` mediumint(8) unsigned NOT NULL default '0',
            `belongs_to_album_id` smallint(5) unsigned NOT NULL default '0',
            PRIMARY KEY  (`picture_id`),
            KEY `FK_Picture_1` (`belongs_to_album_id`)
        ) ENGINE=MyISAM DEFAULT CHARSET=latin2 COLLATE=latin2_czech_cs AUTO_INCREMENT=1 ;
    */

    //prepare
    //null picture_id triggers autoincrement feature
    //picture size stored incorrectly
    $query =    "INSERT INTO m_fot_Pictures
                (picture_id,  public,         name, created, views, belongs_to_album_id)
                VALUES
                (      NULL, $public,'$given_name', '$time',     0,       $target_album);";


    //execute
    $db->Query($query);

    //result
    $result_db = db_affected_rows();

    //problem occured!
    if($result_db != 1)
	{
        echo "<h3>Bohužel, nepodařilo se uložit obrázek do databáze!</h3>";
        echo "<p>Jedná se o interní chybu, prosím, kontaktujte správce systému: <EMAIL>.</p>";
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: save_the_image_to_db (db, $file_name, $given_name, $target_album) inserting into a database failed! -->\n";
		return FALSE;
	}

    //get value of last auto increment key (our image id)
    $image_id = db_insert_id();
	if (UPLOAD_DEBUG) echo "<!-- DEBUG: save_the_image_to_db (db, $file_name, $given_name, $target_album) DB insert succeeded, id. is $image_id! -->\n";

	//save thumbnail to HDD
	$result_hdd = generate_thumbnail ($image_id, $image);
    if ($result_hdd == FALSE)
	{
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: save_the_image_to_db (db, $file_name, $given_name, $target_album) saving thumbnail to HDD FAILED! -->\n";
		return FALSE;
	}

    //now we can save image file to HDD
	if (UPLOAD_DEBUG) echo "<!-- DEBUG: save_the_image_to_db (db, $file_name, $given_name, $target_album) saving to HDD. -->\n";
    $result_hdd = generate_full_size_image ($image_id, $image);
    if ($result_hdd == FALSE)
	{
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: save_the_image_to_db (db, $file_name, $given_name, $target_album) saving to HDD FAILED! -->\n";
		return FALSE;
	}

    //clear memory
    imagedestroy ($image);

	if (UPLOAD_DEBUG) echo "<!-- DEBUG: save_the_image_to_db (db, $file_name, $given_name, $target_album) succeeded, image id is $image_id. -->\n";
    return $image_id;
}



////////////////////////////////////////////////////////////////////////////////
//process one file - return the id of new image in database
//input parameter: part of $_FILES array for one file
/*
    The contents of $_FILES from the example form is as follows. Note that this assumes the use of the file upload name userfile, as used in the example script above. This can be any name.

    $_FILES['userfile']['name']
    The original name of the file on the client machine.

    $_FILES['userfile']['type']
    The mime type of the file, if the browser provided this information. An example would be "image/gif". This mime type is however not checked on the PHP side and therefore don't take its value for granted.

    $_FILES['userfile']['size']
    The size, in bytes, of the uploaded file.

    $_FILES['userfile']['tmp_name']
    The temporary filename of the file in which the uploaded file was stored on the server.

    $_FILES['userfile']['error']
*/
function process_one_file (&$db, $file_array, $tmp_dir, $number, $target_album)
{
	if (UPLOAD_DEBUG) echo "<!-- DEBUG: process_one_file (db, " . print_r($file_array, true) . ", $tmp_dir, $number, $target_album) starting. -->\n";

    //at first, check if this file window was used
    if ($file_array['name'] == "")
    {
        //input field was blank
        return FALSE;
    }

    //move the file from uplod destination to our temp (check if it was uploaded correctly)
    $file_name = move_file_to_temp ($file_array, $tmp_dir, $number);
    if ($file_name == FALSE) return FALSE;


    //check file type
    $result = check_uploaded_image_file ($file_name, $file_array['name']);
    if (!$result) return FALSE;

    //completely saves one image to database (generates thumbnail etc)
    //returns tmp image name
    return save_the_image_to_db ($db, $file_name, $file_array['name'], $target_album);
}








////////////////////////////////////////////////////////////////////////////////
//prepares unique temporary directory
//parameters: folder with rwx permissions (optional number of retries)
function prepare_tmp_dir ($tmp_path, $tries = 5)
{
    //create random number as string
    $base_path = $tmp_path . sprintf ("%d", mt_rand(1000, 9999)) . "/";

    //at first, check if such directory exists
    if (is_dir($base_path))
    {
        //try more if possible
        if ($tries > 0)
        {
            return prepare_tmp_dir ($tmp_path, --$tries);
        } else
        {
            //in other case exit
            echo "<h3>Dočasný adresář již existuje!</h3>";
            echo "<p>Pokud tuto hlášku vidíte poprvé, prosím zkuste obrázek nahrát znovu. Pokud se problém opakuje, kontaktujte správce systému.</p>";
            die;
        }
    }

    //we have unique path
    //ok, it is not atomic, so technically problem can rarely occur

    //create directory structure
    //php 4 does not know recursive attribut for mkdir
    $success = mkdir ($base_path, 0777);

    if (!$success)
    {
        echo "<h3>Nepodařilo se připravit dočasný adresář: $base_path !</h3>";
        die;
    }

 	if (UPLOAD_DEBUG) echo "<!-- DEBUG: prepare_tmp_dir ($tmp_path, $tries): Created temp directory $base_path. -->\n";
    return $base_path;
}

////////////////////////////////////////////////////////////////////////////////
//removes target directory (with all files) (does not expect other directories in it)
//no security checks!
function remove_temp ($tmp_dir)
{
 	if (UPLOAD_DEBUG) echo "<!-- DEBUG: remove_temp ($tmp_dir): Cleaning and removing the directory. -->\n";

    //Opens up a directory handle to be used in subsequent closedir(), readdir(), and rewinddir() calls.
    $dir =  opendir ($tmp_dir);

    //go through all files
    while (($file_name = readdir ($dir)) !== FALSE)
    {
        $full_name = $tmp_dir . $file_name;
        //delete only files (function returns also . and .. !!!
        if (!is_file ($full_name)) continue;

        //echo "$full_name";
        //delete the file
        unlink ($full_name);
    }

    //finally remove our temporary directory
    rmdir  ($tmp_dir);

    return TRUE;
}


////////////////////////////////////////////////////////////////////////////////
//proces all files
//input parameters: $_FILES
//returns - array with ids of all processed images
function process_files (&$db, &$files, $target_album)
{
    //prepares temporary directory for uploads
    $tmp_dir = prepare_tmp_dir (TMP_PATH);

    //process all uploaded files
    $file_number = 1;

    //prepare  array for stored image db ids
    $image_ids = array();

    foreach ($files as $userfile => $file_info)
    {
        //retrieve id
		if (UPLOAD_DEBUG) echo "<!-- DEBUG: process_files (db, files, $target_album): processing file $userfile. -->\n";
        $img_id = process_one_file ($db, $file_info, $tmp_dir, $file_number, $target_album);
        if ($img_id != FALSE)
        {
            //append id
            $image_ids[] = $img_id;
        }
		else
		{
			//just debug
			if (UPLOAD_DEBUG) echo "<!-- DEBUG: process_files (db, files, $target_album): file upload $userfile failed - returned FALSE. -->\n";
		}

        $file_number ++;
    }

    //clean tmp files
    remove_temp ($tmp_dir);

    return $image_ids;
}


////////////////////////////////////////////////////////////////////////////////
//moved to general.inc.php
//function generate_image_description_form ($image_id, $picture_number)


////////////////////////////////////////////////////////////////////////////////
//generate forms with fields for all uploaded images
function generate_image_property_form (&$db, $image_ids, $target_album)
{
    global $user_id;

    //action='./index.php?men=men23.1.0.0&amp;action=process'
    print_line ("<form id='picture_properties' method='post' action='./index.php?men=men23.1.0.0&amp;action=save'>",1);

    //dispaly part for every image
    $order = 1;
    foreach ($image_ids as $image)
    {
        //create all form entries and displays thumbnail
        generate_image_description_form ($db, $image, $order++, $target_album, $user_id, THUMB_X, THUMB_Y);
    }


    //save target album - will be used for link
    print_line ("<input type='hidden' id='target_album_id' name='target_album_id' value='$target_album' />",2);

    //<center> </center>
    print_line ("<input type='submit' name='submit' value='Uložit' />",2);
    print_line ("</form>",1);

}


////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////






//get target album (force conversion to ptrevent malicious data)
$target_album = (int) $_POST['album_id'];

//test if it belongs to current user
$album_owner = db_one_field_query ($db, "SELECT owner_id FROM m_fot_Albums WHERE album_id='". ((string)$target_album) . "';");

//if there is a problem, display message and die
if ($album_owner != $user_id)
{
    print_line("<h1>Nejste majitelem cílového alba!</h1>");
    exit;
    //output_footer ();
}

print_line("<div id='photo_gallery'>");

//print_line ("<h1 class='page_title'>Nahrát fotografie</h1>");
print_line ("<h1 class='page_sub_title'>Krok 2: Úprava fotografií</h1>");


print_line ("<h2>Zpracovávám Vaše soubory...</h2>",1);

//read uploaded file, save them to database
//returns an array with names of files saved (its is the db field name)
//names should be generated quite uniquely
$image_ids = process_files ($db, $_FILES, $target_album);

if ($image_ids)
{
	print_line ("<h2>Prosím zadejte názvy a popisy Vašich fotografií.</h2>",1);
	//print_line ("<p>Poznámka: Náhledy, které vidíte jsou výřezy, originální fotografie samozřejmě ořezány nejsou.</p>",1);
	generate_image_property_form ($db, $image_ids, $target_album);
}
else
{
	print_line ("<h2>Omlouváme se, ale nepodařilo se vložit ani jeden obrárzek, prozím, vraťte se o krok zpět.</h2>",1);
}



print_line("</div>");



//prints footer, closes the page and stops scrip execution
//output_footer ();

?>

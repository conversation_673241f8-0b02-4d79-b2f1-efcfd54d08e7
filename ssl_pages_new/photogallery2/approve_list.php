<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                         displays content of an album

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

    //all purpose utilities
    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");

    //read required low level data
    $config_array = parse_ini_file (INI_PATH, TRUE);

    //define temp dir (from ini file!)
    @define ("THUMB_X", $config_array['thumbnails']['width']);
    @define ("THUMB_Y", $config_array['thumbnails']['height']);


    ////////////////////////////////////////////////////////////////////////////

    //expects form to be established
    //SELECT p.picture_id, p.name, p.approved, UNIX_TIMESTAMP(p.created) as unix_created, a.name, m.jmeno, m.prijmeni
    function display_one_picture ($pic_record)
    {
        //display thumbnail
        $image_id = $pic_record[0];

        print_line ("<div class='thumbnail_box' id='thumbnail-box-$image_id'>", 1);

        //check if user can view the image (img public or user loged)
        $link = "<a target='_blank' href='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$image_id&amp;type=full'>";
        print_line ("$link<img border='0' class='thumbnail' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$image_id&amp;type=thumbnail' width='" . THUMB_X . "' height='" . THUMB_Y . "' alt='Thumbnail for image $image_id' /></a>",2);

        //translate certain characters to spaces, to break tool ong names (destroy layout)
        print_line ("<p class='thumbnail_name'>" . strtr ($pic_record[1], "_-", "  ") . " / "
                         . $pic_record[4] . "<br>"
                         . $pic_record[5] . " " . $pic_record[6] . "<br>"
                         . date("d-M-Y" , $pic_record[3]) . "</p>", 2);

        print_line ("<p class='left'>");
        print_line ("<input type='radio' name='id-$image_id' id='id-$image_id-no' value='refuse' " . (($pic_record[2] == AC_NOT_YET)?"checked ":"") . ">", 2);
        print_line ("<label for='id-$image_id-no'>Čeká</label><br>", 2);

        print_line ("<input type='radio' name='id-$image_id' id='id-$image_id-no-app' value='no' " . (($pic_record[2] == AC_NOT_APP)?"checked ":"") . ">", 2);
        print_line ("<label for='id-$image_id-no-app'>Zamítnout</label><br>", 2);

        print_line ("<input type='radio' name='id-$image_id' id='id-$image_id-yes' value='approve' " . (($pic_record[2] == AC_APPROVED)?"checked ":"") . ">", 2);
        print_line ("<label for='id-$image_id-yes'>Povolit</label>", 2);
        print_line ("</p>");

        print_line ("</div>", 1);
    }


    ////////////////////////////////////////////////////////////////////////////
    //display page
    print_line ("<div id='photo_gallery'>");
    print_line ("<h1 class='page_title'>Fotografie očekávající schválení</h1>");

    /*
    //check proper access rights
    if (($album_info['public'] == AC_PRIVATE) AND ($user_id == NULL))
    {
        print_line ("<h2>Nemáte oprávnění k prohlížení tohoto alba.</h2>");
        print_line ("<p>Toto album je dostupné pouze přihlášeným uživatelům.</p>");
        print_line ("<p><a href='./login.php'>Přihlásit se!</a></p>");
        //die
        exit;
        //output_footer ();
    } */

	
	
	(empty($start))?($start=0):false;
    //list all pictures
    //"SELECT * FROM m_members WHERE id_m = '" . $picture_info['owner'] . "';", TRUE);
    $pic_query = "SELECT p.name FROM m_fot_Pictures p JOIN m_fot_Albums a ON (belongs_to_album_id=album_id) LEFT JOIN m_members m ON (m.id_m = a.owner_id) WHERE p.public = '" . ((string) AC_PUBLIC) . "' AND p.approved = $app ORDER BY p.created DESC";
    $pic_result = $db->Query($pic_query);
	$pocet = $db->getNumRows($pic_result);
	
     $pic_query = "SELECT p.picture_id, p.name, p.approved, UNIX_TIMESTAMP(p.created) as unix_created, a.name, m.jmeno, m.prijmeni FROM m_fot_Pictures p JOIN m_fot_Albums a ON (belongs_to_album_id=album_id) LEFT JOIN m_members m ON (m.id_m = a.owner_id) WHERE p.public = '" . ((string) AC_PUBLIC) . "' AND p.approved = $app ORDER BY p.created DESC LIMIT $start,20;";
    $pic_result = $db->Query($pic_query);

?>
<table border="0">
	<tr>
		<td><?PHP if ($app==0){ echo "<b>" ;}; ?><a href="/index.php?men=<?PHP echo $men ?>&app=0">Čeká na schválení</a><?PHP if ($app==0){ echo "</b>" ;}; ?>&nbsp;&nbsp;&nbsp;</td>
		<td><?PHP if ($app==1){ echo "<b>" ;}; ?><a href="/index.php?men=<?PHP echo $men ?>&app=1">Schválené</a><?PHP if ($app==1){ echo "</b>" ;}; ?>&nbsp;&nbsp;&nbsp;</td>
		<td><?PHP if ($app==-1){ echo "<b>" ;}; ?><a href="/index.php?men=<?PHP echo $men ?>&app=-1">Neschválené</a><?PHP if ($app==-1){ echo "</b>" ;}; ?>&nbsp;&nbsp;&nbsp;</td>
		<td>stránkování: 
		<?PHP
		$s =  intval($pocet/20);
		for ($i=0; $i<=$s; $i++){
			$tmp = $i*20;
			if ($start==$tmp){echo "<b>" ;};
			echo "<a href=index.php?men=$men&app=$app&start=$tmp>".$tmp."</a> ";
			if ($start==$tmp){echo "</b>" ;};
		}
		?>
		</td>
	</tr>
</table>
<?PHP	



    print_line ("<form id='photo_approve' action='./index.php?men=men23.3.0.0&amp;action=save' method='post'>");
	print_line ("<input type='Hidden' name='app' value='$app'>");
    print_line ("<center><table class='m_fot_list' id='picture_list'>");

    $count = 0;
    $pics_in_row = ((int) floor (700 / (THUMB_X+10)));
    while ($pic_record = db_fetch_row($pic_result))
    {
        if (($count % $pics_in_row) == 0) print_line ("<tr>");
        print_line ("<td width='" . (THUMB_X + 10) . "'>");
        display_one_picture ($pic_record);
        print_line ("</td>");
        if (($count % $pics_in_row) == ($pics_in_row - 1)) print_line ("</tr>");
        $count++;
    }
    print_line ("</table>");

    print_line ("<br><br><input type='submit' name='submit' value='Uložit'>");

    print_line ("</center>");
    print_line ("</form>");

    db_free_result($pic_result);


    print_line ("</div>");

?>


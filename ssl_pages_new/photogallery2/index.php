<h1>Fotogalerie</h1>
<!--     <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> -->

<?php

/*


                              Mensa Photo Gallery
                              (c)2006 <PERSON><PERSON><PERSON>n<PERSON>
        2012-08-15, TK: Oprava statistik
        2012-06-15, TK: Dr<PERSON><PERSON><PERSON>.
        2012-04-01, TK: Jak vlkládat fotky do alba
        2012-03-13, TK: Statistiky
		2021-09-28, JD: Oprava SQL query v albech řazených dle data přidání


    Popis tabulek

    m_fot_Tags
        uid - id oznaceneho clena
        pid - id fotky
        x, y - souradnice tagu ve fotce
        deleted_on - 0 pokud je tak platny, jinak timestamp casu smazani
        aid - id autora tagu
        id  - id tagu (primarni klic)


*/

//basic constants and print line function
require_once ("../ssl_pages_new/photogallery2/include/constants.inc.php");
include_once ("../ssl_pages_new/photogallery2/include/general.inc.php");


// funkce na generovani tabulky
require("../ssl_library_new/draw_table_from_query.i");


// konvertuj id na odkaz na profil
function konverz_profil ($key, $value)
{
    if ($key == 'Profil') return "<a target='_new' href='./index.php?men=men4.1.0.0&s_content=view.i&id_m=$value'>profil</a>";
    return $value;
}
?>

<style>
    .draw_table_from_query {width:  20em;}
    .even               {background-color: rgb(238, 238, 255);}
    .Počet_fotografií   {text-align: right;}
    .Četnost            {text-align: right;}
    .Počet_označení     {text-align: right;}
    .Různých_osob       {text-align: right;}
</style>





<div id="gallery_index">

<?php
    //display random image
    $image_id = generate_random_image_id ($db);


    if ($image_id) print_line ("<a href='./index.php?men=men23.5.0.0&amp;pic_id=$image_id'><img id='random_picture' class='photo' border='0' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$image_id&amp;type=resized&amp;max_x=500&amp;max_y=1600' alt='Random picture, id.: $image_id' /></a>",1);
?>



<ul  class='m_fot_links'>
    <li><a href='./index.php?men=men23.4.0.0'>Prohlížet alba</a></li>
    <li><a href='./index.php?men=men23.2.0.0&amp;action=new'>Založit album</a></li>
    <li><a href='./index.php?men=men23.4.0.0&amp;owner=me'>Upravit album</a></li>
    <li><a href='./index.php?men=men23.1.0.0'>Nahrát fotografie</a></li>
    <li><a href='./index.php?men=men23.6.0.0'>Prohlédnout žebříčky</a></li>
</ul>

<p>Fotogalerie je nejužívanější komponentou intranetu.
Členové Mensy prohlédnou každý měsíc řádově několik desítek tisíc fotografií.</p>



<h2 style="margin-top:1em;">Označování tváří na fotografiích (tzv. tagování)</h2>

<p>Fotogalerie umožňuje díky práci Pavla Taufera označování tváří, tzv. tagování, obdobně jako například Facebook. Pod každou fotografií se ukazuje seznam označených lidí, a pokud si někoho vyhledáte v seznamu členů, jsou u jeho profilu uvedeny odkazy na fotografie, na kterých je označen.</p>

<ol>
    <li>Pro označení lidí na fotografii klikněte na nenápadný text "označit" nahoře nad obrázkem. Text se změní se na "označuji".</li>
    <li>Kliknutím do středu něčího obličeje, nejlépe na nos, vytvoříte rámeček s dotazem na jméno osoby. Stačí napsat část jména nebo příjmení a okamžitě vyjede seznam možných kandidátů (čím více napíšete, tím je kratší), obnovuje se v reálném čase. Označit lze pouze současné členy.</li>
    <li>Ze seznamu vyberete vhodné jméno, potvrďte a označení je hotové.</li>
</ol>

<p>Pokud se Vám nějaké označení nelíbí, můžete jej nahlásit. Administrátor jej smaže nebo upraví.</p>



<h2 style="margin-top:1em;">Jak vložit fotografii do diskuse?</h2>
<p>
    Pokud chcete vložit fotografii z alba do diskusního fóra, použijte následující text
    <tt>[img]https://intranet.mensa.cz/m_fot_show_picture2.php?pic_id=9926&type=resized&max_x=500&max_y=1200[/img]</tt>, kde <tt>pic_id</tt> je identifikátor obrázku, který uvidíte v adresním řádku při zvětšení obrázku v albu, a <tt>max_x</tt> i <tt>max_y</tt> jsou maximální rozměry obrázku v pixelech.
</p>



<h2 style="margin-top:1em;">Statistiky</h2>

<?PHP
	$fotek_celkem = $db->FetchArray($db->Query("SELECT count(*) as Pocet FROM m_fot_Pictures"));
	$alb_celkem = $db->FetchArray($db->Query("SELECT count(*) as Pocet FROM m_fot_Albums"));
    $hodnoceni_celkem = $db->FetchArray($db->Query("SELECT count(*) as Pocet FROM m_fot_Ratings"));
	$autoru_celkem = $db->FetchArray($db->Query("SELECT count( * ) AS Pocet, sum( if( NOT id_m IS NULL , 1, 0 ) ) AS Soucasni
		FROM (
			SELECT DISTINCT owner_id, id_m
			FROM m_fot_Albums LEFT OUTER JOIN m_members ON ( owner_id = id_m AND prizpevky >= year( now( ) ) ) ) autori"));

	$tagu_celkem = $db->FetchArray($db->Query("SELECT count(*) as Pocet FROM m_fot_Tags WHERE deleted_on=0"));
	$osob_celkem = $db->FetchArray($db->Query("SELECT count( * ) AS Pocet, sum( if( NOT id_m IS NULL , 1, 0 ) ) AS Soucasni
		FROM (
			SELECT DISTINCT uid, id_m
			FROM m_fot_Tags LEFT OUTER JOIN m_members ON ( uid = id_m AND prizpevky >= year( now( ) ) ) ) osoby
	"));
?>



<ul  class='m_fot_links'>
	<li>fotografií: <?PHP echo $fotek_celkem['Pocet']; ?></li>
	<li>hodnocení: <?PHP echo $hodnoceni_celkem['Pocet']; ?></li>
	<li>alb:  <?PHP echo $alb_celkem['Pocet']; ?></li>
	<li>autorů: <?PHP echo $autoru_celkem['Pocet']; ?> (z toho současných členů: <?PHP echo $autoru_celkem['Soucasni']; ?>)</li>
    <li>označení: <?PHP echo $tagu_celkem['Pocet']; ?></li>
    <li>označených osob: <?PHP echo $osob_celkem['Pocet']; ?> (z toho současných členů: <?PHP echo $osob_celkem['Soucasni']; ?>)</li>
</ul>





<h3 style="margin-top:2em;">Počety fotografií vložených v jednotlivých letech</h3>
<?PHP draw_table_from_query ($db, "roky", "SELECT year(created) as Rok, count(picture_id) as 'Počet fotografií'
	FROM m_fot_Pictures GROUP BY year(created) ORDER BY Rok ASC", 'konverz_profil'); ?>






<h3 style="margin-top:2em;">Nejčastěji označované osoby</h3>
<p>11 nejčastěji označených současných platných členů Mensy. V seznamu se zobrazují pouze lidé, kteří mají veřejný profil.
<!--
<br /><br />
Poznámka: 19. 4. 2012 byla opravena chyba, díky které se ve statistice dříve objevovala i smazaná označení.--></p>

<?PHP
    // testovací označení
    // --m.id_m != 3982 AND      -- z nějakého důvodu je mnoho tagů na autora Pavel Taufer (default?)
    // to bylo dáno testováním při zavádění
	$SQL_tagy = "
		SELECT
			m.id_m as 'Profil',
			CONCAT (m.jmeno, ' ', m.prijmeni) as 'Jméno',
			count(id)  AS 'Četnost'
		FROM
			-- zajimaji nas pouze tagy k platnym clenum
			`m_fot_Tags` t INNER JOIN
			m_members m ON ( t.uid = m.id_m )
		WHERE
			m.id_m != 3979    AND		-- zuzana simkova, nema tagy rada
			m.public = 'Y'	  AND		-- pouze členové, co povolili zveřejnění profilu
			m.prizpevky >= (year(now())-1) AND    -- aktuální člen (nebo člen minulý rok)
			m.disable = 'N'   AND       -- veřejný profil
			deleted_on = 0              -- tag nebyl smazán
		GROUP BY
			m.id_m, m.jmeno, m.prijmeni
		ORDER BY
			count(id) DESC,
			prijmeni ASC,
			jmeno ASC
		LIMIT
			0, 11";

	draw_table_from_query ($db, "tagy", $SQL_tagy, 'konverz_profil');
?>




<h3 style="margin-top:2em;">Nejaktivnější označovatelé tváří</h3>
<p>Následujících 11 členů označilo největší počet tváří celkem.
Započítána jsou všechna nesmazaná označení současných i bývalých členů.</p>
<?PHP
	$SQL_tagy = "
		SELECT
			CONCAT (m.jmeno, ' ', m.prijmeni) as 'Jméno označovatele',
			count( id ) AS 'Počet označení'
		FROM
			`m_fot_Tags` t INNER JOIN
			m_members m ON ( t.aid = m.id_m )
		WHERE
			deleted_on = 0   AND       -- tag nebyl smazán
			m.prizpevky >= (year(now())-1)     -- aktuální člen (nebo člen minulý rok)
		GROUP BY
			m.id_m
		ORDER BY
			count(id) DESC,
			prijmeni ASC,
			jmeno ASC
		LIMIT
			-- jen prvních dvacet dva
			0, 11";

	draw_table_from_query ($db, "tagy", $SQL_tagy, 'konverz_profil');

?>





<h3 style="margin-top:2em;">Nejaktivnější označovatelé různých tváří</h3>
<p>Následujících 11 členů označilo největší počet různých osob.
Označení stejné osoby na více fotografiích se počítá pouze jednou, smazaná označení se nepočítají.
<i>Statistika byla opravena 16. 8. 2012.</i>
</p>
<?PHP
/*
    m_fot_Tags
        uid - id oznaceneho clena
        pid - id fotky
        x, y - souradnice tagu ve fotce
        deleted_on - 0 pokud je tak platny, jinak timestamp casu smazani
        aid - id autora tagu
        id  - id tagu (primarni klic)
        */

	$SQL_tagy = "
		SELECT
			CONCAT (m.jmeno, ' ', m.prijmeni) as 'Jméno označovatele',
			count(uid) AS 'Různých osob'
		FROM
			-- zajimaji nas pouze tagy k platnym clenum
			(SELECT DISTINCT uid, aid FROM `m_fot_Tags` WHERE deleted_on = 0) t INNER JOIN
			m_members m ON (t.aid = m.id_m )
		GROUP BY
			m.id_m
		ORDER BY
			count(uid) DESC,
			prijmeni ASC,
			jmeno ASC
		LIMIT
			0, 11";

	draw_table_from_query ($db, "tagy", $SQL_tagy, 'konverz_profil');
?>


<p>Zajímavé statistiky týkající se jednotlivých fotografií naleznete na stránce <a href="./index.php?men=men23.6.0.0">žebříčky</a>.</p>



<h2 style="margin-top:3em;">O aplikaci</h2>
<p>Tuto komponentu naprogramoval v roce 2006 <a href='mailto:<EMAIL>'>Tomáš Kubeš</a>. Případné komentáře, podněty nebo pochvaly, pište rovnou jemu. Tagování osob a JavaScriptové prohlížení fotografií naprogramoval Pavel Taufer. Kód byl naposledy upraven 28. září 2021.</p>



</div>

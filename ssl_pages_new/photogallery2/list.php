<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                            displays various lists

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

	////////////////////////////////////////////////////////////////////////////
    //read required low level data
    $config_array = parse_ini_file (INI_PATH, TRUE);

    //define constants (from ini file)
    @define ("THUMB_X", $config_array['thumbnails']['width']);
    @define ("THUMB_Y", $config_array['thumbnails']['height']);

    @define ("PAGE_SIZE", $config_array['general']['best_list_items']);




	////////////////////////////////////////////////////////////////////////////
	//defines possible sorting methods
	//name => (criterion name, query)
	//ordering is done upon criterion aliased as list_item
	$criteria_list = array (	
	
			"Nejnovější fotografie" => array ("Počet zobrazení",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', p.views AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY p.created DESC, list_item ASC LIMIT " . PAGE_SIZE . ";"),	
	
	
		"Nejčetněji zobrazované fotografie" => array ("Četnost zobrazení za den",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', FORMAT(p.views/DATEDIFF(CURDATE(),p.created),2) AS 'list_item', p.views/DATEDIFF(CURDATE(),p.created) AS 'order_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY order_item DESC LIMIT " . PAGE_SIZE . ";"),


		"Nejvíce zobrazované fotografie" => array ("Počet zobrazení",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', p.views AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY list_item DESC LIMIT " . PAGE_SIZE . ";"),
			
			
		"Nejlépe hodnocené fotografie" => array ("Hodnocení",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', FORMAT(AVG(r.rating),1) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) JOIN mensaweb.m_fot_Ratings r ON (p.picture_id=r.Pictures_picture_id) GROUP BY picture_id, picture_name, p.public, p.created, unix_created, comment, album, album_id, jmeno, prijmeni ORDER BY list_item ASC, COUNT(r.rating) DESC LIMIT " . PAGE_SIZE . ";"),		

		
		"Nejčasteji hodnocené fotografie" => array ("Počet hodnocení",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', COUNT(r.rating) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) JOIN mensaweb.m_fot_Ratings r ON (p.picture_id=r.Pictures_picture_id) GROUP BY picture_id, picture_name, p.public, p.created, unix_created, comment, album, album_id, jmeno, prijmeni ORDER BY list_item DESC, AVG(r.rating) ASC LIMIT " . PAGE_SIZE . ";"),
	
	
		"Nejlépe hodnocené z nejčastěji hodnocených fotografií" => array ("Hodnocení",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', FORMAT(AVG(r.rating),1) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) JOIN mensaweb.m_fot_Ratings r ON (p.picture_id=r.Pictures_picture_id) GROUP BY picture_id, picture_name, p.public, p.created, unix_created, comment, album, album_id, jmeno, prijmeni ORDER BY COUNT(r.rating) DESC, list_item ASC LIMIT " . PAGE_SIZE . ";"),

		"Nejméně zobrazované fotografie" => array ("Počet zobrazení",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', p.views AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY list_item ASC, p.created ASC LIMIT " . PAGE_SIZE . ";"),
		

		"Fotografie s nejdelším komentářem" => array ("Délka komentáře",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', LENGTH(p.comment) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY list_item DESC, p.created DESC LIMIT " . PAGE_SIZE . ";"),

		"Fotografie s nejkratším nenulovým komentářem" => array ("Délka komentáře",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', LENGTH(p.comment) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) WHERE LENGTH(p.comment) > 0 ORDER BY list_item ASC, p.created DESC LIMIT " . PAGE_SIZE . ";"),
			
		
		"Fotografie s nejdelším jménem" => array ("Délka jména",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', LENGTH(p.name) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY list_item DESC, p.created DESC LIMIT " . PAGE_SIZE . ";"),		

		"Fotografie vložené v tuto dobu kterýkoliv den" => array ("Absolutní odchylka od aktuálního času ve vteřinách",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', (ABS(SECOND(CURTIME())-SECOND(p.created)) + (60 * ABS(MINUTE(CURTIME())-MINUTE(p.created))) + (3600 * ABS(HOUR(CURTIME())-HOUR(p.created)))) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) ORDER BY list_item ASC, p.created DESC LIMIT " . PAGE_SIZE . ";"),		

		// NOTE: at first, order all pictures by year, to display only current contestants, then by selection criteria (use numericalvalue, as strings will compare erraticvally)
		"Convergence" => array ("Body",
		"SELECT p.picture_id AS 'picture_id', p.name AS 'picture_name', p.public, p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', p.comment AS 'comment', FORMAT(SUM(1/r.rating),2) AS 'list_item', a.name AS 'album', album_id AS 'album_id', m.jmeno AS 'jmeno', m.prijmeni AS 'prijmeni', SUM(1/r.rating) AS 'ordering_item' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id=a.album_id) JOIN mensaweb.m_members m ON (a.owner_id = m.id_m) JOIN mensaweb.m_fot_Ratings r ON (p.picture_id=r.Pictures_picture_id) GROUP BY picture_id, picture_name, p.public, p.created, unix_created, comment, album, album_id, jmeno, prijmeni ORDER BY YEAR(p.created) DESC, ordering_item DESC, COUNT(r.rating) DESC, p.picture_id DESC LIMIT " . PAGE_SIZE . ";")	
		
	);



	//where - accessibility condition - depending on context
	//WHERE ".@$redefine_album_where."
	//	        $rating = db_one_field_query ($db, "SELECT AVG(rating) FROM m_fot_Ratings WHERE Pictures_picture_id='$picture_id';", FALSE);
	//
	


	////////////////////////////////////////////////////////////////////////////
	//displays one entry - box with photo - picture should be valid for given context
	function display_entry ($db_row, $list_item_name = "Kritérium")
	{
		//global $redefine_album_where;
		//global $redefine_where;
        global $user_id;
		
  		$image_id = (int) $db_row['picture_id'];
		
        print_line ("<div id='picture-".$image_id."'>",3);
        
		//link only public images - double safety	
		$link1 = (($db_row['public']==AC_PUBLIC) OR ($user_id != NULL))?"<a href='".IMAGE_PATH."pic_id=$image_id'>":"";
		$link2 = ($link1 != "")?"</a>":"";

        print_line ("$link1<img src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$image_id&amp;type=thumbnail' width='" . THUMB_X . "' height='" . THUMB_Y . "' alt='Thumbnail for image $image_id' />$link2",4);
 
        //title
        print_line ("<h3>" . $link1 . strtr ($db_row['picture_name'], "_-", "  ") . $link2 . "</h3>",4);
        print_line ("<p><b>" . $list_item_name . ":</b>  " . 	$db_row['list_item'] . "</p>",4);
        print_line ("<p><strong>Autor:</strong>  " . 		$db_row['jmeno'] . " " . $db_row['prijmeni'] . "</p>",4);
        print_line ("<p><strong>Album:</strong>  <a href='" . ALBUM_PATH . "album_id=" . $db_row['album_id'] . "'>" . $db_row['album'] . "</a></p>",4);
        print_line ("<p><strong>Vloženo:</strong>  " . 	date("j. M Y, G:i:s" , $db_row['unix_created']) . "</p>",4);
        print_line ("<p><strong>Komentář:</strong>  " . 	(($db_row['comment']!="")?$db_row['comment']:"-") . "</p>",4);


        print_line ("</div>",3);
	
	}



	////////////////////////////////////////////////////////////////////////////////////////////
	////////////////////////////////////////////////////////////////////////////////////////////
	// create page
	
	

	//choose ordering (choose from post data or select first)
	$current_criterion = (isset($_POST['criterion']))?($_POST['criterion']):(key($criteria_list));
	
	////////////////////////////////////////////////////////////////////////////
	print_line ("\n\n\n\n<h2>" . $current_criterion . "</h2>");
	print_line ("<div id='photo_list'>\n\n\n\n\n");


	//ordering control
	print_line ("<form enctype='application/x-www-form-urlencoded' method='post' id='sorting_select' action='./index.php?men=men23.6.0.0'><p>Kritérium řazení: ",1);
	print_line ("<select id='criterion' name='criterion' onChange='submit()'>",1);
	foreach ($criteria_list as $key => $value) print_line ("<option value='$key'" . (($current_criterion==$key)?" selected='selected'":"") . ">$key</option>", 2);
	print_line ("</select>",1);
	print_line ("</p></form>\n\n\n\n",1);	



	
    $query = $criteria_list[$current_criterion][1];

	//debug help, switch defined in general.inc.php
	if (FOTO_DB_DEBUG) echo "\n\n<!-- list.php : root : query : '$query' -->\n\n";
	
	$result = $db->Query($query);



////////////////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////////////////
	
	print_line ("<table class='m_fot_list' id='big_tile_list'>");
	$count = 0;
	
	while ($row = db_fetch_array($result))
	{
        if (($count % 2) == 0) print_line ("<tr>", 1);
		print_line ("<td><p><b>" . ($count+1) . ". místo</b></p>" ,2);	
		display_entry ($row, $criteria_list[$current_criterion][0]);		
		print_line ("</td>\n",2);
        if (($count % 2) == 1) print_line ("</tr>", 1);
        $count++;
	}
	
	print_line ("</table>");


?>



</div>
<?php

    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                            displays list of albums

                           (c)2006-2008 <PERSON><PERSON><PERSON>
						    encoding = utf-8

    ////////////////////////////////////////////////////////////////////////////
    */

    //read required low level data
    $config_array = parse_ini_file (INI_PATH, TRUE);

    //define temp dir (from ini file!)
    @define ("THUMB_X", $config_array['thumbnails']['width']);
    @define ("THUMB_Y", $config_array['thumbnails']['height']);
    @define ("PAGE_SIZE", $config_array['general']['album_list_items']);

	//pages start from 0 !!!!!!
	//get user page value - always force int conversion (security)
	$current_page = 0;
	if (isset($_GET['page'])) $current_page = (int) @$_GET['page'];

	//show olny my albums or all albums
	//check if value owner is set to me, suspend any warnings about it not being defined
	$my_albums_filter = false;
	if (@$_GET['owner'] == 'me') $my_albums_filter = true;




	//VIEW_ALL - determines if we are in the intranet or on public web
	if (!VIEW_ALL) {
		$redefine_where = " WHERE a.cathegory = 'mensa' AND p.approved = ".((string) AC_APPROVED);
		$redefine_album_where = " AND approved = ".((string) AC_APPROVED);
	}


	if (FOTO_DB_DEBUG) echo "\n\n<!-- album_list.php : root : thumby: " . THUMB_X . " , thumbx: " . THUMB_Y . " , page_size: " . PAGE_SIZE . ", current page: $current_page -->\n\n";


	//this function is only a hack - MSIE cannot break too long words and
	//destroys table layout if they occur
	function break_too_long_words ($line)
	{
		return wordwrap  ($line, 30, "\n", TRUE);
		//return $line;
	}



    ////////////////////////////////////////////////////////////////////////////
		//displays one div with album picture, link text and everything else
    //expects object with associative array of DB row
    function display_album_box (&$db, $album_row) {
				global $redefine_album_where;
				global $redefine_where;
        global $user_id;

				//skip not public albums if user is not logged
        if (($album_row['public']==0) AND ($user_id == NULL)) return FALSE;


        ////////////////////////////////////////////////////////////////////////
        $edit = ($album_row['owner_id']==$user_id)?(" <a href='".SERVER_PATH."/index.php?men=men23.2.0.0&amp;album_id=" . $album_row['album_id'] . "'>(upravit)</a>"):"";

        print_line ("<div class='album_box' id='album-".$album_row['album_id']."'>",1);
        print_line('<div class="album_thumbnail_box">');

        //print album picture
        if ($album_row['thumbnail_picture_id'])
        {
            //if thumbnail is set
            print_line ("<a href='".ALBUM_PATH."album_id=" . $album_row['album_id'] . "'><img src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=" . ((string) $album_row['thumbnail_picture_id']) . "&amp;type=thumbnail' width='" . THUMB_X . "' height='" . THUMB_Y . "' alt='Thumbnail pro album ".$album_row['name']."' /></a>", 2);
        } else {
        		//otherwise first picture in album
            $thm_id = db_one_field_query ($db, "SELECT picture_id FROM m_fot_Pictures WHERE belongs_to_album_id=" . $album_row['album_id'] . " ORDER BY picture_id LIMIT 1;", FALSE);
            print_line ("<a href='".ALBUM_PATH."album_id=" . $album_row['album_id'] . "'><img class='album_thumbnail' border='0' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=" . ((string) $thm_id) . "&amp;type=thumbnail' width='" . THUMB_X . "' height='" . THUMB_Y . "' alt='Thumbnail pro album ".$album_row['name']."' /></a>", 2);
        }
        print_line('</div>');


        //album title
        print_line ("<h3><a href='".ALBUM_PATH."album_id=" . $album_row['album_id'] . "'>".$album_row['name']."</a>$edit</h3>",2);

        //get picture count in this album
        $pic_count = db_one_field_query ($db, "SELECT count(*) FROM m_fot_Pictures WHERE belongs_to_album_id=" . $album_row['album_id'] . " $redefine_album_where;");
        print_line ("<p class='album_count'><b>fotografií:</b> $pic_count</p>",2);

        //get user name
        //$user_info = db_query1row_query ($db, "SELECT * FROM m_members WHERE id_m = '" . $album_row['owner_id'] . "' $redefine_album_where;", TRUE);
		$user_info = db_query1row_query ($db, "SELECT * FROM m_members WHERE id_m = '" . $album_row['owner_id'] . "';", TRUE);
        print_line ("<p class='album_author'><b>autor:</b> ".  $user_info['jmeno'] . " " . $user_info['prijmeni']."</p>",2);

        print_line ("<p class='album_comment'><b>komentář:</b> ".  break_too_long_words($album_row['comment'])."</p>",2);
		if (VIEW_ALL) {
        print_line ("<p class='album_cathegory'><b>typ:</b> ". (($album_row['cathegory']=='personal')?'intranet':'web')  ."</p>",2);
		}

        //datum založení
        print_line ("<p class='album_cathegory'><b>založeno:</b> ".date("d. m. Y" , $album_row['unix_created'])."</p>",2);

        print_line ("</div>",1);

    }

    //implemented by JHE
    //prints links for page jump
    function print_paging($current, $all_item_count, $my_albums_filter = false){


	    //prints link to the first page
	    if ($current > 5){
            echo " <a href='" . LIST_PATH . "page=".(0) . ($my_albums_filter?"&amp;owner=me":"") . "'><u>1</u></a> ";
            if ($current > 6)
                echo "..";
        }

	    //index of last page
        $last_page_idx = $all_item_count%PAGE_SIZE == 0 ? floor($all_item_count/PAGE_SIZE) - 1 : floor($all_item_count/PAGE_SIZE);

        //prints link to 2 previous pages and 2 following pages
	    for ($page = $current - 5; $page <= $current + 5; $page++){
            if ($page < 0 or $page > $last_page_idx)
                continue;

	        if ($page == $current) {
                echo "<strong>" . ($current+1) . "</strong>";
            } else {
                echo " <a href='" . LIST_PATH . "page=". $page . ($my_albums_filter?"&amp;owner=me":"") . "'>" . ($page+1) . "</a> ";
            }
        }

        //prints link to the last page
        if ($current < $last_page_idx - 5){
            if ($current < $last_page_idx - 6)
                echo "..";
            echo " <a href='" . LIST_PATH . "page=". $last_page_idx . ($my_albums_filter?"&amp;owner=me":"") . "'><u>" . ($last_page_idx+1) . "</u></a> ";
        }
    }


	//pritns navigation links for paging
	//expects to be called inside of 2 column table
	function print_navigation ($current, $item_count, $my_albums_filter = false)
	{
        //note: we fetch +1 rows to determine if next page button shall be active, so this will always work
				print_line ("<div class='links'><p>");
				if ($current > 0) print_line ("<a href='" . LIST_PATH . "page=".($current-1) . ($my_albums_filter?"&amp;owner=me":"") . "'>&lt;&lt;&lt;předchozí stránka</a>");
				else print_line ("jste na začátku výpisu");

				print_line ("</p><p>");

				if ($item_count >= PAGE_SIZE) print_line ("<a href='" . LIST_PATH . "page=".($current+1) . ($my_albums_filter?"&amp;owner=me":"") . "'>následující stránka&gt;&gt;&gt;</a>");
				else print_line ("jste na konci výpisu");
				print_line ("</p></div>");

	}



    ////////////////////////////////////////////////////////////////////////////
   	////////////////////////////////////////////////////////////////////////////
   	////////////////////////////////////////////////////////////////////////////


	// album list query
    $query = "";

    if ($my_albums_filter)
    {
		$starting_album = $current_page * PAGE_SIZE;

       	//select own albums (no paging necessary)
	    $query = "SELECT * , UNIX_TIMESTAMP(created) AS 'unix_created' FROM m_fot_Albums WHERE owner_id='$user_id' ORDER BY unix_created DESC LIMIT ".$starting_album.",".(PAGE_SIZE+1).";";
        //$header_part = " vlastních";
    }else
    {
		//for calrity }can be also defined inline)
		$starting_album = $current_page * PAGE_SIZE;

        //albums for all users
        //$query = "SELECT * FROM m_fot_Albums;";
        //display albums with newest first... (rename columns to ensure compatibility with normal query)
        //note: we fetch +1 rows to determine if next page button shall be active
        $query = "SELECT a.album_id as 'album_id', a.owner_id as 'owner_id', a.name as 'name', a.public as 'public', a.cathegory as 'cathegory', a.ordering as 'ordering', a.comment as 'comment', a.thumbnail_picture_id as 'thumbnail_picture_id', max(p.created) as 'last', UNIX_TIMESTAMP(a.created) AS 'unix_created'  FROM m_fot_Albums a LEFT JOIN m_fot_Pictures p ON (a.album_id = p.belongs_to_album_id) ".@$redefine_where." GROUP BY p.belongs_to_album_id ORDER BY last DESC LIMIT ".$starting_album.",".(PAGE_SIZE+1).";";
    }


    //print_line ("<h1 class='page_title'>Přehled$header_part Alb</h1>");




?>




<!-- web gallery component code - Tomas Kubes -->
<div id='photo_gallery'>




<?php


   	////////////////////////////////////////////////////////////////////////////
   	////////////////////////////////////////////////////////////////////////////
	//album list display

	//debug help, switch defined in general.inc.php
	if (FOTO_DB_DEBUG) echo "\n\n<!-- album_list.php : root : query : '$query' -->\n\n";


	//query for rows
    $result = $db->Query($query);

    //number of all albums in database
    $sql = "SELECT * FROM m_fot_Albums a LEFT JOIN m_fot_Pictures p ON (a.album_id = p.belongs_to_album_id) ".@$redefine_where." GROUP BY p.belongs_to_album_id";
    $res = $db->Query($sql);
    $all_albums_count = db_num_rows($res);


		if (db_num_rows($result) == 0) {
				print_line ("<h2>Na této stránce nejsou žádná alba.</h2>",1);
				print_line ("<p><a href='".SERVER_PATH."/index.php?men=men23.2.0.0&amp;action=new'>Založite si nové album!</a></p>",1);
		} else {
				print_line('<div id="upper_gallery_nav">');
				print_navigation($current_page, db_num_rows($result), $my_albums_filter);
				print_line('</div>');

				$count = 0;
				print_line('<div id="big_tile_list">');
				//note: we fetch +1 rows to determine if next page button shall be active
				while (($row = db_fetch_array($result)) and ($count < PAGE_SIZE)) {
						display_album_box ($db, $row);
						$count++;
				}
				print_line('</div>');

				print_line('<div id="lower_gallery_nav">');
				print_navigation($current_page, db_num_rows($result), $my_albums_filter);
				print_line('</div>');
				print_line('<div id="gallery_pagination">');
				print_paging($current_page, $all_albums_count, $my_albums_filter);
				print_line('</div>');
		}


  ////////////////////////////////////////////////////////////////////////////
	// album selection footer
	// display only in intranet mode
	if (VIEW_ALL)
	{
    	print_line ("\n\n\n<ul class='m_fot_links'>");
	    if (@$_GET['owner']=='me')  print_line ("<li><a href='./index.php?men=men23.4.0.0'>zobrazit všechna alba</a></li>",1);
    	else                        print_line ("<li><a href='./index.php?men=men23.4.0.0&amp;owner=me'>zobrazit pouze moje alba</a></li>",1);
	    print_line ("</ul>");
	}

?>




</div>
<!-- end of web gallery component code -->

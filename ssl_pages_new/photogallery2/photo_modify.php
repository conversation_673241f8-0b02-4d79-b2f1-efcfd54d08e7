<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                               displays one photo

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */


	
    //check for pic id (should be checkedd outside, but to be sure)
    if (!isset ($_GET['pic_id']))
    {
        print_line ("<h2>Nedostal jsem indentifikátor obrázku!</h2>");
        print_line ("<p>To může být způsobeno vnitřní chybou systému nebo tím, že se snažíte prohlížet obrázek mimo intranet.</p>");
        //stop execution of this script
        return;
    }

    //get picture id (force the int to prevent malicious data)
    $picture_id = (int) $_GET['pic_id'];

	
    ////////////////////////////////////////////////////////////////////////////
    //save picture data (if they were posted)
    function save_image_property_modifications (&$db, $picture_id, $post_data)
    {
        //check if it matches post (abort if not)
        if ($picture_id != ((int) $_POST['picture-1'])) return FALSE;

        //store values relevent for our picture
        $values = array ();

        //set default
        $values['public'] = "";

        //decode all passed data
        foreach ($_POST as $name => $value)
        {
            //split name to name - id
            //string strtok ( string str, string token )
            $type = strtok ($name, '-');
            $id   = strtok ('-');

            if ($picture_id == $id)
            {
                //echo "found $type = $value for $file_id <br />";
                //keep this value - and make sure it is secure
                $values[$type] = check_user_input($value);
            }
        }

        //decode public
        $pub = (($values['public']=='on')?((string) AC_PUBLIC):((string) AC_PRIVATE));

        //now run the query
        $result = db_modification_query ($db, "UPDATE m_fot_Pictures SET public='$pub', name='" . check_user_input($values['name'],50) . "', comment='" . check_user_input($values['comment'],255) . "', belongs_to_album_id='" . ((int) $values['belongs_to_album_id']) . "' WHERE picture_id='$picture_id';");
        //echo "$query";

        //fatal error?!
        if ($result > 1) 
		{
			echo "<!-- debug: save_image_property_modifications ($picture_id, $post_data) : more rows modified ?! -->";
			die;
		}	

        //oterwise return - 1 row = OK, 0 faliure
        return $result;
    }
	


    ////////////////////////////////////////////////////////////////////////////

    //need to do it now to display header properly
    //get information about the picture
    /*
        CREATE TABLE `Pictures` (
            `picture_id` mediumint(8) unsigned NOT NULL auto_increment,
            `public` tinyint(1) NOT NULL default '0',
            `name` varchar(50) collate latin2_czech_cs default 'fotka',
            `comment` varchar(255) collate latin2_czech_cs default NULL,
            `created` datetime NOT NULL default '0000-00-00 00:00:00',
            `image_data` mediumblob NOT NULL,
            `image_thumbnail` blob NOT NULL,
            `views` mediumint(8) unsigned NOT NULL default '0',
            `belongs_to_album_id` smallint(5) unsigned NOT NULL default '0',
            PRIMARY KEY  (`picture_id`),
            KEY `FK_Picture_1` (`belongs_to_album_id`)
        ) ENGINE=MyISAM DEFAULT CHARSET=latin2 COLLATE=latin2_czech_cs AUTO_INCREMENT=1 ;
    */


    ////////////////////////////////////////////////////////////////////////////
    //save posted data
    $saved = FALSE;
    if (isset($_POST['save']))
    {
        $saved = save_image_property_modifications ($db, $picture_id, $_POST);
    }


	//get information
    $picture_info = db_query1row_query ($db, "SELECT name, comment, public, created, views, belongs_to_album_id FROM m_fot_Pictures WHERE picture_id=$picture_id;");


    ////////////////////////////////////////////////////////////////////////////
    //check proper access rights
    if (($picture_info['public'] == AC_PRIVATE) AND ($user_id == NULL))
    {
        print_line ("<h2>Nemáte oprávnění k prohlížení této fotografie.</h2>");
        print_line ("<p>Tato fotografie je přístupná pouze přihlášeným uživatelům.</p>");
        return;
    }




	
	
    ////////////////////////////////////////////////////////////////////////////
    //web page

    print_line ("<div id='photo_gallery'>");
    //print_line ("<h2 class='page_title'>Uprav vlastnosti obrázku " . $picture_info['name'] . "</h2>");

    //choose action
    //edit picture or delete picture?
    if (isset($_POST['delete']))
    {
        //delete confirmation dialog
        //edit form

        print_line ("<p>Opravdu chcete tuto fotografii smazat?</p>",3);

        print_line ("<form id='delete_picture' method='post' action='/index.php?men=men23.5.0.0&action=edit&pic_id=$picture_id'>",1);

        //paranoic check to prevent some problems if isset will nto work properly
        print_line ("<input type='hidden' id='detele_check' name='detele_check' value='yes' />",2);

        print_line ("<p class='input'><input type='submit' name='delete_2' value='Smazat' /> &nbsp;",2);

        print_line ("<input type='submit' name='back' value='Zpět' /></p>",2);

        print_line ("</form>",1);

    //delete the picture
    } elseif (isset($_POST['delete_2']) AND ($_POST['delete_check']='yes'))
    {
        //do not delete now v- delete after nav is dispalyed - for correct function
        //delete picture now
        print_line ("<p>Fotografie smazána!</p>",3);

    }else
    {
        if (isset($_POST['save']))
        {
            //message about saving
            if ($saved) print_line ("<p class='message'>Změny uloženy</p>");
            else print_line ("<p class='message'>Uložení změn se nezdařilo</p>");
        }

        //edit form
        print_line ("<form id='picture_properties' method='post' action='/index.php?men=men23.5.0.0&action=edit&pic_id=$picture_id'>",1);

        //generate_image_description_form ($image_id, $picture_number,         $target_album, $album_owner_id, $thumb_x, $thumb_y)
        generate_image_description_form ($db, $picture_id, 1, $picture_info['belongs_to_album_id'], $user_id);

        //save target album - will be used for link
        print_line ("<input type='hidden' id='target_album_id' name='target_album_id' value='" . $picture_info['belongs_to_album_id'] . "' />",2);

        print_line ("<center><input type='submit' name='save' value='Uložit změny' /> &nbsp; ",2);
        print_line ("<input type='submit' name='delete' value='Smazat obrázek' /></center>",2);

        print_line ("</form>",1);
    }
	

	
    //navigation menu
    print_line ("<div id='photo_navigation'>",1);
    print_line ("<ul class='m_fot_links'>",2);
    //do not display if picture was deleted
    if (!isset($_POST['delete_2'])) print_line ("<li><a href='/index.php?men=men23.5.0.0&amp;pic_id=$picture_id'>zpět na stránku fotografie</a></li>",3);
    print_line ("<li><a href='/index.php?men=men23.4.0.0&amp;album_id=" . $picture_info['belongs_to_album_id'] . "#image-$picture_id'>zpět na album</a></li>",3);
    print_line ("<li><a href='/index.php?men=men23.4.0.0'>zpět na přehled alb</a></li>",3);
    print_line ("</ul>", 2);
    print_line ("</div>", 1);


    print_line ("</div>");
	
	
	
	/////////////////////////////////////////////////////////////////////////////////////////
    //delete now if it was ordered
    if (isset($_POST['delete_2']) AND ($_POST['delete_check']='yes'))
    {
        //delete ratings
        $rat_c = db_modification_query ($db, "DELETE FROM m_fot_Ratings WHERE Pictures_picture_id='$picture_id';");
        echo "<!-- Deleted ratings: $rat_c. -->\n";

        //delete picture
        $del = db_modification_query ($db, "DELETE FROM m_fot_Pictures WHERE picture_id='$picture_id';");
        echo "<!-- Deleted picture: $del. -->\n";

        //delete photo file from HDD
        $f_name =  PHOTO_PATH . ((string) $pic_id) . ".jpg";
        $fdel =  unlink ($f_name);
        if ($fdel)  echo "<!-- Deleted file: $f_name . -->\n";
        else        echo "<!-- Cannot delete file: $f_name . -->\n";

        //delete photo file from HDD
        $f_name =  THUMBNAIL_PATH . ((string) $pic_id) . ".jpg";
        $fdel =  unlink ($f_name);
        if ($fdel)  echo "<!-- Deleted thumbnail file: $f_name . -->\n";
        else        echo "<!-- Cannot thumbnail delete file: $f_name . -->\n";
    }

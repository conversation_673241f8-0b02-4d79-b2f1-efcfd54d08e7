<!--
-<PERSON><PERSON><PERSON>, e<PERSON>j<PERSON><PERSON><PERSON><PERSON> na schválení, str<PERSON>kováno po 20<br>

- n<PERSON><PERSON><PERSON>,<br>
autor, datum, nazev, popis, galerie<br>
<br>
combobox s výbirem<br>
- veoe<PERSON><PERSON> - <PERSON><PERSON><PERSON><br> 0
- veoejn<PERSON> - schv<PERSON><PERSON>o<br> 1
- veoe<PERSON><PERSON> - neschv<PERSON>leno<br> -1
- (neve<PERSON><PERSON><PERSON>) nemusíme dávat....<br>

-->

<?php
 //basic constants and print line function
  		require_once ("../ssl_pages_new/photogallery2/include/constants.inc.php");
		    //all purpose utilities
	    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");
		
    if (isset($_POST['submit']))
        //save results
        include ("../ssl_pages_new/photogallery2/approve_save.php");
    else
        //display form
		(empty($app))?($app=0):false;
        include ("../ssl_pages_new/photogallery2/approve_list.php");
?>
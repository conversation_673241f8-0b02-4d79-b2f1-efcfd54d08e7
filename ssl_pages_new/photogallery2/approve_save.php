<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                    upload pictures, select files to upload

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

    //all purpose utilities       -general.inc.php
    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");


    //function update_entry ()


?>


<div id='photo_gallery'>

<h1>Uložení změn</h1>

<ul>
<?php


    //go through all post data
    foreach ($_POST as $field_name => $data)
    {
        //explode using
        $fields = explode ('-', $field_name);

        //check if first part is id
        if ($fields[0] != 'id') continue;

        //check if there are exactly two parts
        if (count($fields) != 2) continue;

        //now we should be in id section
        $id = (int) $fields[1];

        //check if transformation was ok
        if ($id == 0) continue;

        //determine the data
        $target = AC_NOT_YET;
        if ($data == 'approve') $target = AC_APPROVED;
        if ($data == 'no') $target = AC_NOT_APP;

        //now save the data
        $result = db_modification_query ($db, "UPDATE m_fot_Pictures SET approved = '" . ((string) $target) . "' WHERE picture_id = '$id';", FALSE);
		//print_line ("UPDATE m_fot_Pictures SET approved = '" . ((string) $target) . "' WHERE picture_id = '$id';", FALSE);

        if ($result == 1) print_line ("<li>Obrázek $id byl " . (($target==AC_APPROVED)?"zveřejněn":"zamítnut") . ".</li>");

    }


?>
</ul>


<h2>Pokračování...</h2>
<?PHP
     include_once ("../ssl_pages_new/photogallery2/approve_list.php");
?>
</div>


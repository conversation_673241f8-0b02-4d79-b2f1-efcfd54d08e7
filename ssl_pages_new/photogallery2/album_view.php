<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                         displays content of an album

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */


    //read required low level data
    $config_array = parse_ini_file (INI_PATH, TRUE);

    //define temp dir (from ini file!)
    @define ("THUMB_X", $config_array['thumbnails']['width']);
    @define ("THUMB_Y", $config_array['thumbnails']['height']);


    ////////////////////////////////////////////////////////////////////////////
	if (!VIEW_ALL) {
		$redefine_album_where = " AND public=1 AND approved = ".((string) AC_APPROVED);
	}

    function display_one_picture ($pic_record)
    {
        global $user_id;

        //display thumbnail
        $image_id = $pic_record['picture_id'];

        print_line ("<div class='thumbnail_box' id='image-$image_id'>", 1);

        //check if user can view the image (img public or user loged)
        $link = (($pic_record['public']==AC_PUBLIC) OR ($user_id != NULL))?"<a href='".IMAGE_PATH."pic_id=$image_id'>":"";
        print_line ("$link<img border='0' class='thumbnail' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$image_id&amp;type=thumbnail' width='" . THUMB_X . "' height='" . THUMB_Y . "' alt='Thumbnail for image $image_id' />" . (($link != "")?"</a>":""),2);

        print_line ("<p class='thumbnail_name'>" . strtr ($pic_record['name'], "_-", "  ") . "</p>", 2);

        print_line ("</div>", 1);

    }



    ////////////////////////////////////////////////////////////////////////////
    /*
        comment  varchar(255) Yes NULL
        name  varchar(50) No
        public  tinyint(1) No 0
        cathegory  enum('personal', 'mensa') No personal
        owner_id  int(11) No 0
        album_id  smallint(5)  No
    */

    //get album id (force the int to prevent malicious data)
    $album_id = (int) $_GET['album_id'];

    //need to do it now to display header properly
    //get information about the album
    $album_info = db_query1row_query ($db, "SELECT * FROM m_fot_Albums WHERE album_id=$album_id;", TRUE);

    //we need to save if result was positive or not
    //point we want to display header anyways and if we do so, queries for values will fill the array (with empty values)
    //and it will not report as empty
    $exists = (bool) $album_info;

    //outputs html page header
    //function output_header ($rights = AC_PRIVATE, $page_title = "", $page_description = "", $key_words = "")
    //output_header (AC_PUBLIC, "Obsah alba " . $album_info['name'], "obasah alba " . $album_info['name'] . ", popis:" .  $album_info['comment']);



    ////////////////////////////////////////////////////////////////////////////
    //display page
    print_line ("<div id='photo_gallery'>");


    ////////////////////////////////////////////////////////////////////////////
    //check if we have selected something
    if (!$exists)
    {
        print_line ("<h1 class='page_title'>Požadované album neexistuje!</h1>");
        print_line ("<ul class='m_fot_links'><li><a href='./index.php?men=men23.4.0.0'>Zpět na přehled alb</a></li></ul>");
        print_line ("</div>");
        exit;
        //output_footer ();
    }


    print_line ("<h1 class='page_title'>Prohlížíte album " . $album_info['name'] . "</h1>");

    //check proper access rights
    if (($album_info['public'] == AC_PRIVATE) AND ($user_id == NULL))
    {
        print_line ("<h2>Nemáte oprávnění k prohlížení tohoto alba.</h2>");
        print_line ("<p>Toto album je dostupné pouze přihlášeným uživatelům.</p>");
        print_line ("<p><a href='./login.php'>Přihlásit se!</a></p>");
        //die
        return;
        //output_footer ();
    }

    if ($album_info['owner_id'] == $user_id)
    {
        print_line ("<p>Toto je vaše album.</p>");

    }else
    {
        //get author
        $user_info = db_query1row_query ($db, "SELECT * FROM m_members WHERE id_m = '" . $album_info['owner_id'] . "';", TRUE);
        print_line ("<p>Toto album vytvořil uživatel ".  $user_info['jmeno'] . " " . $user_info['prijmeni'].".</p>");
    }

    //determine ordering
    $ordering = ($album_info['ordering']=='name')?" ORDER BY TRIM(name) ASC, picture_id ASC":" ORDER BY created ASC, picture_id ASC";
	//debug
	//print_line ("<p>Ordering ". $album_info['ordering'] . ", query $ordering.<p>");

    //list all pictures
    $pic_query = "SELECT picture_id, name, public, created FROM m_fot_Pictures WHERE belongs_to_album_id=$album_id " . @$redefine_album_where . $ordering . ";";
    $pic_result = $db->Query($pic_query);
	// debug print_line ("<p>$pic_query</p>");


    print_line ("<article class='m_fot_list' id='picture_list'>");
    $count = 0;
	if (VIEW_ALL) {
    	$pics_in_row = ((int) floor (700 / (THUMB_X+10)));
	} else {
		$pics_in_row = ((int) floor (650 / (THUMB_X+10)));
	}
    while ($pic_record = db_fetch_array($pic_result))
    {
        if (($count % $pics_in_row) == 0) print_line ("<div class='display_row'>");
        display_one_picture ($pic_record);
        if (($count % $pics_in_row) == ($pics_in_row - 1)) print_line ("</div>");
        $count++;
    }
    print_line ("</article>");

    if (db_num_rows($pic_result) == 0) print_line ("<p>Toto album neobsahuje žádné fotografie.</p>");


    db_free_result($pic_result);

    //nav for owner only
    print_line ("<ul class='m_fot_links'>");
    if ($album_info['owner_id'] == $user_id)
    {
        print_line ("<li><a href='./index.php?men=men23.1.0.0&amp;album_id=$album_id'>Nahrát do tohoto alba fotografie</a></li>");
        print_line ("<li><a href='./index.php?men=men23.2.0.0&amp;album_id=$album_id'>Upravit nebo smazat toto album</a></li>");
    }

	if (VIEW_ALL) {
    //general nav
    print_line ("<li><a href='./index.php?men=men23.4.0.0'>Zpět na přehled alb</a></li>");
    print_line ("</ul>");
	}

    print_line ("</div>");

    //prints footer, closes the page and stops scrip execution
    //output_footer ();

?>


<?php

    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                        updates information about album

                            (c)2006-2008 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

    //all purpose utilities
    require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");


    $album_id = (isset($_GET['album_id']))?((int) $_GET['album_id']):("new");
    $dont_modify = FALSE;



    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////


    ////////////////////////////////////////////////////////////////////////////
    //display album information form
    function display_album_form (&$db, $album_id = "new", $d_public = TRUE, $d_name = "", $d_cat = 'mensa', $d_ord = 'name', $d_thumb = 0, $d_comment = "")
    {
        global $user_id;

        print_line ("<div id='album-form-$album_id'>",0);

        print_line ("<form id='album-properties-$album_id' method='post' action='./index.php?men=men23.2.0.0&amp;action=save'>",1);

        //print_line ("<h3>Informace o albu</h3>",2);

        //display_checkbox ($id, $label = "", $indent = 0, $checked = FALSE)
        //display_checkbox ("album_public", "Veřejné", 2, $d_public);
        //all public by force
        print_line ("<input type='hidden' id='album_public' name='album_public' value='on' />",2);

        //name
        display_text_field ("album_name", "Název alba", 30, 2, $d_name);

        //cathegory
        cathegory_selector ("album_cathegory", "Kategorie", 2, $d_cat);

        //ordering
        ordering_selector ("album_ordering", "Řazení fotografií", 2, $d_ord);

        //album thumbnail (or text if no iamges are avialable)
        if (!thumbnail_list ($db, "album_thumbnail", "Titulní obrázek", $user_id, $album_id, 2, $d_thumb))
        {
            print_line("<p>Nemáte v albu vložen žádný obrázek, který by se dal použít jako titulní obrázek alba. Můžete jej vybrat později až nějaký nahrajete.</p>", 2);
        }

        //comment
        display_text_area ("album_comment", "Komentář", 40, 3, 2, $d_comment);

        //some technical data
        print_line ("<input type='hidden' id='album_id' name='album_id' value='$album_id' />",2);

        //save
        print_line ("<p class='input'><input type='submit' name='save' value='Uložit informace' />",2);

        //delete for existing albums
        if ($album_id != "new")
            print_line (" &nbsp; <input type='submit' name='delete' value='Smazat album' />",2);

        print_line ("</p>");

        print_line ("</form>",1);

        print_line ("</div>",0);
    }

    ////////////////////////////////////////////////////////////////////////////
    //dispalys for above with vlues for given id
    function display_album_form_for_id (&$db, $album_id)
    {
        global $user_id;

        //get album information from database
        $album_data = db_query1row_query ($db, "SELECT name, public, cathegory, ordering, thumbnail_picture_id, comment FROM m_fot_Albums WHERE album_id='$album_id' AND owner_id='$user_id';");

        print_line ("<h2>Úprava alba: " . $album_data['name'] . "</h2>",0);

        //display form
        display_album_form($db, $album_id, $album_data['public'], $album_data['name'], $album_data['cathegory'], $album_data['ordering'], $album_data['thumbnail_picture_id'], $album_data['comment']);
		
		return $album_id;
    }




    //this function saves ablum information
    //it expects that post data are proper and valid
    //will take care of differences - new / update
    function save_album_information (&$db, $post_data)
    {
        global $user_id;
        /*
            CREATE TABLE `Albums` (
                `album_id` smallint(5) unsigned NOT NULL auto_increment,
                `owner_id` int(11) NOT NULL default '0',
                `name` varchar(50) character set latin2 collate latin2_czech_cs NOT NULL default 'album',
                `public` tinyint(1) NOT NULL default '0',
                `cathegory` enum('personal','mensa') NOT NULL default 'personal',
                `ordering` enum('name','created') NOT NULL default 'name',
                `comment` varchar(255) character set latin2 collate latin2_czech_cs default NULL,
                `thumbnail_picture_id` mediumint(8) unsigned default NULL,
                PRIMARY KEY  (`album_id`)
            ) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=latin2 AUTO_INCREMENT=2 ;
        */

        $name       = check_user_input($post_data['album_name'], 50);
        $public     = (string) (($post_data['album_public']=="on")?AC_PUBLIC:AC_PRIVATE);
        $cathegory  = check_user_input($post_data['album_cathegory']);
        $comment    = check_user_input($post_data['album_comment'], 255);
        $ordering   = check_user_input($post_data['album_ordering']);
        //at fitrst, retype to int - to ensure that only numbers will get through, then retype to string - for use in query
        $thumbnail  = (string) ((int) @$post_data['album_thumbnail']);


        $owner_id   = $user_id;
        $album_id   = check_user_input($post_data['album_id']);

        //assemble the query
        //depends on action type
        $query = "";
        if ($album_id == "new")
        {
            //create new
            $query = "INSERT INTO m_fot_Albums (album_id, owner_id,    name,    public,    cathegory,    ordering,   comment, thumbnail_picture_id )
                                  VALUES (    NULL, $user_id, '$name', '$public', '$cathegory', '$ordering', '$comment', '$thumbnail');";

			//retrieve an id of newly created instance

        }else
        {
            $query = "UPDATE m_fot_Albums SET name='$name', public='$public', cathegory='$cathegory', ordering='$ordering', comment='$comment', thumbnail_picture_id='$thumbnail'
                                    WHERE album_id=$album_id;";

            //always update public bit for pictures
            $pub_quer = "UPDATE m_fot_Pictures SET public='$public' WHERE belongs_to_album_id=$album_id;";
            //debug: echo $pub_quer;
            $db->Query ($pub_quer);

            
        }

        //echo $query;
        $result = $db->Query ($query);
        
        echo "<!-- $query -->";

        //note: if we have just creted album, we need to get its ID:
        if ($album_id == "new")
        {
          $album_id =			db_insert_id();
        }
		
		//return album ID on success or FALSE on fail (known problem: insert of an album 0 will look like a failure)
		if (db_affected_rows() == 1) 	return $album_id;
		else							return FALSE;
    }





    //displays error message, menu and dies
	/*
    function error_message ()
    {
        print_line("Post:");
        print_r ($_POST);
        print_line("Get:");
        print_r ($_GET);

        print_line("<h2>Nerozumím vašemu požadavku!</h2>");
        print_line("<a href='./index.php'>Zpět na hlavní stránku</a>");

        //prints footer, closes the page and stops scrip execution
        //output_footer ();
		return;
    }
	*/


 



	////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
	print_line("<div id='photo_gallery'>");




    ////////////////////////////////////////////////////////////////////////////
    //if the page was called with get data save, we need to process post data
    // post[save] = Uložit informace - encoding problems
    if ((@$_GET['action'] == "save") AND (isset($_POST['save'])))
    {
        //save received data
        $album_id = save_album_information ($db, $_POST);
		if ($album_id)	print_line ("<p><b>Změny uloženy.</b></p>");
        else print_line ("<p><b>Uložení změn se nezdařilo!</b></p>");
    }




    ////////////////////////////////////////////////////////////////////////////
    ////////////////////////////////////////////////////////////////////////////
    switch ($_GET['action'])
    {
        //create brand new album
        case'new':
        {
            //prepare form for new album
            print_line ("<h2>Založení nového alba</h2>");

            //empty form
            display_album_form($db);
            $dont_modify = TRUE;
            break;
        }

        //save value form submit event
        case 'save':
        {
            //two options
            //data were saved - display confirmation adn form again
            //delete was pressed - display delete confirmation dialog

            if (@$_POST['delete'] == "Smazat album")
            {
                $album_id = (int) $_POST['album_id'];
                //check if user is album owner
                $owner = db_one_field_query ($db, "SELECT count(*) FROM m_fot_Albums WHERE owner_id='$user_id' AND album_id='$album_id';");
                if (!$owner)
                {
                    print_line ("<h1 class='page_title'>Požadované album neexistuje!</h1>");
                    $dont_modify = TRUE;
                    break;
                    //output_footer ();
                }

                //delete dialog
                print_line ("<h2>Potvrdit smazání alba: " . $_POST['album_name'] . "</h2>");

                print_line ("<div id='album-form-$album_id'>",0);
                print_line ("<form id='album-properties-$album_id' method='post' action='./index.php?men=men23.2.0.0&amp;album_id=$album_id&amp;action=delete'>",1);

                print_line ("<p>Mám album opravdu smazat?</p>",2);

                //get picture count
                $pic_count = db_one_field_query ($db, "SELECT count(*) FROM m_fot_Pictures WHERE belongs_to_album_id='$album_id';");
                print_line ("<p>Veškeré v něm obsažené fotografie ($pic_count fotografií) budou také smazány!</p>",2);

                print_line ("<input type='hidden' id='album_id' name='album_id' value='$album_id' />",2);

                print_line ("<p class='input'><input type='submit' name='delete' value='Smazat album' /> &nbsp;",2);

                print_line ("<input type='submit' name='save' value='Zrušit' /></p>",2);

                print_line ("</form>",1);
                print_line ("</div>",0);


            }
			// save save branch not neede - treated by default
            break;
        }

        case 'delete':
        {
            //delete album - two step process
            //step 1 get = save, post = Smazat album
            //step 2 get = delete, post = Smazat album

            //we are in step 2
            //delete album and pictures
            if ($_POST['delete'] == "Smazat album")
            {
                $album_id = (int) $_POST['album_id'];

                //check if user is album owner
                $owner = db_one_field_query ($db, "SELECT count(*) FROM m_fot_Albums WHERE owner_id='$user_id' AND album_id='$album_id';");
                if (!$owner)
                {
                    print_line ("<h1 class='page_title'>Požadované album neexistuje!</h1>");
                    $dont_modify = TRUE;
                    break;
                    //output_footer ();
                }

                print_line ("<h2>Mazání alba</h2>");

                //delete files from HDD
                $file_del = $db->Query ("SELECT picture_id FROM m_fot_Pictures WHERE belongs_to_album_id=$album_id;");
                //go through all rows
                while ($row = db_fetch_row($file_del))
                {
                    //delete file from HDD
                    $f_name =  PHOTO_PATH . ((string) $row[0]) . ".jpg";
                    $fdel =  unlink ($f_name);
                    if ($fdel)  echo "<!-- Deleted file: $f_name . -->\n";
                    else        echo "<!-- Cannot delete file: $f_name . -->\n";

                    $f_name =  THUMBNAIL_PATH . ((string) $row[0]) . ".jpg";
                    $fdel =  unlink ($f_name);
                    if ($fdel)  echo "<!-- Deleted thumbnail file: $f_name . -->\n";
                    else        echo "<!-- Cannot delete thumbnail file: $f_name . -->\n";
                }
                db_free_result($file_del);

                //delete ratings
                //works from  MySQL 4.0.2.
                $del_r = db_modification_query ($db, "DELETE FROM m_fot_Ratings USING m_fot_Ratings JOIN m_fot_Pictures ON (m_fot_Ratings.Pictures_picture_id=m_fot_Pictures.picture_id) WHERE m_fot_Pictures.belongs_to_album_id='$album_id';");
                print_line ("<p>Smazáno $del_r hodnocení.</p>");

                print_line ("<p>Mažu fotografie...</p>");
                $del_p = db_modification_query ($db, "DELETE FROM m_fot_Pictures WHERE belongs_to_album_id=$album_id;");
                print_line ("<p>Smazáno $del_p fotografií.</p>");

                print_line ("<p>Mažu album...</p>");
                $del_a = db_modification_query ($db, "DELETE FROM m_fot_Albums WHERE album_id=$album_id AND owner_id=$user_id;");

                if ($del_a) print_line ("<p>Hotovo!</p>");
                else print_line ("<p>Mazání se nezdařilo.</p>");

                //no dialog for browsing this album etc...
                $dont_modify = TRUE;

                //delete - break
                break;
            }
        }

        default:
        {
			//just display the form for given ID
            if ($album_id) display_album_form_for_id ($db, $album_id);
			else $dont_modify = TRUE; //if no album id is given display no form and no save links
        }
    }



    //footer navigation
    if ($_GET['action'] == 'new')
        print_line ("<p><b>Album dosud není uloženo, nezapomeňte jej uložit!</b></p>");

    print_line ("<ul class='m_fot_links'>",2);
    if (!$dont_modify)
    {
        //do not display these if album is creatred and not yet saved
        print_line ("<li><a href='./index.php?men=men23.1.0.0&amp;album_id=$album_id'><b>nahrát do tohoto alba fotografie</b></a></li>");
        print_line ("<li><a href='./index.php?men=men23.4.0.0&amp;album_id=$album_id'>zobrazit obsah toto alba</a></li>");
    }
    print_line ("<li><a href='./index.php?men=men23.4.0.0&amp;owner=me'>přejít na seznam vlastních alb</a></li>");
    print_line ("<li><a href='/index.php?men=men23.4.0.0'>přejít na seznam alb</a></li>");
    print_line ("</ul>",2);



?>




</div>


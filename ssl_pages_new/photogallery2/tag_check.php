<?php
function Name($id_m){
  global $db, $GLOBALS, $data;
  if(isset ($GLOBALS[$id_m])){
    return $GLOBALS[$id_m]['jmeno'].' '.$GLOBALS[$id_m]['prijmeni'];
  }
  if((int)$id_m==0)return;
  $sql="SELECT jmeno, prijmeni, prezdivka FROM m_members WHERE id_m = '" . $id_m. "';";
  $res=$db->Query($sql);
  $data=$db->FetchArray($res);
  $GLOBALS[$id_m]['jmeno']=@$data['jmeno'];
  $GLOBALS[$id_m]['prijmeni']=@$data['prijmeni'];
  return @$data['jmeno'].' '.@$data['prijmeni'];
}
function Status($status)
{ // BEGIN function Status
  switch($status){
    case 0:
      return "ceka";
    case 1:
      return "smazano";
    case 2:
      return "ponechano";
  }
} // END function Status


$task=(int)$_GET['task'];
$rid=(int)$_GET['id'];
switch($task){
  case 1:
    $now=time();
    $sql = 'SELECT * FROM `m_fot_Tag_Check` WHERE id='.$rid;
    $result = $db->Query($sql);
    if($data=$db->FetchArray($result)){
      $sql="UPDATE `m_fot_Tags` SET deleted_on=$now WHERE uid=".$data['uid']." AND pid=".$data['pid']." AND deleted_on=0";
      $db->Query($sql);
      $sql="UPDATE `m_fot_Tag_Check` SET time_solved=$now, solved_by=".$a_user['id_m'].",status=1 WHERE id=".$rid;
      $db->Query($sql);
      echo '<div>Tag smazan</div>';
    }
    break;
  case 2:
    $now=time();
    $sql = 'SELECT * FROM `m_fot_Tag_Check` WHERE id='.$rid;
    $result = $db->Query($sql);
    if($data=$db->FetchArray($result)){
      $sql="UPDATE `m_fot_Tag_Check` SET time_solved=$now, solved_by=".$a_user['id_m'].",status=2 WHERE id=".$rid;
      $db->Query($sql);
      echo '<div>Tag ponechan</div>';
    }
    break;
  }
  $sql = 'SELECT * FROM `m_fot_Tag_Check`';//TODO  moznost vyberu od-do, jen nevyresene...
  //die($sql);
  $count=0;
  $result = $db->Query($sql);
  print_line('<table>');
  print_line('<tr><th>Iniciator kontroly</th><th>ID fotky</th><th>Oznaceny</th><th>ID oznaceni</th><th>Autor oznaceni</th><th>text</th><th>Vyreseno kym</th><th>Vyreseno kdy</th><th>Zadano kdy</th><th>Status</th><th>Moznosti</th></tr>');
  while($data=$db->FetchArray($result)){
    print_line('<tr>');

    $count++;
    $sql_ozn='SELECT aid FROM `m_fot_Tags` WHERE id='.$data['tid'];
    $data_ozn=$db->FetchArray($db->Query($sql_ozn));
    print_line('<td>'.Name(@$data['aid']).'</td><td><a href="?men=men23.5.0.0&pic_id='.@$data['pid'].'">'.@$data['pid'].'</a></td><td>'.Name(@$data['uid']).'</td><td>'.@$data['tid'].'</td><td>'.Name(@$data_ozn['aid']).'</td><td>'.nl2br(@$data['text']).'</td><td>'.Name(@$data['solved_by']).'</td><td>'.(@$data['time_solved']==0?0:date("H:i:s j.m.y",@$data['time_solved'])).'</td><td>'.date("H:i:s j.m.y",@$data['time_added']).'</td><td>'.Status(@$data['status']).'</td>');
    if(@$data['status']==0)print_line('<td><a href="?men=men23.8.0.0&task=1&id='.@$data['id'].'">smazat</a> <a href="?men=men23.8.0.0&task=2&id='.@$data['id'].'">nechat byt</a> </td>');
    print_line('</tr>');
  }
  print_line('</table>');
?>

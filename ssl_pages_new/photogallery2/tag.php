<?PHP
ob_end_clean();
error_reporting(E_ALL^E_NOTICE);
require_once ("../ssl_pages_new/photogallery2/include/constants.inc.php");
require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");
header('content-type: text/html; charset=utf-8');
switch ((int)$_POST['task']){
  case 1: //tagovani

    $tag_y = (int) $_POST["tag_y"];
    $tag_x = (int) $_POST["tag_x"];
    $tag_photo = (int) $_POST["tag_photo"];
    $tag_user = (int) $_POST["tag_user"];
    $now=time();

    //TODO check range, photo, user
    if($tag_x<0 || $tag_y<0)break;
    $sql="SELECT * FROM `m_fot_Tags` WHERE uid='$tag_user' AND pid='$tag_photo' AND deleted_on=0";
    $result = $db->Query($sql);
    if($db->getNumRows($result)!=0){//kontrola jestli uz uzivatel nebyl otagovan na dane fotce //TODO - warning misto zmeny
      $sql="UPDATE `m_fot_Tags` SET deleted_on=$now WHERE uid=$tag_user AND pid=$tag_photo AND deleted_on=0";
      $db->Query($sql);
    }
    $sql = "INSERT INTO `m_fot_Tags` (`uid`, `aid`, `pid`, `x`, `y`, `time`, `deleted_on`) VALUES ('$tag_user', '".$a_user['id_m']."', '$tag_photo', '$tag_x', '$tag_y', '$now', '0')";
    $db->Query($sql);
    //echo $tag_x." ".$tag_y.";".$tag_photo.";".$tag_user;
    break;

    //-------------------------------------------------------------------//
  case 2://list

    $name =  $db->escape($_POST["name"]);
    $surn =  $db->escape($_POST["surn"]);
    $nick =  $db->escape($_POST["nick"]);

    $where="prizpevky>=".(date("Y") - 1)." AND (ukonceni_clenstvi_poplatky=0 OR ukonceni_clenstvi_poplatky>=".date("Y").") ";//kontrola zdali je clen aktivni
    if($name!=""){
      $where.='AND `jmeno` LIKE  \''.$name.'%\'  ';
    }
    if($surn!=""){
      $where.='AND `prijmeni` LIKE  \''.$surn.'%\' ';
    }
    if($nick!=""){
      $where.='AND `prezdivka` LIKE \''.$nick.'%\'';
    }

    if($where=="") die(0);

    $sql = 'SELECT jmeno, prijmeni, prezdivka, id_m FROM `m_members` WHERE '.$where.' ORDER BY jmeno, prijmeni LIMIT 0, 30 ';//TODO  moznost zruseni

    $result = $db->Query($sql);
    $count=0;
    $tmp="";
    while($data=$db->FetchArray($result)){
      $count++;
      $tmp.=$data["jmeno"]." ".$data["prijmeni"]." (".$data["prezdivka"].");".$data["id_m"].";";
    }

    echo $count.";".$tmp.";".$name.$surn.$nick.';';//pocet;jmeno;id
    break;
    //-------------------------------------------------------------------//
  case 3://names

    $tag_photo = (int) $_POST["tag_photo"];

    //TODO check photo - pristup
    $sql="SELECT m_fot_Tags.x, m_fot_Tags.y, m_fot_Tags.aid, m_fot_Tags.uid, m_members.jmeno, m_members.prijmeni, m_members.prezdivka FROM m_fot_Tags INNER JOIN m_members ON m_fot_Tags.uid=m_members.id_m WHERE m_fot_Tags.pid='$tag_photo' AND m_fot_Tags.deleted_on=0 ORDER BY jmeno, prijmeni;";

    $result = $db->Query($sql);
    $count=0;
    $tmp="";
    while($d=$db->FetchArray($result)){
      $count++;
      $prezd= ($d['prezdivka']!='')?(" (".$d['prezdivka'].")"):'';
      $tmp.=$d['x'].";".$d['y'].";".$d['uid'].";".$d['aid'].";".$d['jmeno']." ".$d['prijmeni'].$prezd.";";//x,y,uid,aid,jmeno prijmeni (prezdivka),
    }

    echo $count.";".$tmp.";";
    
    
    break;

  case 4://delete
    $tag_photo = (int) $_POST["tag_photo"];
    $tag_user = (int) $_POST["tag_user"];
    $now=time();
    $sql="SELECT * FROM `m_fot_Tags` WHERE uid='$tag_user' AND pid='$tag_photo' AND deleted_on=0";
    $result = $db->Query($sql);
    if($db->getNumRows($result)!=0){//kontrola jestli uz uzivatel byl otagovan na dane fotce
      $data=$db->FetchArray($result);
      if($a_user['id_m']== $data['aid']){
        $sql="UPDATE `m_fot_Tags` SET deleted_on=$now WHERE uid=$tag_user AND pid=$tag_photo AND deleted_on=0";
        $db->Query($sql);
        echo '1;';//OK
      }else{
        echo '2;';//Jiny autor
      }
    }else{
      echo '3;';//tag nenalezen
    }
    break;
  //----------------------------------------------------------------------------
  case 5://nacitani informaci o fotografii
    if (!isset ($_POST['pic_id']))
    {
        //stop execution of this script
      return;
    }

    $pic_id = (int) $_POST['pic_id'];
    $id_m=(int) $_POST['id_m'];

    /*if ((isset ($_POST['rating'])) AND ($user_id != NULL))
    {
        //todo - check if user is not picture owner
        save_rating ($db, $pic_id);
    }*/
    $sql="SELECT p.name as 'name', p.comment as 'comment', p.public as 'public', p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', views, belongs_to_album_id, a.owner_id AS 'owner', a.ordering AS 'ordering', a.name as 'a_name', album_id FROM mensaweb.m_fot_Albums a JOIN mensaweb.m_fot_Pictures p ON (p.belongs_to_album_id=a.album_id) WHERE picture_id='$pic_id';";
    $res=$db->doQuery($sql);
    if(!($picture_info=$db->FetchArray($res))){//TODO co delat kdyz foto neexistuje
      echo '-1;';
      break;
    }

    $name=$picture_info['name'];
    
    if($id_m!=0){//zobrazovat dle uzivatele?
      $sql="SELECT jmeno, prijmeni, prezdivka FROM m_members WHERE id_m = '" . $id_m. "';";
      $res=$db->doQuery($sql);
      if($data=$db->FetchArray($res)){
        $name=$data['jmeno'].' '.$data['prijmeni'];
      }else{//wrong id_m, ignoring
        $id_m=0;
      }
    }
    if($id_m==0){
      $album="<b>Album:</b> <a href='".ALBUM_PATH."album_id=".$picture_info['album_id']."'>".$picture_info['a_name']."</a>";
    }else{
      $album="<b>Fotografie uživatele <a href='/index.php?men=men4.1.0.0&s_content=view.i&id_m=$id_m'>$name</a> z alba:</b> <a href='".ALBUM_PATH."album_id=".$picture_info['album_id']."'>".$picture_info['a_name']."</a>";
    }

    ////////////////////////////////////////////////////////////////////////////
    //get prev and next picture - determine ordering
    //point we need to sort using either name or created columns, we need to find current value of both
    //we need to trim the values - some hidden characters were casing huge problems in name sorting
    $ord_1col_name   = ($picture_info['ordering'] == 'name')?"TRIM(name)":"created";
    $ord_1value      = ($picture_info['ordering'] == 'name')?trim($picture_info['name']):$picture_info['created'];
    
    //displays a link to left image
     $album_id=$picture_info['belongs_to_album_id'];
    //determine if non public images should be allowed
    //skip not-public images
    $pub = ($user_id == NULL)?("AND public=".((string)AC_PUBLIC)." AND approved=".((string)AC_APPROVED)):"";
    if($id_m==0){
      $query = "SELECT picture_id, name FROM m_fot_Pictures WHERE $ord_1col_name < '$ord_1value' AND belongs_to_album_id='$album_id' $pub ORDER BY $ord_1col_name DESC, picture_id ASC LIMIT 1;";
      $idm="";
    }else{
      $query = "SELECT p.picture_id, p.name FROM m_fot_Pictures p, m_fot_Tags t WHERE $pic_id>t.pid AND t.uid=$id_m AND t.pid=p.picture_id AND t.deleted_on=0 ORDER BY pid DESC LIMIT 1 $pub ";
      $idm="&id_m=$id_m";
    }
    $res=$db->doQuery($query);
    $prev_pic_info = $db->FetchArray($res);
    if (!$prev_pic_info)
    {
      $prev="<span class='links' align='left'>Začátek alba.</span>";
    }else
    {
      $prev="<a href='#".$prev_pic_info['picture_id']."-$id_m'><img class='arrow' src='/images/fotogalerie/left.png' alt='".$prev_pic_info['name']."' title='".$prev_pic_info['name']."'/></a>";
    }
    //right image

    $pub = ($user_id == NULL)?("AND p.public=".((string)AC_PUBLIC)." AND p.approved=".((string)AC_APPROVED)):"";
    if($id_m==0){
      $query = "SELECT picture_id, name FROM m_fot_Pictures p WHERE $ord_1col_name > '$ord_1value' AND belongs_to_album_id='$album_id' $pub ORDER BY $ord_1col_name ASC, picture_id ASC LIMIT 1;";
      $idm="";
    }else{
      $query = "SELECT p.picture_id, p.name FROM m_fot_Pictures p, m_fot_Tags t WHERE $pic_id<t.pid AND t.uid=$id_m AND t.pid=p.picture_id AND t.deleted_on=0 ORDER BY pid ASC LIMIT 1 $pub ";
      $idm="&id_m=$id_m";
    }
    $res=$db->doQuery($query);
    $next_pic_info = $db->FetchArray($res);
    if (!$next_pic_info)
    {
      $next= "<span class='links'>Konec alba.</span>";
    }else
    {
      $next= "<a href='#".$next_pic_info['picture_id']."-$id_m'><img class='arrow' src='/images/fotogalerie/right.png' alt='".$next_pic_info['name']."' /></a>";
    }

    $photo="<a name='photo' id='photo_a' href='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$pic_id&amp;type=full' target='_blank'><img onClick='clickImg(event);' onmousemove='imgOver(event);' id='photo_img' class='photo' border='0' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$pic_id&amp;type=resized&amp;max_x=682&amp;max_y=500' alt='Obrázek číslo: $pic_id - " . $picture_info['name'] . "' /></a>";

    $user_info =  $db->FetchArray($db->doQuery("SELECT * FROM m_members WHERE id_m = '" . $picture_info['owner'] . "';"));
    $author="<b>Autor:</b> ".  $user_info['jmeno'] . " " . $user_info['prijmeni'];
    $views="<b>Zobrazení:</b> " . $picture_info['views'];

    $rating_a = $db->FetchArray($db->doQuery("SELECT AVG(rating) AS 'avg', COUNT(rating) AS 'count' FROM m_fot_Ratings WHERE Pictures_picture_id='$pic_id';"));
    if (isset($rating_a['count']) AND $rating_a['count'] > 0)
      $rating= "<b>Hodnocení uživatelů:</b> ". sprintf("%1.1f", $rating_a['avg']) . ", hodnotitelů: " . $rating_a['count'];
    else
      $rating= "<b>Hodnocení uživatelů:</b> dosud nehodnoceno";
    $date="<b>Vloženo:</b> " . date("j. M Y, G:i:s" , $picture_info['unix_created']);
    $comment="<b>Komentář:</b> " . (($picture_info['comment']!="")?$picture_info['comment']:"-");


    //rating
    $user_rating = FALSE;
    if ($user_id != NULL) $user_rating = db_one_field_query ($db, "SELECT rating FROM m_fot_Ratings WHERE user_id='$user_id' AND Pictures_picture_id='$pic_id';", FALSE);
    //display message depnding on state of rating
    if ($user_rating == FALSE)
    {
        $photo_rating="Hodnoťte fotografii (jako ve škole: 1 - líbí se mi, 3 - průměrná, 5 - nelíbí se mi).";
    }else
    {
        //if user rated, display his choice
        $photo_rating="Vaše současné hodnocení je <b>$user_rating</b>, můžete jej změnit (jako ve škole: 1 - líbí se mi, 3 - průměrná, 5 - nelíbí se mi).";
    }
    $photo_rating=str_replace(":", '&#58;', $photo_rating);
    $edit=($user_id == $picture_info['owner'])?"<li><a href='index.php?men=men23.5.0.0&amp;action=edit&amp;pic_id=$pic_id'>Upravit vlastnosti obrázku nebo jej smazat</a></li>":"";
    
    $next=str_replace(":", '&#58;', $next);
    $prev=str_replace(":", '&#58;', $prev);
    $name=str_replace(":", '&#58;', $name);
    $album=str_replace(":", '&#58;', $album);
    $photo=str_replace(":", '&#58;', $photo);
    $author=str_replace(":", '&#58;', $author);
    $views=str_replace(":", '&#58;', $views);
    $rating=str_replace(":", '&#58;', $rating);
    $date=str_replace(":", '&#58;', $date);
    $comment=str_replace(":", '&#58;', $comment);
    $edit=str_replace(":", '&#58;', $edit);
    echo "1:".$next.':'.$prev.":".$name.":".$album.":".$photo.":".$pic_id.":".$id_m.":".$author.":".$views.":".$rating.":".$date.":".$comment.":".$photo_rating.":".$edit;
    break;


  case 6://report tag
    //TODO check input

    $pic_id = (int) $_POST["pic_id"];
    $id_m = (int) $_POST["id_m"];//TODO - misto id uziv pouzit id oznaceni

    $sql="SELECT * FROM `m_fot_Tags` WHERE uid='$id_m' AND pid='$pic_id' AND deleted_on=0";
    $result = $db->Query($sql);
    if($db->getNumRows($result)==0){//kontrola jestli uz uzivatel nebyl otagovan na dane fotce //TODO - warning misto zmeny
      echo '0';
      break;
    }
    $data=$db->FetchArray($result);
    $tid=$data['id'];
    $text =  $db->escape($_POST["text"]);
    $now=time();
    $sql = 'INSERT INTO `m_fot_Tag_Check` (`aid`, `pid`, `uid`, `tid`, `text`, `time_added`) VALUES ( \''.$a_user['id_m'].'\', \''.$pic_id.'\', \''.$id_m.'\', \''.$tid.'\', \''.$text.'\', \''.$now.'\')';
    $result = $db->Query($sql);
    echo "1:".$text;
    break;


  case 7: //rating
    $pic_id = (int) $_POST['pic_id'];
    $rating = (int) $_POST['mark'];

        //check if value is correct
        if (($rating >= 1) AND ($rating <= 5))
        {
            //if this is first rating, insert
            if (db_one_field_query ($db, "SELECT count(*) FROM m_fot_Ratings WHERE user_id='$user_id' AND Pictures_picture_id='$pic_id';", FALSE) == 0)
            {
                //return number of affected rows or false
                db_modification_query ($db, "INSERT INTO m_fot_Ratings (user_id, Pictures_picture_id, rating) VALUES (" . ((string) $user_id) . " ," . ((string) $pic_id) . ", " . ((string) $rating) . ");");

            } else
            {
                //update previous rating
                db_modification_query ($db, "UPDATE m_fot_Ratings SET rating='" .((string) $rating). "' WHERE  user_id='".((string) $user_id)."' AND Pictures_picture_id='".((string) $pic_id)."';");
            }
        }
    
    $rating_a = $db->FetchArray($db->doQuery("SELECT AVG(rating) AS 'avg', COUNT(rating) AS 'count' FROM m_fot_Ratings WHERE Pictures_picture_id='$pic_id';"));
    if (isset($rating_a['count']) AND $rating_a['count'] > 0)
      $rating= "<b>Hodnocení uživatelů:</b> ". sprintf("%1.1f", $rating_a['avg']) . ", hodnotitelů: " . $rating_a['count'];
    else
      $rating= "<b>Hodnocení uživatelů:</b> dosud nehodnoceno";
    
    $user_rating = FALSE;
    if ($user_id != NULL) $user_rating = db_one_field_query ($db, "SELECT rating FROM m_fot_Ratings WHERE user_id='$user_id' AND Pictures_picture_id='$pic_id';", FALSE);
    //display message depnding on state of rating
    if ($user_rating == FALSE)
    {
        $photo_rating="Hodnoťte fotografii (jako ve škole: 1 - líbí se mi, 3 - průměrná, 5 - nelíbí se mi).";
    }else
    {
        //if user rated, display his choice
        $photo_rating="Vaše současné hodnocení je <b>$user_rating</b>, můžete jej změnit (jako ve škole: 1 - líbí se mi, 3 - průměrná, 5 - nelíbí se mi).";
    }
    $rating=str_replace(":", '&#58;', $rating);
    $photo_rating=str_replace(":", '&#58;', $photo_rating);
    echo "1:".$rating.":".$photo_rating;

    break;
  default:
    break;
}
exit;
?>

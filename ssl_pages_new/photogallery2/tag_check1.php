<?php
  $task=(int)$_GET['task'];
  $rid=(int)$_GET['id'];
  switch($task){
    case 1:
      $now=time();
      $sql = 'SELECT * FROM `m_fot_Tag_Check` WHERE id='.$rid;
      $result = $db->Query($sql);
      if($data=$db->FetchArray($result)){
        $sql="UPDATE `m_fot_Tags` SET deleted_on=$now WHERE uid=".$data['uid']." AND pid=".$data['pid']." AND deleted_on=0";
        $db->Query($sql);
        $sql="UPDATE `m_fot_Tag_Check` SET time_solved=$now, solved_by=".$a_user['id_m'].",status=1 WHERE id=".$rid;
        $db->Query($sql);
      }
      break;
    case 2:
      $now=time();
      $sql = 'SELECT * FROM `m_fot_Tag_Check` WHERE id='.$rid;
      $result = $db->Query($sql);
      if($data=$db->FetchArray($result)){
        $sql="UPDATE `m_fot_Tag_Check` SET time_solved=$now, solved_by=".$a_user['id_m'].",status=2 WHERE id=".$rid;
        $db->Query($sql);
      }
      break;
  }
  $sql = 'SELECT * FROM `m_fot_Tag_Check`';//TODO  moznost vyberu od-do, jen nevyresene...
  //die($sql);
  $count=0;
  $result = $db->Query($sql);
  print_line('<table>');
  print_line('<tr><th>Iniciator kontroly</th><th>ID fotky</th><th>ID oznaceneho</th><th>ID oznaceni</th><th>text</th><th>Vyreseno kym</th><th>Vyreseno kdy</th><th>Zadano kdy</th><th>Status</th><th>Moznosti</th></tr>');
  while($data=$db->FetchArray($result)){
    print_line('<tr>');

    $count++;

    print_line('<td>'.$data['aid'].'</td><td><a href="?men=men23.5.0.0&pic_id='.$data['pid'].'">'.$data['pid'].'</a></td><td>'.$data['uid'].'</td><td>'.$data['tid'].'</td><td>'.nl2br($data['text']).'</td><td>'.$data['solved_by'].'</td><td>'.($data['time_solved']==0?0:date("H:i:s j.m.y",$data['time_solved'])).'</td><td>'.date("H:i:s j.m.y",$data['time_added']).'</td><td>'.$data['status'].'</td>');
    if($data['status']==0)print_line('<td><a href="?men=men23.7.1.0&task=1&id='.$data['id'].'">smazat</a> <a href="?men=men23.7.1.0&task=2&id='.$data['id'].'">nechat byt</a> </td>');
    print_line('</tr>');
  }
  print_line('</table>');
?>

<?php
    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                             constant definitions

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

    ////////////////////////////////////////////////////////////////////////////
    //////////////////////////// D E F I N E S /////////////////////////////////
    //path to an ini configuration file
    define ("INI_PATH", "../ssl_pages_new/photogallery2/include/gallery.ini");

    //path to the full size picture data storage (not in database)
    //TO DO: change to an absolute path (otherwise there will be problems with dispaly outside of an intranet)
    // /photo_files/ did not work
    define ("PHOTO_PATH", "../photo_files/");
    define ("THUMBNAIL_PATH", "../photo_files/thumbnails/");	

    //constants for public/private bit
    define ("AC_PRIVATE",   0);
    define ("AC_PUBLIC",    1);

    define ("AC_NOT_YET",   0);
	define ("AC_NOT_APP",  -1);
    define ("AC_APPROVED",  1);



    //errors
    define ("WRONG_ID",     1);
    define ("NO_AUTHORIZATION", 2);
    define ("CANNOT_OPEN_FIE",  3);

    define ("SERVER_PATH", "//{$_SERVER['SERVER_NAME']}"); // $_SERVER['HTTP_HOST'] nefungovalo na HTTPS (obviously)
    define ("VIEW_ALL", true);
    define ("ALBUM_PATH", "/index.php?men=men23.4.0.0&amp;");
    define ("IMAGE_PATH", "/index.php?men=men23.5.0.0&amp;");
    define ("LIST_PATH",  "/index.php?men=men23.4.0.0&amp;");

    ////////////////////////////////////////////////////////////////////////////
    //////////////////////////// G L O B A L S /////////////////////////////////
    //retrieve internal user id
    //stored as public global
    @$user_id        = $a_user["id_m"];

    /* taken from gallery.ini
    define ("THUMB_X", 128);
    define ("THUMB_Y", 96);

    define ("PIC_X", 1600);
    define ("PIC_Y", 1600);
    */

    ////////////////////////////////////////////////////////////////////////////
    //basic function
    function print_line ($line, $indent = 0)
    {
        for ($i = 0;  $i < $indent; $i++) echo "    ";
        echo $line;
        echo "\n";
    }

<?php

/* ////////////////////////////////////////////////////////////////////////////

                          Mensa Photo Gallery
                      all purpose general utilities
                          (c)2006 <PERSON><PERSON><PERSON>dni upravy
    2012-Feb-19, TK:  Drobne zmeny ve vypisu poslednich alb (generate_new_album)


//////////////////////////////////////////////////////////////////////////// */

define("FOTO_DB_DEBUG", false);
define("UPLOAD_ERRORS", array(
    0 => 'Soubor nahrán bez chyby',
    1 => 'Soubor je př<PERSON>li<PERSON> velký (více než ' . ini_get('upload_max_filesize') . ')',
    2 => 'Soubor je p<PERSON><PERSON><PERSON> velk<PERSON>',
    3 => 'Soubor se nahrál jen <PERSON>',
    4 => 'Soubor nebyl nahrán',
    6 => '<PERSON><PERSON><PERSON><PERSON> do<PERSON> složka',
    7 => 'Nezdařil se zápis na disk',
    8 => 'Nahrání souboru bylo přerušeno doplňkem PHP',
));



/**
 * @param mysqli_result $result
 * @return array|false|null
 */
function db_fetch_row($result)
{
    return mysqli_fetch_row($result);
}

/* reads result to array variable[index] */

function db_fetch_array($queryid)
{
    return mysqli_fetch_array($queryid);
}

/* return number of rows */

/**
 * @param mysqli_result $result
 * @return int
 */
function db_num_rows($result)
{
    return $result ? mysqli_num_rows($result) : 0;
}

function db_affected_rows()
{
    // FIXME use parameter
    global $db;

    return mysqli_affected_rows($db->conn);
}

function db_insert_id()
{
    // FIXME use parameter
    global $db;

    return mysqli_insert_id($db->conn);
}

/**
 * @param mysqli_result $result
 */
function db_free_result($result)
{
    mysqli_free_result($result);
}

//////////////////////////////high level functions//////////////////////////////


//runs insert, update, delete query
//returns number of affected rows

function db_modification_query(&$db, $query, $fatal = TRUE)
{
    if ((!($res = $db->Query($query))) AND $fatal) {
        if (FOTO_DB_DEBUG) echo "<h1>SQL Error</h1>\n<p>db.inc.php: Error</p>\n<p>When function db_modification_query(<i>$query</i>) executed the query.</p>";

        die ();
    }

    //if query failed but was not fatal return FALSE

    if (!$res) return FALSE;

    //otherwise return no of affected rows
    return db_affected_rows();
}

////////////////////////////////////////////////////////////////////////////////

//this function executes one-field query end returns the single result
//it returns the first field of first row
//function will cause die, if != rows match the query unles attribute fatal is set to false
//(use LIMIT 1 to restrict result of SQL query if desired)

function db_one_field_query(&$db, $query, $fatal = TRUE)
{
    if ((!$res = $db->Query($query)) AND $fatal) {
        if (FOTO_DB_DEBUG) echo "<h1>SQL Error</h1>\n<p>db.inc.php: Error: " . $db->error . "</p>\n<p>When function db_one_field_query(<i>$query</i>) executed query.</p>";
        die ();
    }

    if ($res == FALSE)
        return FALSE;

    if (db_num_rows($res) != 1) {
        //decide if this is fatal or not

        if ($fatal) {
            if (FOTO_DB_DEBUG) echo "<h1>SQL Error</h1>\n<p>db.inc.php:  Function db_one_field_query() expects, that query:<i>" . $query . "</i> returns 1 row, but " . $db->getNumRows($res) . "were returned!</p>";
            die ();

        } else {
            if (FOTO_DB_DEBUG) echo "<!-- SQL Error: db.inc.php: Function db_one_field_query() expects, that query:" . $query . " returns 1 row, but " . $db->getNumRows($res) . "were returned! -->\n";

            return FALSE;
        }
    }

    $value = db_fetch_row($res);
    db_free_result($res);

    //return only one field
    return $value[0];
}

////////////////////////////////////////////////////////////////////////////////

//this is complex function returning result array for any 1 row db query////////
//if there are more rows in the result, function will fail with error.
//use LIMIT 1 in your SQL statement
//returns false if no row was found

function db_query1row_query(&$db, $query_string, $suppress_warnings = FALSE)
{
    //execute query

    if (!($result = $db->Query($query_string))) {
        //fatal error

        if (FOTO_DB_DEBUG) {
            echo "db.inc.php: db_query1row_query(<i>$query_string</i>) <br />\n";
            echo "<h1>SQL Error</h1>\n<p>Error: " . $db->error . "</p>\n<p>while executing the query.</p>";
        }
        die ();
    }

    //check that only one row was returned

    if (db_num_rows($result) != 1) {
        //display warning if allowed

        if (!$suppress_warnings) {
            if (FOTO_DB_DEBUG) echo "\n<!-- db.inc.php: db_query1row_query($query_string): warning - function expects that query returns 1 row, but " . mysqli_num_rows($result) . " rows were returned. -->\n";
        }
        return FALSE;
    }

    $output = db_fetch_array($result);

    db_free_result($result);

    return $output;
}

////////////////////////////////////////////////////////////////////////////

//expects form to be established

function display_checkbox($id, $label = "", $indent = 0, $checked = FALSE)
{
    $chk = $checked ? "checked='checked'" : "";

    print_line("<p class='input'>", $indent);
    print_line("<input id='$id' name='$id' type='checkbox' $chk />", $indent + 1);
    print_line("<label for='$id'>$label</label>", $indent + 1);
    print_line("</p>", $indent);
}

//expects form to be established

function display_text_field($id, $label = "", $size = 30, $indent = 0, $default = "")
{
    print_line("<p class='input'><strong>$label</strong><br />", $indent);
    print_line("<input id='$id' name='$id' type='text' size='$size' value='$default' />", $indent + 1);
    print_line("</p>", $indent);
}

//expects form to be established

function display_text_area($id, $label = "", $columns = 20, $rows = 5, $indent = 0, $default = "")
{
    print_line("<p class='input'><strong>$label</strong><br />", $indent);
    print_line("<textarea id='$id' name='$id' cols='$columns' rows='$rows'>$default</textarea>", $indent + 1);
    print_line("</p>", $indent);
}

//expects form to be established

//'personal','mensa'

function cathegory_selector($id, $label = "", $indent = 0, $default = 'mensa')
{
    print_line("<p class='input'><strong>$label</strong><br />", $indent);
    print_line("<select id='$id' name='$id'>", $indent + 1);
    print_line("<option value='mensa'" . (($default == 'mensa') ? " selected='selected'" : "") . ">intranet &amp; web</option>", $indent + 2);
    print_line("<option value='personal'" . (($default == 'personal') ? " selected='selected'" : "") . ">jen intranet</option>", $indent + 2);
    print_line("</select>", $indent + 1);
    print_line("</p>", $indent);
}

//expects form to be established

//'name','created'

function ordering_selector($id, $label = "", $indent = 0, $default = 'name')
{
    print_line("<p class='input'><strong>$label</strong><br />", $indent);
    print_line("<select id='$id' name='$id'>", $indent + 1);
    print_line("<option value='name'" . (($default == 'name') ? " selected='selected'" : "") . ">jméno</option>", $indent + 2);
    print_line("<option value='created'" . (($default == 'created') ? " selected='selected'" : "") . ">datum</option>", $indent + 2);
    print_line("</select>", $indent + 1);
    print_line("</p>", $indent);
}

//generate list of all puictures

function thumbnail_list(&$db, $id, $label = "Thumbnail List", $user_id = 0, $album_id = 0, $indent = 0, $default = FALSE)
{
    //security check

    $user_id = (int)$user_id;
    $album_id = (int)$album_id;

    //at first make the query - or abort if there is problem

    //query for images of this user

    $query = "SELECT p.picture_id AS 'picture_id', p.name AS 'name' FROM mensaweb.m_fot_Pictures p JOIN mensaweb.m_fot_Albums a ON (p.belongs_to_album_id = a.album_id) WHERE a.owner_id='" . ((string)$user_id) . "' AND a.album_id='" . ((string)$album_id) . "';";

    $result = $db->Query($query);

    //check if there are any rows..

    //abort if no

    if (db_num_rows($result) == 0) {
        return FALSE;
    }

    print_line("<p class='input'><strong>$label</strong><br />", $indent);

    if ($default) print_line("<img class='thumbnail' src='" . SERVER_PATH . "/m_fot_show_picture2.php?pic_id=$default&amp;type=thumbnail' alt='Thumbnail for albmu $album_id' /><br />", $indent);

    print_line("<select id='$id' name='$id'>", $indent + 1);

    while ($row = db_fetch_array($result)) {
        print_line("<option value='" . ((string)$row['picture_id']) . "'" . (($default == $row['picture_id']) ? " selected='selected'" : "") . ">" . $row['name'] . "</option>", $indent + 2);
    }

    db_free_result($result);

    print_line("</select>", $indent + 1);
    print_line("</p>", $indent);

    return TRUE;
}

//creates select album with list of albums for given user

//expects form to be established

//returns true if there are any albums to select and false otherwise

function album_list_box(&$db, $name, $user_id, $default_id = 0, $indent = 0)
{

    //security check

    $user_id = (int)$user_id;

    //at first query for albums

    $query = "SELECT album_id, name, cathegory FROM m_fot_Albums WHERE owner_id='" . ((string)$user_id) . "' ORDER BY created DESC;";
    $result = $db->Query($query);

    //check if there are any rows..

    //abort if no

    if (db_num_rows($result) == 0) {
        db_free_result($result);

        return FALSE;
    }

    print_line("<p class='input'>", $indent);
    print_line("<strong>Vyberte album do kterého fotografie vložíte</strong><br />", $indent + 1);
    print_line("<select name='$name'>", $indent + 1);

    while ($row = db_fetch_array($result)) {
        $checked = ($default_id == $row['album_id']) ? "selected='selected'" : "";

        print_line("<option value='" . $row['album_id'] . "' $checked>" . $row['name'] . " (" . (($row['cathegory'] == 'personal') ? "intranet" : "web") . ")</option>", $indent + 2);
    }

    print_line("</select>", $indent + 1);
    print_line("</p>", $indent);

    db_free_result($result);

    return TRUE;
}

////////////////////////////////////////////////////////////////////////////

//this functions generates part of the form to upadate data for one image

//expect form to be established

function generate_image_description_form(&$db, $image_id, $picture_number, $target_album, $album_owner_id, $thumb_x = 128, $thumb_y = 96)
{
    //get picture data from database

    $image = db_query1row_query($db, "SELECT name, comment, public FROM m_fot_Pictures WHERE picture_id='$image_id';");

    print_line("<div id='property-form-$image_id'>", 2);
    print_line("<h3>Fotografie " . $image['name'] . "</h3>", 3);

    //display thumbnail

    print_line("<a href='./m_fot_show_picture2.php?pic_id=$image_id&amp;type=full' target='_blank'><img class='right' border='0' src='" . SERVER_PATH . "/m_fot_show_picture2.php?pic_id=$image_id&amp;type=resized&amp;max_x=330&amp;max_y=220' alt='Obrázek číslo: $image_id' /></a>", 3);

    //data for easier saving - file recognition

    print_line("<input type='hidden' id='picture-$picture_number' name='picture-$picture_number' value='$image_id' />", 3);

    //target album (default from picture)

    album_list_box($db, "belongs_to_album_id-$image_id", $album_owner_id, $target_album, 3);

    //display forms - public?

    display_checkbox("public-$image_id", "veřejně přístupné", 3, (string)$image['public']);
    display_text_field("name-$image_id", "název", 50, 3, $image['name']);
    display_text_area("comment-$image_id", "komentář", 40, 3, 3, $image['comment']);

    print_line("</div>", 2);
}

////////////////////////////////////////////////////////////////////////////

//should provide html entity escaping and special chars escaping (sql injection protection, page layout protection
//it also oes character encoding from post get cookie format to internal
//max len - if 0 then no limit

function check_user_input($input_string, $max_len = 0, $pos_get_cookie = TRUE)
{
    //check len

    if (($max_len != 0) AND (mb_strlen($input_string) > $max_len)) {
        //substr ( string string, int start [, int length] )

        $input_string = substr($input_string, 0, $max_len);
    }

    //need to check for magic quotes

    //The PHP directive magic_quotes_gpc is on by default, and it essentially runs addslashes() on all GET, POST, and COOKIE data.

    //1 on, 0 off

    if ((get_magic_quotes_gpc() == 1) AND $pos_get_cookie) {
        //if we have magic quotes and input are post, get cookie data, do not add slashes
        return strip_tags($input_string);
    } else {
        //do not convert quotes to html entities! addslashes will escape them

        return addslashes(htmlspecialchars($input_string, ENT_NOQUOTES));
    }
}

/////////////////////////////////////////////////////////////////////////

//gets an id of random public image

function generate_random_image_id(&$db, $all = true)
{
    if ($all) {
        $ot = "";
    } else {
        $ot = " AND approved = 1";
    }

    $count = db_one_field_query($db, "SELECT count(*) FROM m_fot_Pictures WHERE public='1' $ot;", FALSE);
    $ord = mt_rand(0, $count - 1);

    return db_one_field_query($db, "SELECT picture_id FROM m_fot_Pictures WHERE public='1' $ot LIMIT $ord,1;", FALSE);
}

////////////////////////////////////////////////////////////////////////////

//gets an id of random public image

function generate_new_album(&$db, $pocet = 3)
{
    $tmp = $db->Query("
            SELECT f.*, DATE_FORMAT(f.created, '%e. %c. %Y') AS created2, m.jmeno, m.prijmeni 
            FROM m_fot_Albums AS f INNER JOIN m_members AS m ON m.id_m=f.owner_id 
            WHERE album_id IN (SELECT DISTINCT belongs_to_album_id FROM m_fot_Pictures) 
            ORDER BY f.created desc 
            LIMIT $pocet");

    $ret = "<table border=\"0\" cellspacing=\"5\"><tr>";

    while ($row = mysqli_fetch_array($tmp)) {
        // najdi titulni obrazek, pokud neni ddefinovan

        if ($row["thumbnail_picture_id"] == 0) {
            $tmp2 = $db->Query("SELECT picture_id FROM m_fot_Pictures WHERE belongs_to_album_id=" . $row["album_id"] . " LIMIT 1");
            $row["thumbnail_picture_id"] = $db->getResult($tmp2, 0, "picture_id");
        }

        $ret .= "<td class=\"small_text\" valign=\"top\">";
        $ret .= "<a href=\"index.php?men=men23.4.0.0&album_id=" . $row["album_id"] . "\"><img src=\"m_fot_show_picture2.php?pic_id=" . $row["thumbnail_picture_id"] . "&type=thumbnail\"  width=\"128\" border=\"0\" ></a><br>";
        $ret .= "<strong>" . $row["name"] . "</strong><br>";
        $ret .= "<strong>Autor:</strong> " . $row["jmeno"] . " " . $row["prijmeni"] . "<br>";
        $ret .= "<strong>Datum:</strong> " . $row["created2"] . "<br>";
        $ret .= "</td>";

    }

    $ret .= "</tr></table>";

    return $ret;
}


<?php
 	//basic constants and print line function
  	require_once ("../ssl_pages_new/photogallery2/include/constants.inc.php");
		
	//all purpose utilities
	require_once ("../ssl_pages_new/photogallery2/include/general.inc.php");
		
    //entry stub for album_modify.php
 
    //debug
    //create fake get data...
    //if no other data were detected
    if (!isset($_GET['pic_id']))
    {
        $random_id = generate_random_image_id ($db);
        if ($random_id)
        {
            print_line ("<p class='message'>Náhodná fotografie</p>");
            $_GET['pic_id'] = $random_id;
        }
    }

    //link to the page with content
    if (isset($_GET['action']))
    {
        if (($_GET['action'] == "edit") and (isset($_GET['pic_id'])))
        {
            //get modify page and quit here
			echo "<!-- debug: loading modification page, performing action '" . $_GET['action'] . "' for picture id.: " . $_GET['pic_id'] . ". -->";
            require ("../ssl_pages_new/photogallery2/photo_modify.php");
            return;
        }
    }

    //otherwise display
    require ("../ssl_pages_new/photogallery2/photo_view.php");

?>

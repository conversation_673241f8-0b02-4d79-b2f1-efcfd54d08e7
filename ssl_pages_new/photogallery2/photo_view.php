<?php

    /*
    ////////////////////////////////////////////////////////////////////////////

                              Mensa Photo Gallery
                               displays one photo

                              (c)2006 <PERSON><PERSON><PERSON>

    ////////////////////////////////////////////////////////////////////////////
    */

    ////////////////////////////////////////////////////////////////////////////
    //functions


    //displays rating form
    function display_rating_form (&$db, $pic_id)
    {
        global $user_id;

        //check if this user have not rated this picture already
        //if no rating exists, return FALSE
        $user_rating = FALSE;
        if ($user_id != NULL) $user_rating = db_one_field_query ($db, "SELECT rating FROM m_fot_Ratings WHERE user_id='$user_id' AND Pictures_picture_id='$pic_id';", FALSE);


        print_line ("<div id='photo_rating'>",1);
        print_line ("<form id='rating_form' method='post' action='./index.php?men=men23.5.0.0&amp;pic_id=$pic_id'>",2);//TODO ajax

        //display message depnding on state of rating
        if ($user_rating == FALSE)
        {
            print_line ("<p  class='rating_form' id='user_rating'>Hodnoťte fotografii (jako ve škole: 1 - líbí se mi, 3 - průměrná, 5 - nelíbí se mi).</p>", 3);

        }else
        {
            //if user rated, display his choice
            print_line ("<p class='rating_form' id='user_rating'>Vaše současné hodnocení je <b>$user_rating</b>, můžete jej změnit (jako ve škole: 1 - líbí se mi, 3 - průměrná, 5 - nelíbí se mi).</p>", 3);
        }

        print_line ("<p class='rating_form'>",3);
        print_line ("<input type='submit' name='rating' value='1' onclick='return Rating(1);' />&nbsp;",4);
        //print_line ("<input type='submit' name='rating' value='2' />&nbsp;",4);
        print_line ("<input type='submit' name='rating' value='3' onclick='return Rating(3);' />&nbsp;",4);
        //print_line ("<input type='submit' name='rating' value='4' />&nbsp;",4);
        print_line ("<input type='submit' name='rating' value='5' onclick='return Rating(5);' />&nbsp;",4);
        print_line ("</p>",3);
        print_line ("</form>",2);

        print_line ("</div>", 1);
    }


    //saves rating recieved from post data
    function save_rating (&$db, $pic_id)
    {
         global $user_id;

        $rating = (int) $_POST['rating'];

        //check if value is correct
        if (($rating >= 1) AND ($rating <= 5))
        {
            //if this is first rating, insert
            if (db_one_field_query ($db, "SELECT count(*) FROM m_fot_Ratings WHERE user_id='$user_id' AND Pictures_picture_id='$pic_id';", FALSE) == 0)
            {
                //return number of affected rows or false
                db_modification_query ($db, "INSERT INTO m_fot_Ratings (user_id, Pictures_picture_id, rating) VALUES (" . ((string) $user_id) . " ," . ((string) $pic_id) . ", " . ((string) $rating) . ");");

            } else
            {
                //update previous rating
                db_modification_query ($db, "UPDATE m_fot_Ratings SET rating='" .((string) $rating). "' WHERE  user_id='".((string) $user_id)."' AND Pictures_picture_id='".((string) $pic_id)."';");
            }
        }

    }

  //displays a link to left image
  function display_goto_left (&$db, $ord_1col_name, $ord_1value, $album_id,$id_m,$picture_id)
  {
    global $user_id;

    //determine if non public images should be allowed
    //skip not-public images
    $pub = ($user_id == NULL)?("AND public=".((string)AC_PUBLIC)." AND approved=".((string)AC_APPROVED)):"";
    if($id_m==0) {
      $query = "SELECT picture_id, name FROM m_fot_Pictures WHERE $ord_1col_name <= '$ord_1value' AND belongs_to_album_id='$album_id' AND picture_id < '$picture_id' $pub ORDER BY $ord_1col_name DESC, picture_id ASC LIMIT 1;";
      $idm="";
    } else {
      $query = "SELECT p.picture_id, p.name FROM m_fot_Pictures p, m_fot_Tags t WHERE $picture_id>t.pid AND t.uid=$id_m AND t.pid=p.picture_id AND t.deleted_on=0 ORDER BY pid DESC LIMIT 1 $pub ";
      $idm="&id_m=$id_m";
    }
    $prev_pic_info = db_query1row_query ($db, $query);
    if (!$prev_pic_info) {
      echo ("<span class='links' align='left'>Začátek alba.</span>");
    } else {
      echo ("<a href='#".$prev_pic_info['picture_id']."-$id_m'><img class='arrow' src='/images/fotogalerie/left.png' alt='".$prev_pic_info['name']."' title='".$prev_pic_info['name']."'/></a>");
    }
  }


  function display_goto_right (&$db, $ord_1col_name, $ord_1value, $album_id, $id_m, $picture_id)
  {
    global $user_id;

    //determine if non public images should be allowed
    //skip not-public images
    $pub = ($user_id == NULL)?("AND p.public=".((string)AC_PUBLIC)." AND p.approved=".((string)AC_APPROVED)):"";
    if($id_m==0){
      $query = "SELECT picture_id, name FROM m_fot_Pictures p WHERE $ord_1col_name >= '$ord_1value' AND belongs_to_album_id='$album_id' AND picture_id > '$picture_id' $pub ORDER BY $ord_1col_name ASC, picture_id ASC LIMIT 1;";
      $idm="";
    } else {
      $query = "SELECT p.picture_id, p.name FROM m_fot_Pictures p, m_fot_Tags t WHERE $picture_id<t.pid AND t.uid=$id_m AND t.pid=p.picture_id AND t.deleted_on=0 ORDER BY pid ASC LIMIT 1 $pub ";
      $idm="&id_m=$id_m";
    }
    $next_pic_info = db_query1row_query ($db, $query);
    if (!$next_pic_info)
    {
      echo ("<span class='links'>Konec alba.</span>");
    } else {
      echo ("<a href='#".$next_pic_info['picture_id']."-$id_m'><img class='arrow' src='/images/fotogalerie/right.png' alt='".$next_pic_info['name']."' /></a>");
    }

  }


  function display_info (&$db, $picture_id, $picture_info)
  {
    global $user_id;

    print_line ("<div id='picture_info'>");

    //get author name
    $user_info = db_query1row_query ($db, "SELECT * FROM m_members WHERE id_m = '" . $picture_info['owner'] . "';", TRUE);
    //display author
    print_line ("<p class='picture_info' id='author_'><b>Autor:</b> ".  $user_info['jmeno'] . " " . $user_info['prijmeni']."</p>");
    //print_line ("<p id='author'>Autor: $owner_id</p>");

    //display comment
    print_line ("<p class='picture_info' id='views_'><b>Zobrazení:</b> " . $picture_info['views'] . "</p>");

  if (VIEW_ALL) {
    //display total rating
    $rating = db_query1row_query ($db, "SELECT AVG(rating) AS 'avg', COUNT(rating) AS 'count' FROM m_fot_Ratings WHERE Pictures_picture_id='$picture_id';", FALSE);
    if (isset($rating['count']) AND $rating['count'] > 0)
        print_line ("<p class='picture_info' id='rating_'><b>Hodnocení uživatelů:</b> ". sprintf("%1.1f", $rating['avg']) . ", hodnotitelů: " . $rating['count'] . "</p>");
    else
        print_line ("<p class='picture_info' id='rating_'><b>Hodnocení uživatelů:</b> dosud nehodnoceno</p>");
  }

  print_line ("<p class='picture_info' id='date_'><b>Vloženo:</b> " . date("j. M Y, G:i:s" , $picture_info['unix_created']) . "</p>");

  //display comment
  print_line ("<p class='picture_info' id='comment'><b>Komentář:</b> " . (($picture_info['comment']!="")?$picture_info['comment']:"-") . "</p>");

  print_line ("</div>");
  }




  ////////////////////////////////////////////////////////////////////////////
  ////////////////////////////////////////////////////////////////////////////


  //check for pic id
  if (!isset ($_GET['pic_id']))
  {
    print_line ("<h2>Nedostal jsem indentifikátor obrázku!</h2>");
    print_line ("<p>To může být způsobeno vnitřní chybou systému nebo tím, že se snažíte prohlížet obrázek mimo intranet.</p>");

    //stop execution of this script
    return;
  }

    //get picture id (force the int to prevent malicious data)
    $picture_id = (int) $_GET['pic_id'];
    $id_m=(int) @$_GET['id_m'];


    ////////////////////////////////////////////////////////////////////////////
    //evaluate rating - check if we have received rating
    if ((isset ($_POST['rating'])) AND ($user_id != NULL))
    {
        //todo - check if user is not picture owner
        save_rating ($db, $picture_id);
    }


    ////////////////////////////////////////////////////////////////////////////
    //need to do it now to display header properly
    //get information about the picture
    /*
        CREATE TABLE `Pictures` (
            `picture_id` mediumint(8) unsigned NOT NULL auto_increment,
            `public` tinyint(1) NOT NULL default '0',
            `name` varchar(50) collate latin2_czech_cs default 'fotka',
            `comment` varchar(255) collate latin2_czech_cs default NULL,
            `created` datetime NOT NULL default '0000-00-00 00:00:00',
            `image_data` mediumblob NOT NULL,
            `image_thumbnail` blob NOT NULL,
            `views` mediumint(8) unsigned NOT NULL default '0',
            `belongs_to_album_id` smallint(5) unsigned NOT NULL default '0',
            PRIMARY KEY  (`picture_id`),
            KEY `FK_Picture_1` (`belongs_to_album_id`)
        ) ENGINE=MyISAM DEFAULT CHARSET=latin2 COLLATE=latin2_czech_cs AUTO_INCREMENT=1 ;
    */
    $picture_info = db_query1row_query ($db, "SELECT p.name as 'name', p.comment as 'comment', p.public as 'public', p.created, UNIX_TIMESTAMP(p.created) AS 'unix_created', views, belongs_to_album_id, a.owner_id AS 'owner', a.ordering AS 'ordering', a.name as 'a_name', album_id FROM mensaweb.m_fot_Albums a JOIN mensaweb.m_fot_Pictures p ON (p.belongs_to_album_id=a.album_id) WHERE picture_id='$picture_id';");

    //outputs html page header
    //output_header (AC_PUBLIC, "Fotografie - " . $picture_info['name'], "Fotografie " . $picture_info['name'] . ", popis: " . $picture_info['comment']);

    //check proper access rights
    if (($picture_info['public'] == AC_PRIVATE) AND ($user_id == NULL))
    {
        print_line ("<h2>Nemáte oprávnění k prohlížení této fotografie.</h2>");
        print_line ("<p>Tato fotografie je přístupná pouze přihlášeným uživatelům.</p>");
        print_line ("<p><a href='./login.php'>Přihlásit se!</a></p>");
        //die
        //output_footer ();
        //return from this script
        exit;
    }


  //  print_line ("</td><td align='center' width='14%'>" , 1);

  //print_line ('<div id="log" style="border: 1px solid red;z-index:1;background-color:blue; position: fixed; top: 120pt; left: 820pt;"> </div>');
  print_line ('<script type="text/javascript">');
  print_line (' var photo_id="'.$picture_id.'";');
  print_line (' var photo_id_orig="'.$picture_id.'";');
  print_line (' var id_m="'.$id_m.'";');
  print_line (' var id_m_orig="'.$id_m.'";');
  print_line (' var user_id='.$a_user['id_m'].';');
  print_line (' var photo_full="";');
  print_line ('</script>');
  print_line ('<script src="js/jquery-1.4.2.min.js" type="text/javascript"></script>');
  print_line ('<script src="js/jquery.history.js" type="text/javascript"></script>');
  print_line ('<script src="js/m_fot.js" type="text/javascript"></script>');

  print_line ("<div id='photo_gallery'>");
	print_line ("<table border='0' width='100%'><tr><td width='43%'>");
  print_line ("<b id='name'>".$picture_info['name']."</b>", 1);
  print_line ("</td><td align='center' width='14%'>" , 1);
  print_line ("<a id='tag_click' href='javascript:tag();'>Označit</a>" , 1);
	print_line ("</td><td align='right' width='43%' id='album'>" , 1);
	if($id_m!=0){
    $sql="SELECT jmeno, prijmeni, prezdivka FROM m_members WHERE id_m = '" . $id_m. "';";
    $res=$db->doQuery($sql);
    if($data=$db->FetchArray($res)){
      $name=$data['jmeno'].' '.$data['prijmeni'];
    }else{//wrong id_m, ignoring
      $id_m=0;
    }
  }
  if($id_m==0){
    print_line ("<strong>Album:</strong> <a href='".ALBUM_PATH."album_id=".$picture_info['album_id']."'>".$picture_info['a_name']."</a>", 1);
  }else{
    print_line ("<strong>Fotografie uživatele <a href='/index.php?men=men4.1.0.0&s_content=view.i&id_m=$id_m'>$name</a> z alba:</strong> <a href='".ALBUM_PATH."album_id=".$picture_info['album_id']."'>".$picture_info['a_name']."</a>", 1);
  }
	print_line ("</td></tr></table>");

    ////////////////////////////////////////////////////////////////////////////
    //get prev and next picture - determine ordering
    //point we need to sort using either name or created columns, we need to find current value of both
	//we need to trim the values - some hidden characters were casing huge problems in name sorting
    $ord_1col_name   = ($picture_info['ordering'] == 'name')?"TRIM(name)":"created";
    $ord_1value      = ($picture_info['ordering'] == 'name')?trim($picture_info['name']):$picture_info['created'];



    ////////////////////////////////////////////////////////////////////////////
    //display picture with link to the full version
    print_line ("\n\n<div class='display-photo'><div class='links' id='prev'>");
    /*
    print_line ("<noscript>",1);//NOSCRIPT
    print_line ("<a href='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$picture_id&amp;type=full' target='_blank'>",2);
    print_line ("<img id='photo_img' class='photo' border='0' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$picture_id&amp;type=resized&amp;max_x=682&amp;max_y=500' alt='Obrázek číslo: $picture_id - " . $picture_info['name'] . "' />",2);
    print_line ("</a>",2);
    print_line ("</noscript>",1);//NOSCRIPT end
*/
    display_goto_left ($db, $ord_1col_name, $ord_1value, $picture_info['belongs_to_album_id'], $id_m, $picture_id);
    print_line('</div>');
    echo ("<div id='photo' style='text-align: center' height='520px' width='700px'><a name='photo' id='photo_a' href='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$picture_id&amp;type=full' target='_blank'>");
    print_line ("<img onClick='clickImg(event);' onmousemove='imgOver(event);' id='photo_img' class='photo' border='0' src='".SERVER_PATH."/m_fot_show_picture2.php?pic_id=$picture_id&amp;type=resized&amp;max_x=682&amp;max_y=500' alt='Obrázek číslo: $picture_id - " . $picture_info['name'] . "' /></a></div>",1);
    echo "<div class='links' id=next>";
    display_goto_right ($db, $ord_1col_name, $ord_1value, $picture_info['belongs_to_album_id'], $id_m, $picture_id);

    print_line ("</div></div>\n\n");

    print_line("<img id='tag_box' onmousemove='imgOver(event);' style='position: absolute; display: none; top: 0px; left: 0px; border: 5px coral solid;' src='/images/fotogalerie/box.png'>");//TODO naformatovat
    print_line("</div>");
    print_line("<div id='tag_box_name' onmousemove='imgOver(event);' style='position: absolute; display: none; top: 0px; left: 0px; background-color:coral;'>");//TODO naformatovat
    print_line("</div>");

    print_line("<div id='report' style='position: absolute; display: none; top: 0px; left: 0px; border: 3px coral solid; width: 230px; background-color:blue;'>");//TODO naformatovat
    print_line('Tento tag jste nevložili vy, pokud jste přesvědčeni, že je špatně a má být odstraněn, napište důvod.',1);
    print_line('<textarea id="report_text" rows="2" cols="20"></textarea>', 1);
    print_line("<input type='submit' onclick='reportTag();' value='Nahlásit'/>",1);
    print_line("<input type='submit' onclick='hideReport();' value='Zrušit'/>",1);
    print_line("</div>");

    print_line("<div id='name_box' style='position: absolute; display: none; top: 0px; left: 0px; border: 3px coral solid; width: 230px; background-color:blue;'>");//TODO naformatovat
    print_line("Jméno: <input type='text' id='name_' onkeyup='changeName();'/><br />", 1);
    print_line("Příjmení: <input type='text' id='surn_' onkeyup='changeName();'/><br />", 1);
    print_line("Přezdívka: <input type='text' id='nick_' onkeyup='changeName();'/><br />", 1);
    print_line("<div id='boxd'><select size='4' style='width: 200px;'></select></div><br />", 1);
    print_line("<input type='submit' onclick='sendTag();' value='Označit'/>",1);
    print_line("<input type='submit' onclick='cancelBox();' value='Zrušit'/>",1);
    print_line("</div>");

    print_line("<div id='tagged_list'>");
    print_line("</div>");

    ////////////////////////////////////////////////////////////////////////////
    //rating - display rating form if user is logged in
    if (($user_id != NULL) AND ($user_id != $picture_info['owner']))
        display_rating_form ($db, $picture_id);




    ////////////////////////////////////////////////////////////////////////////
    //navigation menu
    print_line ("<table width='90%' class='m_fot_list' id='photo_navigation'>");
    print_line ("<tr>", 1);

    print_line ("<td valign='top'>", 2);
    //info about picture
    display_info ($db, $picture_id, $picture_info);
    print_line ("</td>", 2);


    print_line ("<td valign='top'>", 2);
    print_line ("<div id='central_navigation'>",2);
    print_line ("<ul class='m_fot_links'>",2);
	if (VIEW_ALL) {
    echo "<div id='upravit_vlastnosti'>";
    if ($user_id == $picture_info['owner']) print_line ("<li><a href='index.php?men=men23.5.0.0&amp;action=edit&amp;pic_id=$picture_id'>Upravit vlastnosti obrázku nebo jej smazat</a></li>",3);
    echo "</div>";
    print_line ("<li><a href='./index.php?men=men23.4.0.0&amp;album_id=" . $picture_info['belongs_to_album_id'] . "#image-$picture_id'>Zpět na album</a></li>",3);
    print_line ("<li><a href='./index.php?men=men23.4.0.0'>Zpět na přehled alb</a></li>",3);
	}
    print_line ("</ul>", 2);
    print_line ("</div>", 2);
    print_line ("</td>", 2);


    print_line ("</tr>", 1);
    print_line ("</table>", 1);



    print_line ("</div>");



?>


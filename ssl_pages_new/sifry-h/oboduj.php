Deskohrátky - <PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON><PERSON>š<PERSON>lů:

<?PHP
function write_date($date, $format=""){ //2005-09-28 12:21:20
	$y = mb_substr($date, 0, 4);
	$m = mb_substr($date, 5, 2);
	$d = mb_substr($date, 8, 2);
	$h = mb_substr($date, 11, 2);
	$min = mb_substr($date, 14, 2);
	$sec = mb_substr($date, 17, 2);
	
	$ret = $d.".".$m.".".$y." ".$h.".".$min.".".$sec;
	return $ret;
}

if (!isset($kolo)){
	$kolo = 1;
}
if (!isset($order_by)){
	$order_by = "celkem";
}
if ($order_by == "email"){
	 $ORDER_BY = " ORDER BY lidi.email ";
} elseif ($order_by == "prijmeni"){ 
	 $ORDER_BY = " ORDER BY lidi.prij<PERSON>, lidi.jmeno ";
} elseif ($order_by == "celkem"){ 
	 $ORDER_BY = " ORDER BY celkem desc ";
} else {
	 $ORDER_BY = " ORDER BY $order_by desc ";
}
 $SQL = "SELECT lidi.jmeno, lidi.prijmeni, lidi.mesto, 
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m  GROUP BY id_m) AS celkem,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=1 GROUP BY id_m) AS body1,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=2 GROUP BY id_m) AS body2,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=3 GROUP BY id_m) AS body3,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=4 GROUP BY id_m) AS body4,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=5 GROUP BY id_m) AS body5,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=6 GROUP BY id_m) AS body6,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=7 GROUP BY id_m) AS body7,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=8 GROUP BY id_m) AS body8,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=9 GROUP BY id_m) AS body9,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=10 GROUP BY id_m) AS body10,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=11 GROUP BY id_m) AS body11,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=12 GROUP BY id_m) AS body12, 
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=13 GROUP BY id_m) AS body13,
			(SELECT sum(body) FROM www_sifra_reseni_h WHERE id_m=lidi.id_m AND kolo=14 GROUP BY id_m) AS body14
			FROM www_sifra_lidi_h AS lidi  $ORDER_BY";
$rs = $db -> Query($SQL);
$old_email = "";
if ($db->getNumRows($rs) > 0){
	while ($radek = $db->FetchArray($rs)){
		$email = $radek["email"];
		$jmeno = $radek["jmeno"]." ".$radek["prijmeni"];
		$adresa = $radek["ulice"]." ".$radek["mesto"]." ".$radek["psc"];
		$ret .=  "	<td style=\"font-size: 10px;\">".$radek["jmeno"]."&nbsp;</td>
					<td style=\"font-size: 10px;\">".$radek["prijmeni"]."&nbsp;</td>
					<td style=\"font-size: 10px;\">".$radek["mesto"]."&nbsp;</td>
					<td style=\"font-size: 10px;\" align=\"right\">".$radek["celkem"]."&nbsp;</td>";
		for ($i = 1; $i<15; $i++){
			$ret .=  "			<td style=\"font-size: 10px;\" align=\"right\">".$radek["body".$i]."&nbsp;</td>";
		}
		$ret .=  "			</tr>";
	}
}
 ?>

<form action="<?PHP echo $PHP_SELF?>?men=<?PHP echo $men;?>" enctype="multipart/form-data" method="post" name="oboduj">
<select name="order_by">
<option value="prijmeni"<?PHP if ($order_by == "prijmeni"){echo " selected";}; ?>>příjmení</option>
<option value="email"<?PHP if ($order_by == "email"){echo " selected";}; ?>>email</option>
<option value="celkem"<?PHP if ($order_by == "celkem"){echo " selected";}; ?>>body celkem</option>
<?PHP for($i = 1; $i < 15; $i++){ 
		if ($order_by == "body".$i ){
			$selected = " selected";
		} else {
			$selected = "";
		}
		echo "<option value=\"body".$i."\"$selected>body $i. kolo</option>\n";
	 } ?>
</select> <input type="submit" value="Zobrazit" name="zobrazit">
<table border="1" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF">
<tr><td style="font-size: 10px;"><strong>jméno</strong></td>
<td style="font-size: 10px;"><strong>příjmení</strong></td>
<td style="font-size: 10px;"><strong>město</strong></td>
<td style="font-size: 10px;"><strong>Celkem</strong></td>
<?PHP for ($i = 1; $i < 15; $i++){ 
			echo'<td style="font-size: 10px;"><strong>'.$i.'. kolo</strong></td>';
 		} ?>
</tr>
<?PHP echo $ret; ?>
</table> 
</form>

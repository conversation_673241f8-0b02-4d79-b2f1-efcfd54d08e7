V<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> -
<?PHP
function write_date($date, $format=""){ //2005-09-28 12:21:20
	$y = substr($date, 0, 4);
	$m = substr($date, 5, 2);
	$d = substr($date, 8, 2);
	$h = substr($date, 11, 2);
	$min = substr($date, 14, 2);
	$sec = substr($date, 17, 2);
	
	$ret = $d.".".$m.".".$y." ".$h.".".$min.".".$sec;
	return $ret;
}

$ret = "";
 $SQL = "SELECT max(kolo) as k FROM www_sifra_reseni_h  ";
	$rs = $db->Query($SQL);
	$maxkolo = $db->getResult($rs, 0, "k");
if (!isset($kolo)){ 
	$kolo = $maxkolo; 
}
if (!isset($order_by)){
	$order_by = "email";
}
if ($order_by == "email"){
	 $ORDER_BY = " ORDER BY lidi.vlozeno, lidi.email, reseni.vlozeno";
} elseif ($order_by == "vlozeno"){ 
	 $ORDER_BY = " ORDER BY reseni.vlozeno";
} elseif ($order_by == "splneno"){ 
	 $ORDER_BY = " AND reseni.splneno=1 ORDER BY reseni.vlozeno";
}

if (isset($odeslat_res)){
	 $SQL = "SELECT reseni.id_r 
				FROM www_sifra_reseni_h AS reseni WHERE reseni.kolo = $kolo";
	$rs = $db->Query($SQL);
	if ($db->getNumRows($rs) > 0){
		while ($radek = $db->FetchArray($rs)){
			$body_celkem = 0;
			$UPDATE = "";
			for ($i = 1 ; $i < 7; $i++){
				@$b = ${"b".$i."_".$radek["id_r"]};
				$b = strtr($b, ",", ".");
				$body_celkem += $b;
				if (strlen($b) > 0){
					$UPDATE .= "b$i = $b, ";
				} else {
					$UPDATE .= "b$i = NULL, ";
				}
			}
			@$splneno = ${"splneno_".$radek["id_r"]}; 
			@$pozn = ${"pozn_".$radek["id_r"]};
			if ($splneno <> 1){
				$splneno = 0;
			}
			if (strlen($UPDATE) > 0){
				$UPDATE .= " body=$body_celkem, splneno=$splneno, pozn2='$pozn'  ";
				$SQL = "UPDATE www_sifra_reseni_h SET $UPDATE WHERE id_r=".$radek["id_r"];
				$db->Query($SQL);
//				echo "<br>";
			}
		}
	}
	
	if (trim($email2) <> ""){
		$email2 = trim($email2);
		$SQL = "SELECT id_m, heslo  FROM www_sifra_lidi_h  WHERE email LIKE '$email2'  ";
		$rs = $db -> Query($SQL);
		$poc = $db->getNumRows($rs);
		$r_id_m = "";
		if ($poc > 0){
   			$r_id_m = $db->getResult($rs, 0, "id_m");  
		} else { 
            $strSQL = "INSERT INTO www_sifra_lidi_h (email, heslo, jmeno, prijmeni, telefon,  ulice, mesto, psc, vlozeno) 
                           VALUES ( '$email2','','$r_jmeno', '$r_prijmeni', '', '$r_ulice','$r_mesto','',Now() )";
            $sql = $db->Query($strSQL);
			$r_id_m = $db->getInsertId();	
		}
	    $strSQL = "INSERT INTO www_sifra_reseni_h (id_m, kolo, poznamka, r1, r2, r3, r4, r5, r6, b1, b2, b3, b4, b5, b6, pozn2,   vlozeno) 
                         VALUES ( $r_id_m, $kolo, '','$r1','$r2', '$r3', '$r4', '$r5','','$b1','$b2', '$b3', '$b4', '$b5', '','$pozn2', '".trim($dat)."' )";
         $sql = $db->Query($strSQL);
		
	}
}
$SQL = "SELECT lidi.jmeno, lidi.prijmeni, lidi.email, lidi.mesto,lidi.ulice, lidi.psc, reseni.* 
			FROM www_sifra_lidi_h AS lidi INNER JOIN www_sifra_reseni_h AS reseni ON lidi.id_m = reseni.id_m   WHERE reseni.kolo = $kolo  $ORDER_BY";
$rs = $db -> Query($SQL);
$old_email = "";
echo $db->getNumRows($rs)." záznamů <br>";
$por = 1;
if ($db->getNumRows($rs) > 0){
	while ($radek = $db->FetchArray($rs)){
		$email = $radek["email"];
		if ($old_email <> $email){
			$ret .= "<tr><td height=\"1\" bgcolor=\"#000000\" colspan=\"20\"></td></tr>";
			$v_email = $radek["email"];
			$jmeno = $radek["jmeno"]." ".$radek["prijmeni"];
			$adresa = $radek["ulice"]." ".$radek["mesto"]." ".$radek["psc"];
			$old_email = $email;
		} else {
			$v_email = "";
			$jmeno = "";
			$adresa = "";
		}
		
		$ret .= "<tr><td style=\"font-size: 9px;\">$por. ".$v_email."</td>";
		for ($i = 1; $i < 7; $i++){
			$reseni = trim($radek["r".$i]);
			if (strlen($reseni) > 0){ 
//				$reseni = iconv("utf-8", "CP1250", $reseni);
				$input = "<input type=\"text\" name=\"b".$i."_".$radek["id_r"]."\" value=\"".$radek["b".$i]."\" style=\"width:26px;align:right;\" size=\"3\">";
				if (strlen($reseni) > 15){
					$ret .=  "<td style=\"font-size: 9px;\"><div id=\"sm".$i."_".$radek["id_r"]."\">".substr($reseni, 0, 15)."<a href=\"#\" onclick=\"document.getElementById('sm".$i."_".$radek["id_r"]."').style.display='none';document.getElementById('vel".$i."_".$radek["id_r"]."').style.display='block';return false;\">&gt;&gt;</a></div><div id=\"vel".$i."_".$radek["id_r"]."\" style=\"display:none;\">".$reseni."</div>&nbsp;</td>
					 <td>".$input."</td>";
				} else {
					$ret .=  "<td style=\"font-size: 9px;\">". $reseni ."&nbsp;</td>
					 <td>".$input."</td>";
				}
				
			} else {
				$input = "&nbsp;"; 
				$ret .=  "<td style=\"font-size: 9px;\">&nbsp;</td>
					 <td>".$input."</td>";
			}
			 
					
		}
		if ($radek["splneno"] == 1){
			$checked = " checked";
		} else {
			$checked = "";
		}
		$ret .=  "	<td style=\"font-size: 9px;\">".$radek["poznamka"]."&nbsp;</td>
					<td style=\"font-size: 9px;\"><input type=\"checkbox\" name=\"splneno_".$radek["id_r"]."\" value=\"1\"$checked >".$radek["id_r"]."</td>
					<td style=\"font-size: 9px;\"><textarea name=\"pozn_".$radek["id_r"]."\" style=\"font-size: 9px;\">".$radek["pozn2"]."</textarea></td>
					 <td style=\"font-size: 9px;\">".write_date($radek["vlozeno"])."</td>
					 <td style=\"font-size: 9px;\">".$jmeno."</td>
					 <td style=\"font-size: 9px;\">".$adresa."</td>
					</tr>";
		$por ++;
	}
}
 ?> 
<form enctype="application/x-www-form-urlencoded" action="<?PHP echo $PHP_SELF?>?men=<?PHP echo $men;?>" method="post" name="oboduj"> 
Kolo: <select name="kolo">
<?PHP for ($i = 1; $i <= $maxkolo; $i++){ 
			if ($kolo == $i){
				$selected = " selected";
			} else {
				$selected = "";
			} 
			echo "<option value=\"$i\"$selected>$i</option>";
	 } ?>
</select>
<select name="order_by">
<option value="email"<?PHP if($order_by == "email"){echo " selected";}?>>email</option>
<option value="vlozeno"<?PHP if($order_by == "vlozeno"){echo " selected";}?>>datum odeslání odpovědi</option>
<option value="splneno"<?PHP if($order_by == "splneno"){echo " selected";}?>>tři dobré odpovědi</option>
</select> <input type="submit" value="Zobrazit" name="zobrazit">
<table border="0" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF">
<tr><td></td></tr>
<?PHP echo $ret; ?>
</table> 
<table> 
<tr><td colspan="3">Vložení nové odpovědi</td></tr>
<tr><td>email:</td><td colspan="2"><input type="text" name="email2" value="" size="40"></td></tr>
<tr><td>jméno<font color="#FF0000">*</font>:</td><td><input type="text" name="r_jmeno" value=""> </td></tr>
<tr><td>příjmení<font color="#FF0000">*</font>:</td><td><input type="text" name="r_prijmeni" value=""></td></tr> 	
<tr><td>škola<font color="#FF0000">*</font>:</td><td><input type="text" name="r_mesto" value=""></td></tr>
<tr><td>třída<font color="#FF0000">*</font>:</td><td><input type="text" name="r_ulice" value=""></td></tr>
<tr><td>Datum</td><td><input type="text" name="dat" value="<?PHP echo date("Y-m-d H:i:s");?>" size="40"></td><td>(yyyy-mm-dd hh:mm:ss)</td></tr>
<tr><td><b>1.</b>: </td><td><textarea name="r1" cols="30" rows="3"><?PHP echo $r1;?></textarea></td><td><input type="text" name="b1" value="" size="2"></td></tr>
<tr><td><b>2.</b>: </td><td><textarea name="r2" cols="30" rows="3"><?PHP echo $r2;?></textarea></td><td><input type="text" name="b2" value="" size="2"></td></tr>
<tr><td><b>3.</b>: </td><td><textarea name="r3" cols="30" rows="3"><?PHP echo $r3;?></textarea></td><td><input type="text" name="b3" value="" size="2"></td></tr>
<tr><td><b>4.</b>: </td><td><textarea name="r4" cols="30" rows="3"><?PHP echo $r4;?></textarea></td><td><input type="text" name="b4" value="" size="2"></td></tr>
<tr><td><b>5.</b>: </td><td><textarea name="r5" cols="30" rows="3"><?PHP echo $r5;?></textarea></td><td><input type="text" name="b5" value="" size="2"></td></tr>
 
<tr><td>poznamka:</td><td><textarea name="pozn2" cols="30" rows="3"><?PHP echo $pozn2;?></textarea></td></tr>
</table>

<input type="submit" name="odeslat_res" value="uložit body">
</form>
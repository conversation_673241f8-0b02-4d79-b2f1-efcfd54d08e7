Podrobn<PERSON> v<PERSON><PERSON> - deskohrátky: 
<?PHP
function write_date($date, $format=""){ //2005-09-28 12:21:20
	$y = substr($date, 0, 4);
	$m = substr($date, 5, 2);
	$d = substr($date, 8, 2);
	$h = substr($date, 11, 2);
	$min = substr($date, 14, 2);
	$sec = substr($date, 17, 2);
	
	$ret = $d.".".$m.".".$y." ".$h.".".$min.".".$sec;
	return $ret;
}
$ret = "";

if (!isset($kolo)){
	$kolo = 1;
}
if (!isset($order_by)){
	$order_by = "email";
}
if ($order_by == "email"){
	 $ORDER_BY = " ORDER BY lidi.email ";
} elseif ($order_by == "prijmeni"){ 
	 $ORDER_BY = " ORDER BY lidi.prijmeni, lidi.jmeno ";
}

$SQL = "SELECT lidi.*
			FROM www_sifra_lidi_h AS lidi  $ORDER_BY";
$rs = $db->Query($SQL);
$old_email = "";
if ($db->getNumRows($rs) > 0){
	while ($radek = $db->FetchArray($rs)){
		$email = $radek["email"];
		$jmeno = $radek["jmeno"]." ".$radek["prijmeni"];
		$adresa = $radek["ulice"]." ".$radek["mesto"]." ".$radek["psc"];
		$ret .=  "<td style=\"font-size: 9px;\">".$radek["email"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["heslo"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["jmeno"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["prijmeni"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["telefon"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["ulice"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["mesto"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".$radek["psc"]."&nbsp;</td>
					<td style=\"font-size: 9px;\">".write_date($radek["vlozeno"])."</td>
					</tr>";
	}
}
 ?> 
<form action="<?PHP echo $PHP_SELF?>?men=<?PHP echo $men;?>" enctype="multipart/form-data" method="post" name="oboduj">
<select name="order_by">
<option value="email">email</option>
<option value="prijmeni">příjmení</option>
</select> <input type="submit" value="Zobrazit" name="zobrazit">
<table border="0" cellpadding="0" cellspacing="0" bgcolor="#FFFFFF">
<tr><td></td></tr>
<?PHP echo $ret; ?>
</table> 
</form>

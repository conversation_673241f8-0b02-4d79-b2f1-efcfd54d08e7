<?PHP
require_once("../ssl_library_new/upload_magazine.l");
if(access("update", $men, $a_user["id_m"], $db)){
	if(isset($submit)){
		upload_magazine($upload, $upload_name);

	} else {
		?>
		<p>Vystavení zazipovaného časopisu na web mensy a intranet.<br>
		Soubor musí být ve tvaru: cYY-MM.zip, sYY-MM.zip, nebo iYY-MM.zip.<br>
		Podle toho, zda se jedná o časopis Mensy Česko, SR, nebo International.<br>
		Operace je časově nároč<PERSON>, protože dokází k FTP kopírování souboru na server.<br>
		Pokud chcete některý ze souborů nahradit, pouze jej vložte znovu.
		</p>
		<form action="index.php" ENCTYPE=multipart/form-data METHOD=POST><p><b><PERSON><PERSON><PERSON><PERSON><PERSON> časop<PERSON></b><br>
		<INPUT TYPE=HIDDEN NAME="men"  VALUE=<?PHP echo $men ?>> 
		<INPUT TYPE=HIDDEN NAME=MAX_FILE_SIZE VALUE=10000000>
		<table cellpadding="0" cellspacing="3" border="0">
		<tr>
			<td><p>Soubor:</p></td>
			<td align="right"><p><input type="file" name="upload" size="46"></p></td>
			<td align="right"><p><input type="submit" name="submit" value=" V l o ž i t "></p></td>
		</tr>
		</table></p></form>
		<?PHP
	}
} else {
	require_once("../ssl_pages_new/access_denied.i");
}
?>
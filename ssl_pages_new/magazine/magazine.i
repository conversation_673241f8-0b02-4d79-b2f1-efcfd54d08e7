<h1>St<PERSON>ž<PERSON><PERSON> časopisu Mensa</h1>

<img src="./images/casopis.png" width="127" height="180" alt="Casopis" style="float: right; margin-left: 1em;">

<p>Na těchto stránkách si můžete stáhnout elektronické verze všech časopisů Mensa.</p>

<p>Pokud chcete do časopisu přispívat, prosím, napište na adresu <a href="mailto:<EMAIL>?Subject=Prispevek"><EMAIL></a>,
    uzávěrky jsou zpravidla každou druhou středu lichého měsíce. Před napsáním Vašeho článku, prosím, věnujte pozornost
    následujícím doporučením (odkazy se otevírají v novém okně, jsou na veřejném webu časopisu):</p>


<ul>
    <li><a target="_new" href="http://casopis.mensa.cz/o_casopisu/vkladani_prispevku.html">Jak přispívat do časopisu
            Mensa?</a></li>
    <li><a target="_new" href="http://casopis.mensa.cz/o_casopisu/desatero_prispevatele.html">Desatero přispěvatele</a>
    </li>
    <li><a target="_new" href="http://casopis.mensa.cz/o_casopisu/jak_psat_reakci_na_akci.html">Jak psát reakci na
            akci?</a></li>
    <li><a target="_new" href="http://casopis.mensa.cz/o_casopisu/doporuceni_pro_fotografy.html">Doporučení pro
            fotografy</a></li>
</ul>

<p>Pokud chcete v časopise inzerovat, prosím napište na <a href="mailto:<EMAIL>?Subject=Inzerce"><EMAIL></a>,
    další informace naleznete v článku
    <a target="_new" href="http://casopis.mensa.cz/o_casopisu/klicove_informace_pro_inzerenty.html">Klíčové informace
        pro inzerenty</a>.
</p>


<?PHP
require_once("../ssl_library_new/parse.l");


$path = "../files/magazine/";
$d = dir($path);

// format nazvu: i09-11.zip, c92-0910.zip
// tj. (pismeno oznacujici typ)RR-MM[MM].pripona
while ($entry = $d->read()) {
    if (!($entry == "." or $entry == "..")) {
        // najdi spravny rok, odhadni podle dvojcisli
        $rok = "19";
        // hodnota prvniho dvojcisli
        if (intval(substr($entry, 1, 2)) < 85) {
            $rok = "20";
        }
        $a_files[$rok . $entry] = $entry;
    }
}
$d->close();


// if no files, then stop
if (count($a_files) <= 0) {
    echo "<p>Zatím nejsou vloženy žádné časopisy.</p>";
    return;
}


// sort read file entries by key
ksort($a_files);
$i = count($a_files) - 1;
foreach ($a_files as $value) {
    $a_files2[$i] = $value;
    $i--;
}
?>


<table border="0" cellpadding="0" cellspacing="0" width="99%">
    <tr>
        <td valign="top" width="50%">


            <p><b>Časopis Mensy Česko</b> ke stažení</p>
            <table border="0" cellpadding="0" cellspacing="0">
                <?PHP
                for ($i = 0; $i < count($a_files2); $i++) {
                    $entry = $a_files2[$i];
                    // tiskni ceske casopisy
                    if (substr($entry, 0, 1) == 'c') {
                        ?>
                        <tr>
                            <td>
                                <li style="font-size:12px">
                                    <a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>"><?PHP echo parse($entry) ?>
                                        - <?PHP echo Ceil(FileSize($path . $entry) / 1000) ?> kB</a>
                            </td>
                        </tr>
                        <?PHP
                    }
                }

                ?>
                <tr>
                    <td style="font-size:10px;">
                        <em>Omlouváme se za sníženou kvalitu starších vydání časopisů zapříčiněnou způsobem jejich
                            rozmnožování a nutností ručního skenování těchto předloh. Sníženou technickou hodnotu
                            vám jistě vynahradí jejich vysoká kvalita obsahová. </em>
                    </td>
                </tr>
            </table>


            <p><b>Albert - časopis brněnské mensovní skupiny</b></p>
            <table border="0" cellpadding="0" cellspacing="0">
                <?PHP
                for ($i = 0; $i < count($a_files2); $i++) {
                    $entry = $a_files2[$i];
                    if (substr($entry, 0, 1) == 'a') {
                        ?>
                        <tr>
                            <td>
                                <li style="font-size:12px">
                                    <a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>"><?PHP echo parse($entry) ?>
                                        - <?PHP echo Ceil(FileSize($path . $entry) / 1000) ?> kB.</a>
                            </td>
                        </tr>
                        <?PHP
                    }
                }
                ?>
            </table>


        </td>
        <td valign="top" width="50%">
            <p><b>Newsletter Mensy Česko</b> ke stažení</p>
            <table border="0" cellpadding="0" cellspacing="0">
                <?PHP
                for ($i = 0; $i < count($a_files2); $i++) {
                    $entry = $a_files2[$i];
                    if (substr($entry, 0, 1) == 'n') {
                        ?>
                        <tr>
                            <td>
                                <li style="font-size:12px">
                                    <a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>"><?PHP echo parse($entry) ?>
                                        - <?PHP echo Ceil(FileSize($path . $entry) / 1000) ?> kB.</a>
                            </td>
                        </tr>
                        <?PHP
                    }
                }
                ?>
            </table>
            <p><b>Sborník Mensy Česko</b> ke stažení</p>
            <table border="0" cellpadding="0" cellspacing="0">
                <?PHP
                for ($i = 0; $i < count($a_files2); $i++) {
                    $entry = $a_files2[$i];
                    if (substr($entry, 0, 1) == 'b') {
                        ?>
                        <tr>
                            <td>
                                <li style="font-size:12px">
                                    <a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>"><?PHP echo parse($entry) ?>
                                        - <?PHP echo Ceil(FileSize($path . $entry) / 1000) ?> kB.</a>
                            </td>
                        </tr>
                        <?PHP
                    }
                }
                ?>
            </table>

            <p><b>Časopis Mensy International</b> ke stažení</p>
            <table border="0" cellpadding="0" cellspacing="0">
                <?PHP
                for ($i = 0; $i < count($a_files2); $i++) {
                    $entry = $a_files2[$i];
                    if (substr($entry, 0, 1) == 'i') {
                        ?>
                        <tr>
                            <td>
                                <li style="font-size:12px">
                                    <a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>"><?PHP echo parse($entry) ?>
                                        - <?PHP echo Ceil(FileSize($path . $entry) / 1000) ?> kB.</a>
                            </td>
                        </tr>
                        <?PHP
                    }
                }
                ?>
            </table>
            <p><b>Časopis Mensy SR</b> ke stažení</p>
            <table border="0" cellpadding="0" cellspacing="0">
                <?PHP
                for ($i = 0; $i < count($a_files2); $i++) {
                    $entry = $a_files2[$i];
                    if (substr($entry, 0, 1) == 's') {
                        ?>
                        <tr>
                            <td>
                                <li style="font-size:12px">
                                    <a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>"><?PHP echo parse($entry) ?>
                                        - <?PHP echo Ceil(FileSize($path . $entry) / 1000) ?> kB.</a>
                            </td>
                        </tr>
                        <?PHP
                    }
                }
                ?>
            </table>

        </td>
    </tr>
</table>

<?PHP
function change_month($number){
	switch ($number){
		case "01":
			return "leden";
		case "02":
			return "únor";
		case "03":
			return "březen";
		case "04":
			return "duben";
		case "05":
			return "květen";
		case "06":
			return "červen";
		case "07":
			return "červenec";
		case "08":
			return "srpen";
		case "09":
			return "září";
		case "10":
			return "říjen";
		case "11":
			return "listopad";
		case "12":
			return "prosinec";
		default:
			return "nesprávný měsíc";
	}
}

function parse($file){

	$rok = " 19";
	if (substr($file,1,1)=="0"){
		$rok = " 20";
	}
	return +substr($file,5,2).'. '.change_month(substr($file,3,2)).$rok.substr($file,1,2);
}

	$path = "../files/zapisy_z_rady/";
	$d = dir($path);



while ($entry=$d->read()){
	if (!($entry=="." or $entry=="..") and (substr($entry,0,1)=="z" and substr($entry, strpos($entry,".")+1, 4)=="html")){
		$rok = "19";
		if (substr($entry,1,1)=="0"){
			$rok = "20";
		}
		$a_files[$rok.$entry]=$entry;
	}
}
$d->close();

if (count($a_files)>0) {
	ksort($a_files);
	$i=count($a_files)-1;

	foreach ($a_files as $value) {
		$a_files2[$i] = $value;
		$i--;
	}
?>

<p><b>Zápisy ze zasedání Rady Mensy</b></p>
<table border="0" cellpadding="0" cellspacing="0" width="90%">
<?PHP
  for ($i=0;$i<count($a_files2);$i++){
	$entry=$a_files2[$i];
	if(substr($entry,0,1)=='z') {
		?>
			<tr>
				<td><li style="font-size:12px"><a href="document.php?men=<?PHP echo $men ?>&file=<?PHP echo $entry ?>" target="_blank"><?PHP echo parse($entry) ?></a></td>
			</tr>
		<?PHP
	}
  }
?>
</table>
<?PHP
} else {
	?>
			<p>Zatím nejsou vloženy žádné zápisy.</p>
	<?PHP
}
?>

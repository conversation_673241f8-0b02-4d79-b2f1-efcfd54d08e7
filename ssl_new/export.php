<?PHP

error_reporting(E_ALL);

session_start();
ob_start();

if (!isset($men)) {
    $men = "men0.0.0.0";
}

$login = $_SESSION["login"];
$heslo = $_SESSION["heslo"];

if (empty($login) or empty($heslo)) {
    //echo "chybi login";
    Header("Location: login.php");
}

//ODHLASENI
preg_match("/^men(\d+)\.(\d+)\.(\d+)\.(\d+)$/", $men, $parts);

$s1 = @$parts[1];
$s2 = @$parts[2];
$s3 = @$parts[3];
$s4 = @$parts[4];

if ($s1 == 1 and $s2 == 1 and $s3 == 1 and $s4 == 0) {
    session_destroy();
}

/** @deprecated use LIB_DIR for new libraries */
define("LIB_NEW_DIR", dirname(__FILE__)."/../ssl_library_new");

define("LIB_DIR", dirname(__FILE__)."/../ssl_library");
define("PAGE_DIR", dirname(__FILE__)."/../ssl_pages_new");

require_once LIB_DIR."/autoload.php";
require_once LIB_DIR."/vendor/autoload.php";

$autoload_dirs = [
    LIB_DIR,
];

spl_autoload_register(function ($class) use ($autoload_dirs) {
    autoload($class, $autoload_dirs);
});

require_once LIB_NEW_DIR."/register_globals.i";
require_once LIB_NEW_DIR."/login.i";
require_once LIB_NEW_DIR."/database.class.l";

$db = new database;
$db->Open();

if(login($login, $heslo, $log_err_string, $db)){
    require_once LIB_NEW_DIR . "/user.l";

    $a_user = user($login, $heslo, 0, $db);
    if (!$a_user) {
        Header("Location: login.php");
    }

        //promazani aktualne prihlasenych
    if ($s1 == 1 and $s2 == 1 and $s3 == 1 and $s4 == 0) {
        $SEL = "DELETE from m_members_login WHERE id_m=".$a_user["id_m"];
        $db->Query($SEL);
    }

    require_once LIB_NEW_DIR."/access.l";

    if (access("read", $men, $a_user["id_m"], $db)) {
        //default stranka pro billboard a kdyz neni dokument
        switch ($men) {
            case "men19.2.6.0":
                header('Pragma: anytextexeptno-cache', true);
                header("Content-type: application/x-msexcel");
                header("Content-Disposition: attachment; filename=mensa_platny.csv");
                $vypis = "export";
                $vybrano = "v";

                include PAGE_DIR."/centr/sekretarka/clenove.i";

                break;
            case "men19.2.18.0":
            case "men19.2.2.0":
                $vypis = "export";
                $c_id_m = $_POST["c_id_m"];

                include PAGE_DIR."/centr/sekretarka/popup_tisk_form.php";
                include PAGE_DIR."/centr/sekretarka/certifikat_tisk_form.php";

                if (isset($_POST["volba_1"]) && $_POST["volba_1"] == "certifikat") {
                    ob_clean();
                    $mpdf->Output("tisk_certifikaty.pdf", 'D');
                    exit();
                }
                elseif (isset($_POST['volba_1']) && $_POST['volba_1'] == "certifikat_PDF") {
                    // Process data for certificate with background image - processing is handled in popup_tisk_form.php
                    ob_clean();
                    $mpdf->Output("tisk_certifikaty.pdf", 'D');
                    exit();
                }
                else if (isset($_POST["volba_1"]) && $_POST["volba_1"] == "adresy") {
                    ob_clean();
                    $mpdf->Output("tisk_adresy.pdf", 'D');
                    exit();
                }
                break;
            default:
                exit;
        }
    }
    else {
        include PAGE_DIR."/access_denied.i";
    }
} else {
    Header("Location: login.php");
}

ob_end_flush();

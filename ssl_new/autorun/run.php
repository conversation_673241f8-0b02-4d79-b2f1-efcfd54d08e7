<?PHP
/**
 * UTF-8, LF
 *
 * Wrapper spousteni autmatickych skriptu
 * Tento skript je pravidelne volan cronem a spousti ostatni autorun skripty.
 *
 *
 * Kam jde log z crontabu nevime, asi k mirkovi a asi je obtizne se k nemu
 * dostat, radeji proto udelame vlastni reseni.
 *
 *
 * Tento autmaticky skript kontroluje aby nebyl spusten vicekrat,
 * proto je nastavena perioda mozneho dalsiho spusteni, kdy se ocekava, ze
 * je jich mrtvy. Tento koeficient by mel byt 2x vetsi nez perioda
 * se kterou je spousten (neni jiste, ze to funguje !!!).
 *
 *
 * Zmenovnik
 *  2014-11-20, TK: Vycisteni kodu a opraveni aby konecne dobihal korektne
 *  2013-08-25, TK: Zapnuti logovani o behu skriptu do naseho souboru. Udelej error handler pro chyby.
 *
 */
$dead_time = 300; //sekund, limit kdy bude skript znovu spusten, do te doby se odmita spustit, pokud je v db. zanzma o nedobehnutem
$start = time(); // time je UNIX time stamp





// toto zaridi ulozeni logu, pokud skript havaruje/obecne skonci ... fatal error totiz neosetri try catch !!
// vola se pri jakemkoliv ukonceni skriptu !!! nicmene je tu podminka, ze pokud neni nic na bufferu
// tak nic nezapise, tj. pokud si korektni logovani po sobe vycisti, je vse ok :)
//
// Registers a callback to be executed after script execution finishes or exit() is called.
// http://php.net/manual/en/function.register-shutdown-function.php
register_shutdown_function( "fatal_handler" );
function fatal_handler() {
    // Gets information about the last error that occurred.
    $error = (error_get_last() !== NULL) ? error_get_last() : NULL;

    // nechceme notice a deprecated
    if (($error === NULL) || ($error['type'] == E_NOTICE) || ($error['type'] == E_DEPRECATED )) {
        $error = NULL;
    }

    // buffer
    $vysledek = ob_get_contents ();
    if (mb_strlen($vysledek) < 1) $vysledek = FALSE; // pokud je buffer prazdny, ber jako kdyby nebyl

    //  int file_put_contents ( string $filename , mixed $data [, int $flags = 0 [, resource $context ]] )
    // zapis do souboru, musi byt cesta z pohledu apache, ne FTP
    if (($vysledek != FALSE) || ($error !== NULL)) file_put_contents ('/home/<USER>/logs/autorun.' . date('Y-m-d_H-i-s') . '.error.txt' ,  $vysledek . (($error !== NULL)?("\n\nChyba: " . print_r($error, true)):""));

    // vycisti a vypni bufferovani
    ob_end_clean();
}





////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
// start
// http://php.net/manual/en/errorfunc.configuration.php#ini.error-reporting
// This setting does not show E_NOTICE, E_STRICT and E_DEPRECATED level errors
error_reporting(E_ALL & ~E_NOTICE & ~E_STRICT & ~E_DEPRECATED);


// zapni zapisovani do bufferu pro nas
// toto by mel zarazit budto log nebo shutdown handler
ob_start();


use config\ConfigFactory;
use database\DatabaseFactory;
use intranet\IntranetFactory;
use mailer\MailerFactory;
use request\Request;
use vcalendar\VCalendarFactory;


/** @deprecated use LIB_DIR for new libraries */
define("LIB_2013_DIR", dirname(__FILE__) . "/../../ssl_library_2013");
/** @deprecated use LIB_DIR for new libraries */
define("LIB_NEW_DIR", dirname(__FILE__) . "/../../ssl_library_new");

define("CONFIG_DIR", dirname(__FILE__) . "/../../ssl_config");
define("LIB_DIR", dirname(__FILE__) . "/../../ssl_library");
define("PAGE_DIR", dirname(__FILE__) . "/../../ssl_pages_new");

require_once LIB_DIR . "/autoload.php";
require_once LIB_DIR . "/vendor/autoload.php";

$autoload_dirs = [
    LIB_DIR,
];

spl_autoload_register(function($class) use ($autoload_dirs) {
    autoload($class, $autoload_dirs);
});

require_once LIB_NEW_DIR . "/register_globals.i";
require_once LIB_NEW_DIR . "/login.i";
require_once LIB_NEW_DIR . "/database.class.l";
require_once LIB_NEW_DIR . "/get_list.l";





// pripoj se do databaze
$db = new database();
$db->Open();



//kontrola, zdali automat nebezi
$tmp = $db->Query("SELECT *, TIME_TO_SEC(TIMEDIFF(NOW(),cas)) AS  rozdil FROM mensaweb.autorun WHERE work=1");
if ($db->getNumRows($tmp) > 0)
{
    $rozdil = $db->getResult($tmp, 0, "rozdil");
    if ($rozdil > $dead_time)
    {
        // zda se, ze existuje nedobehnuty skript
        // zabij skripty, ktere zrejme havarovaly a nedobehly
        // bohuzel samotne skripty nezabijeme, jen se oznaci v db. za mrtve (Hrubcik ukazal, ze to muze byt problem !!!)
        $db->Query("UPDATE mensaweb.autorun SET work=0, log=CONCAT(log, '\n\nSkript do databaze nezapsal, toto je dodatecne ukonceni. Overte soubor ftp://<EMAIL>/logs/autorun.***********.error.txt') WHERE work=1");
        echo "Existoval skript, ktery nedobehl, ukoncuji jej.\nCas spusteni: $start, rozdil od predchoziho $rozdil s, nepokracuji.\n";
        exit(-3);
    }
    else
    {
        // tady nechceme jet znovu
        echo "Opetovne spusteni v $start, rozdil $rozdil s.\nNepokracuji.\n";
        exit(-2);
    }
}



chdir(".."); // toto musi byt kvuli tomu ob start
echo "Pracovni adresar: ". getcwd ()."\n";


$db->Query("INSERT INTO mensaweb.autorun (cas, work, konec, log) VALUES (NOW(), 1, 'NULL', 'Skript spusten v: ".$db->escape(getcwd())."')");
echo "Zacatek: " . date('Y-m-d H:i:s') . " ($start)\n";



// spusti pracovni skripty
// Prace zahajena v adresari: /home/<USER>/ssl_new
////////////////////////////////////////////////////////////////////////////////
try
{
    require_once '../ssl_library_new/konference.l';
    require_once '../ssl_library_new/autosendmail.l';
    require_once '../ssl_library_2013/m_pocet_clenu.php';
    require_once '../ssl_library_2013/check_gp_payments.php';
    echo "Vse dobehlo korektne.\n";
} catch (Exception $e)
{
    echo 'Skript havaroval s chybou: ',  $e->getMessage(), "\n";
    exit(-1); // toto by mel chytit error handler jako korektni chybu
}
echo "Konec: " . date('Y-m-d H:i:s') . " cas behu " . (time() - $start) . " s.\n";
////////////////////////////////////////////////////////////////////////////////





// zapis do logu pri legalnim prubehu
$vystup = ob_get_contents (); // ob_get_contents — Gets the contents of the output buffer without clearing it.
if ($vystup === FALSE) return; // to znamena, ze neni zapnute bufferovani, to je divne, kazdopadne neni co resit
ob_clean(); // vycisti buffer, aby tam uz nic nebylo, ale nevypinej bufferovani

// uloz log do databaze
$query ="UPDATE mensaweb.autorun SET work=0, konec=NOW(), log='".$db->escape($vystup)."' WHERE work=1";
$db->Query($query);

// zapis do souboru
// int file_put_contents ( string $filename , mixed $data [, int $flags = 0 [, resource $context ]] ) — Write a string to a file
// 147-148 je standardni delka korektniho vystupu, kdy se nic nedeje
if (mb_strlen($vystup) > 148) file_put_contents ('/home/<USER>/logs/autorun.' . date('Y-m-d_H-i-s') . '.log.txt', $vystup);

// pokud tady nevznikly chyby pri zapisu, je tedy prazdny buffer, bufferovni vypni -> shutdown handler uz nic nechyti !!!
// a to chceme, protoze jsme vse korektne zapsali
if (ob_get_length() < 1) ob_end_clean ();




// vypni zapisovani - NECHCEME - to co zustane tady by mel pozrat shutdown handler jako chybu a ma tedy zustat otevreny handler
$db->Close();

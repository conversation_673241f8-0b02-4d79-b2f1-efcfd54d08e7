<?php require_once "bootstrap.php"; ?>
<?php require_once "code/platba_poukaz.php"; ?>

<?php $form_errors = getFormErrors("voucher"); ?>
<?php clearFormErrors("voucher"); ?>

<!DOCTYPE html>
<html lang="cs">
<?php include_once "includes/header.php"; ?>

<body>
    <?php include_once "includes/menu.php"; ?>

    <center>
        <div id="obsah" style="text-align: left; width: 45%;">
            <h1>Uplatnění slevového kódu</h1>

            <?php $payment_data = initPaymentData(); ?>
            <?php $display_form = $payment_data->validateForVoucher(); ?>

            <?php if ($display_form): ?>
                <p>
                    Pro uplatnění slevového kódu prosím vyplňte číslo slevového kódu a&nbsp;zkontrolujte vyplněné jméno a&nbsp;příjmení. Jméno a&nbsp;příjmení musí odpovídat údajům zadaným při vytvoření slevového kódu.
                </p>

                <?php if ($form_errors): ?>
                    <div style="color: red;">
                        <p>
                            <?php foreach ($form_errors as $error): ?>
                                <?php echo $error; ?><br />
                            <?php endforeach; ?>
                        </p>
                    </div>
                <?php endif; ?>

                <div style="margin-bottom: 20px;">
                    <form action="platba_poukaz_odeslani.php" method="post">
                        <input type="hidden" name="sent" value="1"/>
                        <input type="hidden" name="data[customId]" value="<?php echo $payment_data->getCustomId(); ?>"/>

                        <div style="margin-bottom: 10px;">
                            <!-- tady musi byt jmeno testovaneho, nikoliv zakonneho zastupce (ktery plati) -->
                            <label for="name">Jméno</label><br />
                            <input id="name" type="text" name="data[name]" value="<?php echo $payment_data->getAttendeeName(); ?>" />
                        </div>

                        <div style="margin-bottom: 10px;">
                            <!-- tady musi byt prijmeni testovaneho, nikoliv zakonneho zastupce (ktery plati) -->
                            <label for="surname">Příjmení</label><br />
                            <input id="surname" type="text" name="data[surname]" value="<?php echo $payment_data->getAttendeeSurname(); ?>" />
                        </div>

                        <div>
                            <label for="voucher">Slevový kód</label><br />
                            <input id="voucher" type="number" name="data[voucher]" value="<?php echo $payment_data->getVoucher(); ?>" min="100000" max="999999" step="1" />
                        </div>

                        <div style="margin-top: 20px;">
                            <input id="submit-voucher" type="submit" value="Ověřit kód" style="cursor: pointer;"/>
                        </div>
                    </form>
                </div>

            <?php else: ?>
                <p>
                    Formulář pro uplatnění slevového kódu nelze zobrazit, nejsou nastaveny nezbytné parametry.
                </p>
                <p>
                    Pro správné zobrazení formuláře pro uplatnění slevového kódu prosím vyberte na platební stránce Platba za IQ test variantu Uplatnění slevového kódu a&nbsp;klikněte na tlačítko Pokračovat.
                </p>
            <?php endif; ?>
        </div>
    </center>

    <?php include_once "includes/footer.php"; ?>
</body>
</html>
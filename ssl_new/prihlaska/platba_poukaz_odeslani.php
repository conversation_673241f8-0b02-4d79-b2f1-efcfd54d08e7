<?php require_once "bootstrap.php"; ?>
<?php require_once "code/platba_poukaz.php"; ?>

<?php

if (!isFormSent()) {
    echo "Přímý přístup ke stránce není povolený.";
    exit;
}

$form_data = getFormData();
$form_errors = validateVoucherData($form_data);

if ($form_errors) {
    setFormErrors("voucher", $form_errors);
    redirectToPage("platba_poukaz.php", $form_data);
}

/** @noinspection PhpUndefinedVariableInspection */
$voucher_errors = tryApplyingVoucherCode($intranet->getMensaweb(), $form_data);

if ($voucher_errors) {
    setFormErrors("voucher", $voucher_errors);
    redirectToPage("platba_poukaz.php", $form_data);
}

redirectToPage("platba_shrnuti.php", [
    "customId" => $form_data['customId'],
    "rc" => 1,
]);

<?php

require_once "platba.php";

use database\model\mensaweb\ModelFactory;
use payment\Data\Payment as PaymentData;

/**
 * @return PaymentData
 */
function initPaymentData()
{
    $payment_data = new PaymentData();
    $payment_data->setCustomId(getSimpleGetValue("customId"));
    $payment_data->setName(getSimpleGetValue("name"));
    $payment_data->setSurname(getSimpleGetValue("surname"));
    $payment_data->setVoucher(getSimpleGetValue("voucher"));

    return $payment_data;
}

/**
 * @param array $form_data
 * @return array
 */
function validateVoucherData(array $form_data)
{
    $errors = [];

    if (!isset($form_data['customId'])) {
        $errors[] = "Variabilní symbol musí být vyplněn.";
    }
    else if (!intval($form_data['customId'])) {
        $errors[] = "Variabilní symbol musí být celé číslo.";
    }

    if (!isset($form_data['name']) || !$form_data['name']) {
        $errors[] = "Jméno musí být vyplněno.";
    }
    if (!isset($form_data['surname']) || !$form_data['surname']) {
        $errors[] = "Příjmení musí být vyplněno.";
    }

    if (!isset($form_data['voucher']) || !$form_data['voucher']) {
        $errors[] = "Slevový kód musí být vyplněn.";
    }

    return $errors;
}

/**
 * @param ModelFactory $mensaweb
 * @param array $form_data
 * @return array
 */
function tryApplyingVoucherCode(ModelFactory $mensaweb, array $form_data)
{
    $poukazy_na_test_model = $mensaweb->getPoukazyNaTestModel();
    $is_valid_voucher_code = $poukazy_na_test_model->isValidVoucherCode($form_data);

    if ($is_valid_voucher_code) {
        $poukazy_na_test_model->setVoucherAsApplied($form_data['voucher'], $form_data['customId'], true);

        return [];
    }

    return [
        "Slevový kód není platný pro zadané jméno, příjmení a aktuální datum nebo již byl uplatněn."
    ];
}

<?PHP
/**
 * UTF-8, UNIX LF
 *
 * Registrace na akce a potvrzení o přihlášení
 *
 * Změnovník
 *  2012-Dec-05, TK: Zavedení změnovníku. Snaha o doplnění názvu akce do předmětu emailu.
 *  2013-Aug-19, TK: Email s potvrzením přihlášenému.
 *  2013-Sep-02, PM: V emailu účastníkovi nahrazeno pole send_email z mc_akce_prihlasky (může být prázdné) polem email z tabulky mc_akce.
 *  2018-Apr-11, Pridana ICS pozvanka
 *  2018-Jul-24, Pridano GDPR handling
 *
 */

use helpers\Formatters;
use mailer\MailerFactory;
use vcalendar\VCalendarFactory;

require_once LIB_2013_DIR . "/mcaptcha.php";

require_once LIB_NEW_DIR . "/calendar.class.l";

// TODO pořádný debordel kódu :)
// TODO přesun tříd do ssl_library_new

/** @noinspection PhpUndefinedVariableInspection */
$mensaweb = $intranet->getMensaweb();

if (isset($_REQUEST["id_a"])) {
    /* 2018-12-17: TK: rychle overeni, ze se hlasime na akci, nikoliv na testovani. */
    $id_a = (int) $_REQUEST["id_a"];
    $akce = $mensaweb->getAkceModel()->getByIdA($id_a);

    if (!$akce) {
        echo "<h1 style='margin: 2em; text-align: center;'>Chyba 404: Akce {$id_a} nenalezena.</h1>";
        return;
    }

    if ($akce['typ'] === "test") {
        echo "<h1 style='margin: 2em; text-align: center;'>Snažíte se použít starou přihlášku na test IQ.</h1>";
        echo "<p style='margin: 2em; text-align: center; font-size: large;'>" .
            "Prosíme, použijte <a href='./test_iq.php?id_a={$id_a}'>novou verzi přihlášky</a>.</p>";

        return;
    }
}
else {
    echo "<h1>Chyba 404: Akce nenalezena! Chybně zadaná adresa.</h1>";
    return false;
}

class RegAkceCalendar extends calendar
{
    /**
     * @param int $id_a
     * @param int $pravidelna
     * @param string $typ
     * @param string $ms
     * @param int $aktualni
     * @param bool $zobraz_ikonky
     * @param int $id_m
     * @return string
     */
    public function get_action(int $id_a = 0, int $pravidelna = 0, string $typ = "", string $ms = "", int $aktualni = 0, bool $zobraz_ikonky = false, int $id_m = 0): string
    {
        return str_replace(
            'action="./index.php"',
            'action="reg_akce.php?id_a='.intval($id_a).'"',
            parent::get_action($id_a, $pravidelna, $typ, $ms, $aktualni, $zobraz_ikonky, $id_m)
        );
    }
}

/**
 * @param string $key
 * @return mixed
 */
function extract_from_post($key)
{
    // @TODO: protoze je vetsina promennych v intranetu VARCHAR, je prazdny retezec lepsi default
    // navic odpovida syntaxi puvodnich insertu, ktere byly psane jako '$promenna'.
    // principialne to ale neni idelani, nicmene bude vyzadovat hlubsi refaktoring na opravu.
    return $_POST[$key] ?? "";
}

$db = new database;
$db->Open();

$calendar = new RegAkceCalendar($db);

$jmeno = extract_from_post("jmeno");
$email = extract_from_post("email");
$ucastnikNad16 = extract_from_post("ucastnik_nad16");
$email2 = extract_from_post("email2");
$jmenoZZ = extract_from_post("jmeno_zz");
$telefon = extract_from_post("telefon");
$vek = extract_from_post("vek");
$rc = extract_from_post("rc");
$cc = extract_from_post("cc");
$adresa = extract_from_post("adresa");
$mesto = extract_from_post("mesto");
$psc = extract_from_post("psc");
$datumnar = extract_from_post("datumnar");
$variabilni = extract_from_post("variabilni");
$vol1 = extract_from_post("vol1");
$vol2 = extract_from_post("vol2");
$vol3 = extract_from_post("vol3");
$poznamka = extract_from_post("poznamka");
$captcha_value = extract_from_post("comment");

// captcha
// prihlaska se pak tise nezpracuje.
$captcha_class = new MCaptcha($id_a);

if (isset($_POST['odeslat_m']) && $captcha_value == $captcha_class->get_value()) {
    // ziskej informace o definici prihlasky
    // tj. popisu poli v prihlasce
    $definicePrihlasky = $mensaweb->getAkcePrihlaskyModel()->getByIdA($id_a);

    if ($definicePrihlasky) {
        $akce = $mensaweb->getAkceModel()->getByIdA($id_a);

        $mail_organizator = trim($definicePrihlasky["send_email"]);
        $mailer = MailerFactory::getGoogleMailer();

        // chce organizator poslat email
        if ($definicePrihlasky["send_email"] <> "") {
            // sestav telo, zacni odkazem an stranky akce
            // TODO všechna url do configu
            // TODO mail šablona
            $mailbody = "
                <p>Na akci <strong>{$akce['nazev']}</strong> se přihlásil účastník <strong>{$jmeno}</strong>.</p>
                
                <p>
                    Stránka akce na webu: <a href='https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a={$id_a}'>https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a={$id_a}</a><br />
                    Stránka akce na intranetu: <a href='https://intranet.mensa.cz/index.php?men=men15.5.0.0&id_a={$id_a}'>https://intranet.mensa.cz/index.php?men=men15.5.0.0&id_a={$id_a}</a>
                </p>
                
                <p><strong>Zadané informace</strong><br />
            ";

            if (mb_strlen($jmeno) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["jmeno"]) . ": {$jmeno}<br />";
            }
            if (mb_strlen($email) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["email"]) . ": <a href='mailto:{$email}'>{$email}</a><br />";
            }
            if (mb_strlen($jmenoZZ) > 0) {
                $mailbody .= "Jméno a příjmení zákonného zástupce: {$jmenoZZ}<br />";
            }
            if (mb_strlen($email2) > 0) {
                $mailbody .= "E-mail zákonného zástupce: <a href='mailto:{$email2}'>{$email2}</a><br />";
            }
            if (mb_strlen($telefon) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["telefon"]) . ": {$telefon}<br />";
            }
            if (mb_strlen($vek) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["vek"]) . ": {$vek}<br />";
            }
            if (mb_strlen($datumnar) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["datumnar"]) . ": {$datumnar}<br />";
            }
            if (mb_strlen($rc) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["rc"]) . ": {$rc}<br />";
            }
            if (mb_strlen($cc) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["cc"]) . ": {$cc}<br />";
            }
            if (mb_strlen($adresa) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["adresa"]) . ": {$adresa}<br />";
            }
            if (mb_strlen($mesto) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["mesto"]) . ": {$mesto}<br />";
            }
            if (mb_strlen($psc) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["psc"]) . ": {$psc}<br />";
            }
            if (mb_strlen($variabilni) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["variabilni"]) . ": {$variabilni}<br />";
            }
            if (mb_strlen($vol1) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["vol1"]) . ": {$vol1}<br />";
            }
            if (mb_strlen($vol2) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["vol2"]) . ": {$vol2}<br />";
            }
            if (mb_strlen($vol3) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["vol3"]) . ": {$vol3}<br />";
            }
            if (mb_strlen($poznamka) > 0) {
                $mailbody .= htmlspecialchars($definicePrihlasky["poznamka"]) . ": {$poznamka}<br />";
            }

            $mailbody .= "</p>";

            $mailer->sendDefaultMail(
                $email,
                $jmeno,
                [
                    $mail_organizator => '',
                ],
                "Přihláška na akci {$akce['nazev']}",
                $mailbody
            );
        }

        // zasli potvrzeni ucastnikovi
        if (!empty(trim($email2))) {
            $emailucastnik = trim($email2);
        }
        else {
            $emailucastnik = trim($email);
        }

        if ($emailucastnik != "") {
            $mailbody = "
                <p>Potvrzení o přihlášení na akci Mensy: <strong>{$akce['nazev']}</strong>.</p>
            
                <p>
                    Akce začíná " . Formatters::formatShortDateTime($akce['date_start']) . " na adrese {$akce['place']}, {$akce['city']}.<br />
                    Stránka akce: <a href='https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a={$id_a}'>https://intranet.mensa.cz/prihlaska/reg_akce.php?&id_a={$id_a}</a>
                </p>
                
                <p>Kontakt na organizátora: <a href='mailto:{$akce['email']}'>{$akce['email']}</a></p>
                
                <p>
                    S pozdravem,<br />
                    Mensa Česko
                </p>
            ";

            $mailer->sendIcalMail(
                $akce['email'],
                "Organizátor",
                [
                    $emailucastnik => $emailucastnik,
                ],
                "Přihláška na akci {$akce['nazev']}",
                $mailbody,
                [],
                VCalendarFactory::getCalendar()->getEventIcal(array_merge(
                    $akce,
                    [
                        "jmeno_org" => "Organizátor",
                        "email_org" => $akce['email'],
                    ]
                )),
                "MensaAkce.ics"
            );
        }
    }

    $owner = $mensaweb->getAkceModel()->getByIdA($id_a);
    $id_owner = $owner['id_owner'];

    try {
        // TODO používat pole (objekt) všude místo N proměnných
        $mensaweb->getWwwForm1Model()->insertAction([
            "id_a" => $id_a,
            "id_org" => $owner['id_owner'],
            "jmeno" => $jmeno,
            "email" => $email,
            "jmeno_zz" => $jmenoZZ,
            "email2" => $email2,
            "telefon" => $telefon,
            "vek" => $vek,
            "rc" => $rc,
            "cc" => $cc,
            "adresa" => $adresa,
            "mesto" => $mesto,
            "psc" => $psc,
            "datumnar" => $datumnar,
            "variabilni" => $variabilni,
            "vol1" => $vol1,
            "vol2" => $vol2,
            "vol3" => $vol3,
            "poznamka" => $poznamka,
            "datum" => date("Y-m-d H:i:s"),
        ]);

        echo "<h2 class='notify'>Přihláška byla uložena.</h2>";
        
        // Vyčistit POST data, pokud se povede přihláška na akci
        if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST') {
            // Zobrazení flash message pouze jednou skrz session
            $_SESSION['form_success'] = true;
            header("Location: reg_akce.php?id_a={$id_a}");
            exit;
        }
    }
    catch (\PDOException $e) {
        // @TODO: remove db. error details (debug)
        echo "<h2 class='notify error'>Uložení přihlášky selhalo z důvodu chyby na straně databáze.</h2>\n\n<!-- {$e} -->\n\n";
    }

} elseif (isset($_POST['odeslat_m']) && $captcha_value != $captcha_class->get_value()){
    // captcha warning
    echo "<h2 class='notify error'>Přihlášení selhalo. Pokud nejste spamovací robot, prosím, akci opakujete nebo kontaktujte organizátora.</h2>";
}

// Display success message only once if session flag exists
if (isset($_SESSION['form_success']) && $_SESSION['form_success'] === true) {
    echo "<h2 class='notify'>Přihláška byla uložena.</h2>";

    unset($_SESSION['form_success']);
}

////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////
//                               Vypis stránky akce                           //
////////////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////////////

echo $calendar->get_action($id_a);
?>

<script type="text/javascript">
        // <![CDATA[
        function update_zz() {
            // skryje nebo zobrazi pole s emailem ZZ
            var vek = document.querySelector('input[name="ucastnik_nad16"]').checked;
            var fields = document.getElementsByClassName("zz-form-fields");
            if (!vek) {
                [].forEach.call(fields, function(element) {
                    element.style.display = "";
                    element.children[1].setAttribute("required", "");
                });
            }
            else {
                [].forEach.call(fields, function(element) {
                    element.style.display = "none";
                    element.children[1].removeAttribute("required");
                });
            }
        }

        // dopln nasjeldujici funkce po nacteni dokumentu
        document.addEventListener("DOMContentLoaded", function(){
            var vekovyCheck = document.querySelector('input[name="ucastnik_nad16"]');
            if(vekovyCheck) {
                // aktualizuj zobrazeni zz
                update_zz();

                // pri zmene aktualizuj zz
                vekovyCheck.addEventListener('change', function() {
                    update_zz();
                });
            }
        });
        // ]]>
</script>

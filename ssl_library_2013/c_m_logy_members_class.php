<?php
/*
 *  @package mensa.cz
 *  <AUTHOR> <PERSON><PERSON><PERSON><PERSON>, <EMAIL>
 *  @version 1.0
 */

/**
 * <PERSON><PERSON><PERSON><PERSON> obsluhy logů akcí nad datab<PERSON><PERSON><PERSON>
 *
 * Používá tabulku c_m_logy_members
 * CREATE TABLE IF NOT EXISTS `c_m_logy_members` (
 * `id` int(11) NOT NULL AUTO_INCREMENT,
 * `datum_udalosti` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
 * `typ` int(11) DEFAULT NULL COMMENT 'Typ události. Je popsáno v objektu modelu',
 * `kdo` int(11) DEFAULT NULL COMMENT 'id_m Kdo provedl',
 * `komu` int(11) DEFAULT NULL COMMENT 'c_id_m Na datech kterého člena',
 * `popis` varchar(2048) CHARACTER SET cp1250 COLLATE cp1250_czech_cs DEFAULT NULL COMMENT 'Upřesnění',
 * <PERSON>IMARY KEY (`id`),
 * KEY `typ` (`typ`,`komu`)
 * ) ENGINE=MyISAM  DEFAULT CHARSET=latin1 COMMENT='Logy o událostech manipulace s členy' AUTO_INCREMENT=1 ;
 * Data jsou v win-1250
 *
 *  Zmenovnik:
 * 2014-10-2: VK - Vytvořeno
 */

// TODO 2023: move to database models

class c_m_logy_members
{

    public $dbsource;    // connection do databáze

    function __construct($db)
    {
        $this->dbsource = $db;
    }

    /**
     * Zapíše do databáze logový záznam o události
     *
     * @param int $typ_udalosti - viz hodnoty ve funkci getPopisTypu
     * @param int $kdo - hodnota id_m z tabulky mensasec.c_m_members - kdo prováděl logovanou akci
     * @param int $komu - hodnota c_id_m z tabulky mensasec.c_m_members - na kterém záznamu bylo prováděno
     * @param char(2048) $popis - popis logované události
     */
    public function zapisLog($typ_udalosti, $kdo, $komu, $popis)
    {
        $SQL = 'INSERT INTO mensasec.c_m_logy_members SET datum_udalosti=NOW(), typ=' . $typ_udalosti . ', kdo=' . $kdo . ', komu=' . $komu . ', popis="' . $popis . '"';
        return $this->dbsource->Query($SQL);
    }

    /**
     *
     * @param int $c_id_m - hodnota c_id_m z tabulky mensasec.c_m_members - pro kterého člena chcemy informace
     * @return Array() - vrací pole řádků s již odeslanými výsledky testů (popis, datumodeslani, jméno odesílajícího, příjmení odesílajícího)
     */
    public function getClenaVysledky($c_id_m)
    {
        $SQL = 'SELECT mensasec.c_m_logy_members.popis,  '
            . 'DATE_FORMAT(mensasec.c_m_logy_members.datum_udalosti, "%d.%m.%Y") as datumodeslani, mensasec.c_m_members.jmeno, mensasec.c_m_members.prijmeni '
            . 'FROM mensasec.c_m_logy_members, mensasec.c_m_members '
            . 'WHERE mensasec.c_m_logy_members.typ IN (1, 2) AND mensasec.c_m_logy_members.komu=' . $c_id_m . ' AND mensasec.c_m_members.id_m=c_m_logy_members.kdo '
            . 'ORDER BY mensasec.c_m_logy_members.datum_udalosti DESC';
        $vysledek = $this->dbsource->Query($SQL);
        return $this->dbsource->FetchAssocAll($vysledek);
    }
}

<?php
require_once 'google-api-php-client-master/autoload.php';
require_once("M_lib.php"); //formulare, post, html


define('CLIENT_ID', '************-kq1j0hofbnrtv4uohfi8564g1jl18800.apps.googleusercontent.com');
define('SERVICE_ACCOUNT_NAME', '<EMAIL>');
define('DOMAIN', 'mensa.cz');
define('SUB', '<EMAIL>');
define('KEY_FILE_LOCATION', '../ssl_library_2013/robot.p12');
define('MAIL_DOMAIN', '@mensa.cz');

/**
 * Class M_gapps
 * Trida pro ovladani Google Apps
 */
class M_gapps
{
    private $service;

    public function __construct()
    {
        $client = new Google_Client();
        $client->setApplicationName("Gadmin");
        $this->service = new Google_Service_Directory($client);
        if (isset($_SESSION['service_token'])) {
            $client->setAccessToken($_SESSION['service_token']);
        }
        $key = file_get_contents(KEY_FILE_LOCATION);
        $cred = new Google_Auth_AssertionCredentials(
            SERVICE_ACCOUNT_NAME,
            array(
                'https://www.googleapis.com/auth/admin.directory.group',
                'https://www.googleapis.com/auth/admin.directory.group.member',
                'https://www.googleapis.com/auth/admin.directory.group.readonly',
                'https://www.googleapis.com/auth/admin.directory.user',
                'https://www.googleapis.com/auth/admin.directory.user.readonly'),
            $key,
            'notasecret',
            'http://oauth.net/grant_type/jwt/1.0/bearer',
            SUB
        );
        //    'https://www.googleapis.com/auth/admin.directory.user.alias',
        //    'https://www.googleapis.com/auth/admin.directory.user.alias.readonly'
        $client->setAssertionCredentials($cred);
        if ($client->getAuth()->isAccessTokenExpired()) {
            $client->getAuth()->refreshTokenWithAssertion($cred);
        }
        $_SESSION['service_token'] = $client->getAccessToken();
    }

    // kontrola, zda-li uzivatel existuje
    public function existName($user_name)
    {
        $user = $this->retrieveUser($user_name);
        if ($user != false) return 1;//mailbox
        $entry = $this->retrieveFwd($user_name);
        if ($entry != false) return 2;//fwd
        return false;
    }


    //zrizeni noveho uzivatele
    public function createUser($username, $givenName, $familyName, $password)
    {
        //$gapps->createUser("Jan","Novak","<EMAIL>","abcABC123");
        $userInstance = new Google_Service_Directory_User();
        $nameInstance = new Google_Service_Directory_UserName();

        $nameInstance->setGivenName($givenName);
        $nameInstance->setFamilyName($familyName);

        $userInstance->setName($nameInstance);
        $userInstance->setHashFunction("MD5");
        $userInstance->setPrimaryEmail($username . MAIL_DOMAIN);
        $userInstance->setPassword(hash("md5", $password));
        $result = true;
        try {
            $createUserResult = $this->service->users->insert($userInstance);
        } catch (Google_IO_Exception $gioe) {
            $gioe->getMessage();
            $result = false;
        } catch (Google_Service_Exception $gse) {
            $gse->getMessage();
            $result = false;
        }
        return $result;
    }


    //vrati array info o uzivateli
    public function retrieveUser($username)
    {
        //$gapps->retrieveUser("jan.novak");
        //array() type-User, given_name, family_name, suspended - true/false, username - jan.novak - bez @mensa.cz
        //username dava primarni email, dobre u aliasu, aliases - vsechny aliasy oddelene carkou
        try {
            $result = $this->service->users->get($username . MAIL_DOMAIN);
            $list = array();
            $list['type'] = "User";
            $list['given_name'] = $result->name->givenName;
            $list['family_name'] = $result->name->familyName;
            $list['suspended'] = $result->suspended;
            $parts = explode('@', $result->primaryEmail);
            $list['username'] = $parts[0];
            $aliases = $result->aliases;
            if (is_array($aliases)) {
                $list['aliases'] = implode(", ", $aliases);
            } else {
                $list['aliases'] = false;
            }
        } catch (Exception $e) {
            $e->getCode();
            $list = false;
        }
        return $list;
    }




    //Google_Service_Directory_User Object
    //(
    //[collection_key:protected] => nonEditableAliases
    //[internal_gapi_mappings:protected] => Array
    //(
    //)
    //
    //[addresses] =>
    //[agreedToTerms] => 1
    //[aliases] =>
    //[changePasswordAtNextLogin] =>
    //[creationTime] => 2014-08-23T19:28:22.000Z
    //[customSchemas] =>
    //[customerId] => C031snucr
    //[deletionTime] =>
    //[emails] => Array
    //(
    //[0] => Array
    //(
    //[address] => <EMAIL>
    //[primary] => 1
    //)
    //
    //)
    //
    //[etag] => "KuAr0EK1uUI7oJNfO_dWOREN2ro/iM9Y1t2vtcMVgyqc6a1VvByv3y4"
    //[externalIds] =>
    //[hashFunction] =>
    //[id] => 113510790327909127551
    //[ims] =>
    //[includeInGlobalAddressList] => 1
    //[ipWhitelisted] =>
    //[isAdmin] =>
    //[isDelegatedAdmin] =>
    //[isMailboxSetup] => 1
    //[kind] => admin#directory#user
    //[lastLoginTime] => 2015-10-17T18:55:53.000Z
    //[nameType:protected] => Google_Service_Directory_UserName
    //[nameDataType:protected] =>
    //[nonEditableAliases] => Array
    //(
    //[0] => <EMAIL>
    //)
    //
    //[notes] =>
    //[orgUnitPath] => /
    //[organizations] =>
    //[password] =>
    //[phones] =>
    //[primaryEmail] => <EMAIL>
    //[relations] =>
    //[suspended] =>
    //[suspensionReason] =>
    //[thumbnailPhotoUrl] =>
    //[websites] =>
    //[modelData:protected] => Array
    //(
    //[name] => Array
    //(
    //[givenName] => Adam
    //[familyName] => Dvořák
    //[fullName] => Adam Dvořák
    /**
     * Google_Service_Directory_User Object
     * vrati array vsech uzivatelu.
     *
     * @return array
     */
    public function retrieveAllUsers()
    {
        $pageToken = '';
        $usersService = $this->service->users;
        while (1) {
            if ($pageToken != '') {
                $usersListObject = $usersService->listUsers(array('domain' => DOMAIN, 'pageToken' => $pageToken));
            } else {
                $usersListObject = $usersService->listUsers(array('domain' => DOMAIN));
            }
            if (!empty($usersListObject->nextPageToken)) {
                $pageToken = $usersListObject->nextPageToken;
            } else {
                $pageToken = '';
            }
            foreach ($usersListObject->users as $user) {
                $suspend = ($user->suspended) ? "!" : "";
                foreach ($user->getEmails() as $email) {
                    $users[] = $suspend . $email ["address"] . ' ' . $user->name->fullName;
                }
                if (!empty($user->aliases)) {
                    foreach ($user->aliases as $alias) {
                        $users[] = "*" . $alias;
                    }
                }
            }
            if ($pageToken == '') {
                break;
            }
        }
        return $users;
    }


    /**
     * Vrati aliasi tim, ze projde vsechny uzivatele a sleduje.
     * @return array
     */
    public function retrieveAliases()
    {
        $pageToken = '';
        $usersService = $this->service->users;
        while (1) {
            if ($pageToken != '') {
                $usersListObject = $usersService->listUsers(array('domain' => DOMAIN, 'pageToken' => $pageToken));
            } else {
                $usersListObject = $usersService->listUsers(array('domain' => DOMAIN));
            }
            if (!empty($usersListObject->nextPageToken)) {
                $pageToken = $usersListObject->nextPageToken;
            } else {
                $pageToken = '';
            }
            foreach ($usersListObject->users as $user) {
                if (!empty($user->aliases)) {
                    foreach ($user->aliases as $alias) {
                        $users[] = $alias . " => " . $user->primaryEmail;
                    }
                }
            }
            if ($pageToken == '') {
                break;
            }
        }
        return $users;
    }


    /**
     * Vypis vsech uzivatelu plnohodnotnych schranek napsany tak
     * aby byl kompatibilni s puvodni verzi stranky.
     * @return array
     */
    public function retrieveAllUsersWitDetails()
    {
        $pageToken = '';
        $usersService = $this->service->users;
        while (1) {
            if ($pageToken != '') {
                $usersListObject = $usersService->listUsers(array('domain' => DOMAIN, 'pageToken' => $pageToken));
            } else {
                $usersListObject = $usersService->listUsers(array('domain' => DOMAIN));
            }
            if (!empty($usersListObject->nextPageToken)) {
                $pageToken = $usersListObject->nextPageToken;
            } else {
                $pageToken = '';
            }


            foreach ($usersListObject->users as $user) {

                // print_r($user);
                $list = array();
                $list['Username'] = str_replace('@mensa.cz', '', $user->primaryEmail);
                $list['Given_Name'] = $user->name->givenName;
                $list['Family_Name'] = $user->name->familyName;
                $list['Suspended'] = $user->suspended ? 'Yes' : 'No';
                $users[] = $list;


            }
            if ($pageToken == '') {
                break;
            }
        }
        return $users;
    }


    //vytvori FWD
    public function new_Forward($username, $email, $description = "")
    {
        //$gapps->new_Forward("jan.novak","<EMAIL>","FWD: honza.n");
        if ($description == "") $description = "FWD: " . $username;
        try { //vytvori Group
            $group = new Google_Service_Directory_Group(array('email' => $username . MAIL_DOMAIN,
                'kind' => 'admin#directory#member',
                'name' => $username,
                'description' => $description));
            $createGRPResult = $this->service->groups->insert($group);
        } catch (Google_IO_Exception $gioe) {
            //echo "Google_IO_Exception: ".$gioe->getMessage();
            $result = false;
        } catch (Google_Service_Exception $gse) {
            //echo "Google_Service_Exception: ".$gse->getMessage();
            $result = false;
        }
        sleep(3);
        try { //vlozi do group zrizeneho vyse member
            $member = new Google_Service_Directory_Member(array('email' => $email, 'kind' => 'admin#directory#member'));
            $createFWDResult = $this->service->members->insert($username . MAIL_DOMAIN, $member);
            $result = true;
        } catch (Google_IO_Exception $gioe) {
            //echo "Google_IO_Exception: ".$gioe->getMessage();
            $result = false;
        } catch (Google_Service_Exception $gse) {
            //echo "Google_Service_Exception: ".$gse->getMessage();
            $result = false;
        }
        return $result;
    }

    //vlozeni noveho emailu do FWD
    public function insert_Email($username, $email)
    {
        try {
            $member = new Google_Service_Directory_Member(array('email' => $email, 'kind' => 'admin#directory#member'));
            $createFWDResult = $this->service->members->insert($username . MAIL_DOMAIN, $member);
            $result = true;
        } catch (Google_IO_Exception $gioe) {
            //echo "Google_IO_Exception: ".$gioe->getMessage();
            $result = false;
        } catch (Google_Service_Exception $gse) {
            //echo "Google_IO_Exception: ".$gioe->getMessage();
            $result = false;
        }
        return $result;
    }

    //vrati array FWD
    public function retrieveGroups()
    {
        $pageToken = '';
        $groupsService = $this->service->groups;
        $groups = array();
        while (1) {
            if ($pageToken != '') {
                $groupsListObject = $groupsService->listGroups(array('domain' => DOMAIN, 'pageToken' => $pageToken));
            } else {
                $groupsListObject = $groupsService->listGroups(array('domain' => DOMAIN));
            }
            if (!empty($groupsListObject->nextPageToken)) {
                $pageToken = $groupsListObject->nextPageToken;
            } else {
                $pageToken = '';
            }

            foreach ($groupsListObject->groups as $group) {
                //if($user->suspended){
                //        continue;
                //}

                // print_r($group);
                //                Google_Service_Directory_Group Object
                //                (
                //                    [collection_key:protected] => nonEditableAliases
                //            [internal_gapi_mappings:protected] => Array
                //                (
                //                )
                //
                //                [adminCreated] => 1
                //            [aliases] =>
                //            [description] => FWD: adam.knirsch
                //            [directMembersCount] => 1
                //            [email] => <EMAIL>
                //            [etag] => "KuAr0EK1uUI7oJNfO_dWOREN2ro/ibId7ol1bSWF1r2M6dSwxGiAIHg"
                //            [id] => 00sqyw641zpje8g
                //            [kind] => admin#directory#group
                //            [name] => adam.knirsch
                //            [nonEditableAliases] => Array
                //                (
                //                    [0] => <EMAIL>
                //                )
                //
                //            [modelData:protected] => Array
                //                (
                //                )
                //
                //                [processed:protected] => Array
                //                (
                //                )
                //
                //        )

                /*
                $groups[] = $group->getEmail();
                if(!empty($group->aliases)){
                  foreach($group->aliases as $alias){
                    $groups[] = "*".$alias;
                  }
                }*/
                $item = array();
                // 2017-01-14: info z googlu je v UTF-8
                //$item['name'] = iconv("UTF-8", "Windows-1250", $group->name);
                $item['name'] = $group->name;
        $groups[] = $item;
      }
            if ($pageToken == '') {
                break;
            }
        }
        return $groups;
    }


    //vrati list email pro groups - skupinu, mel by tam byt jeden email
    public function retrieveFwd($username)
    {
        try {
            $result = $this->service->groups->get($username . MAIL_DOMAIN);
            $list = array();
            $list['type'] = "Group";
            $list['name'] = $result->name;
            $list['email'] = $result->email;
            $list['description'] = $result->description;
            $aliases = $result->aliases;
            if (is_array($aliases)) {
                $list['aliases'] = implode(", ", $aliases);
            } else {
                $list['aliases'] = false;
            }
            $result = $this->service->members->listMembers($username . MAIL_DOMAIN);
            $lm = false;
            foreach ($result->members as $member) {
                $list_member[] = $member->getEmail();//.' '.$member->getRole().' '.$member->getType();
                $lm = true;
            }
            if ($lm) {
                $list['members'] = implode(", ", $list_member);
            } else {
                $list['members'] = false;
            }
        } catch (Exception $e) {
            $e->getCode();
            $list = false;
        }
        return $list;
    }

    //zruseni skupiny
    public function deleteGroup($user_name)
    {
        try {
            $deleteGRPResult = $this->service->groups->delete($user_name . MAIL_DOMAIN);
            $result = true;
        } catch (Exception $e) {
            $e->getCode();
            $result = false;
        }
        return $result;
    }

    public function retrieveNicknames($username)
    {
        try {
            $result = $this->service->users->get($username . MAIL_DOMAIN);
            $aliases = $result->aliases;
            if (is_array($aliases)) {
                $list = implode(", ", $aliases);
            } else {
                $list = false;
            }
        } catch (Exception $e) {
            $e->getCode();
            $list = false;
        }
        return $list;
    }

}

?>

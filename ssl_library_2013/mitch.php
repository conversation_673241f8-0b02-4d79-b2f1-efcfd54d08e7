<?php
namespace intranet\testovani\mitch;
/**
 * Konfigurace a zakladni funkce pro implementaci testu MITCH.
 *
 * Created by IntelliJ IDEA.
 * User: tkubes
 * Date: 15/07/2018
 * Time: 12:24
 */


// Konfigurace
// od 1. dne kdy je diteti 15 let uz muze samo.
define("VEK_PRO_ZZ", 15);


/**
 * Vrati TRUE, pokud je pro danou osobu potreba vysledek komunikovat se ZZ.
 *
 * Vytvoreni DateTime objektu:
 *  new DateTime($akce['date']);
 *  nebo
 *  DateTime::createFromFormat("Y-m-d H:i:s",$row["email2_verify_time"]);
 *
 * @param $datum_testu
 * @param $datum_narozeni_ditete
 * @return boolean
 */
function je_treba_zz($datum_testu, $datum_narozeni_ditete){
    echo("<!-- je_treba_zz() -->");

    // print_r(gettype($datum_testu));
    // print_r($datum_testu);
    // print_r(gettype($datum_narozeni_ditete));
    // print_r($datum_narozeni_ditete);

    // overit typ objektu
    assert(gettype($datum_testu) == "object",
        "datum_testu neni objekt typy datum.");
    assert(gettype($datum_narozeni_ditete) == "object",
        "datum_narozeni_ditete neni objekt typy datum.");

    // that the object on which diff is called is subtracted from the object that is passed to diff.
    $vek = $datum_narozeni_ditete->diff($datum_testu);
    // DateInterval Object ( [y] => 28 [m] => 0 [d] => 0 [h] => 0 [i] => 0 [s] => 0 [weekday] => 0
    //              [weekday_behavior] => 0 [first_last_day_of] => 0 [invert] => 0 [days] => 10227 [special_type] => 0
    //              [special_amount] => 0 [have_weekday_relative] => 0 [have_special_relative] => 0 )
    assert($vek->days > 0, "Parametry datum_testu a datum_narozeni_ditete jsou prohozene.");

    // vysledek
    return $vek->y < VEK_PRO_ZZ;
}


/**
 * Vrati vek do ktereho je treba ZZ.
 * Pokud je aktualni vek ditete MENSI nez toto cislo, je treba ZZ.
 * @return int
 */
function get_vek_je_treba_zz(){
    return VEK_PRO_ZZ;
}
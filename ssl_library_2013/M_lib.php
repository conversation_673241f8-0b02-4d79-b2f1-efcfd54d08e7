<?php
/**
 * Funkce pro posilani mailu.
 *
 * Zmenovnik
 * - 2020-05-24, TK: Konvertovano na utf-8
 * - 2025-03-12, MD: Sendgrid mailer
 */

/**
 * Vygeneruje zakladni emailovy retezec ze jmena uzivatele.
 *
 * @param string $jmeno
 * @param string $prijmeni
 * @return false pokud selze, jinak string se jmenem
 */
function generateUserName(string $jmeno, string $prijmeni)
{
    // vygeneruj vhodny string
    setlocale(LC_CTYPE, 'cs_CZ');

    // echo("$jmeno $prijmeni, ");
    $plainName = strtolower(iconv("utf-8", "ASCII//TRANSLIT", M_wa(trim($jmeno) . "." . trim($prijmeni))));
    // echo("$plainName, ");
    $safeName = M_nm($plainName);
    // echo("$safeName, ");
    $em_jmeno = trim(str_replace("..", ".", str_replace("-", ".", str_replace("_", ".", $safeName))));

    // usekni posledni tecku, pokud na konci retezce zbyla
    $em_jmeno = rtrim($em_jmeno, '.');
    // echo("$em_jmeno, ");

    // proved test toho co jsme nasli, at mame jistotu, ze konverze probehla korektne
    $result = preg_match("/^([a-z0-9]+\.)+[a-z0-9]+$/", $em_jmeno);
    // nepodarilo se najit
    if (!$result) {
        echo "<p>Selhalo generování uživatelského jména, výsledek není platný $jmeno, $prijmeni => '$em_jmeno'.</p>";
        return false;
    }

    return $em_jmeno;
}

/**
 * Manuálně Win1250 do ASCII - bez hacku, carek
 * @param $txt
 * @return string
 */
function M_wa($txt): string
{
    return strtr($txt, array(
            'ä' => 'a', 'Ä' => 'A', 'á' => 'a', 'Á' => 'A', 'č' => 'c', 'C' => 'C', 'ć' => 'c',
            'Č' => 'C', 'd' => 'd', 'D' => 'D', 'ě' => 'e', 'E' => 'E', 'é' => 'e', 'É' => 'E',
            'ë' => 'e', 'Ë' => 'E', 'í' => 'i', 'Í' => 'I', 'l' => 'l', 'L' => 'L', 'n' => 'n',
            'Ň' => 'N', 'ň' => 'n', 'N' => 'N', 'ó' => 'o', 'Ó' => 'O', 'ö' => 'o', 'Ö' => 'O',
            'ô' => 'o', 'Ô' => 'o', 'r' => 'r', 'R' => 'R', 'ř' => 'r', 'Ř' => 'R', 'š' => 's',
            'Š' => 'S', 's' => 's', 'S' => 'S', 'ť' => 't', 'Ť' => 'T', 'ú' => 'u', 'Ú' => 'U',
            'ů' => 'u', 'U' => 'U', 'ü' => 'u', 'Ü' => 'U', 'ý' => 'y', 'Ý' => 'Y', 'ž' => 'z',
            'Ž' => 'Z', 'z' => 'z', 'Z' => 'Z')
    );
}

/**
 * Odstraní uvozovky krom ", pozor vynucuje 8859-1
 * @param $text
 * @return string
 */
function M_qr($text): string
{
    return htmlspecialchars($text, ENT_QUOTES, "ISO-8859-1");
}

/**
 * odstrani hieroglyfy, _ a mezeru - pro nazvy souboru, tabulek
 * @param $text
 * @return string
 */
function M_nm($text): string
{
    return strtr($text, " ,!?#&@+/%áčďéěíňóřšťúůýžÁČĎÉĚÍŇÓŘŠŤÚŮÝŽ", "----------acdeeinorstuuyzACDEEINORSTUUYZ");
}

// ############ REQUEST  ############

function R_text($name, $default = false)
{
    //pro texty bez uvozovek
    // text("prijmeni","nezadano");
    return (isset($_REQUEST[$name])) ? M_qr($_REQUEST[$name]) : $default;
}

// ############ END REQUEST  ############

function send_email($to, $subject, $body): bool
{
    //UTF-8 header
    $header = "From: =?UTF-8?B?" . base64_encode("Intranet Mensa Česko") . "?=<<EMAIL>>\n";
    $header .= 'Return-Path: <EMAIL>\n';
    $header .= 'Content-Type: text/plain;charset=\"utf-8\"\n';
    $header .= 'Content-Transfer-Encoding: 8bit\n';
    $header .= 'MIME-Version: 1.0\n';
    $header .= 'X-Mailer: PHP/Mensaweb\n';
    $subject = "=?utf-8?B?" . base64_encode($subject) . "?=";
    // neni treba, vstupni string je jiz v unicode
    // $body = M_wu($body); // zakoduje

    $mailer = new mailer\Mailer();
    return $mailer->sendGlobalMail(__FILE__ . ':' . __LINE__, $to, $subject, $body, $header, "-r <EMAIL>");
}

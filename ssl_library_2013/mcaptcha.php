<?php
/**
 * Trivial CAPTCHA library.
 *
 * It is designed to be stateless - does not require persistence or
 * any other method to pass information in the background.
 *
 * To achieve statelessness, captcha code depends on current
 * date and optional seed, which can be any integer.
 *
 * This has drawback in the case user opens the form before midnight
 * and tries to submit it afterwards.
 */
class MCaptcha{
    private $captcha_total = null;
    private $captcha_a = null;
    private $captcha_b = null;

    public function __construct($seed = 0) {
        $seed = (int) $seed; // force seed to integer, protoze pokud tam prijde cokoliv z dat stranky, bude to int
        $day = (int) date("d");
        $this->captcha_total = ($day + $seed) % 20 + 1;
        $this->captcha_a = $this->captcha_total - rand(0,20);
        $this->captcha_b = $this->captcha_total - $this->captcha_a;
    }

    /**
     * Returns <label> element with an explanation of what number is required.
     * @return string
     */
    public function print_label(){
        return "<label for='comment'>Kolik je součet čísel {$this->captcha_a} a {$this->captcha_b} (ochrana proti robotům):</label>";
    }

    /**
     * Returns the HTML code of the <input> element.
     * @param $default string default value
     * @return string
     */
    public function print_input($default = ""){
        // turn off autocomplete, since the number will be different every time.
        return "<input type='text' name='comment' id='comment' size='2' value='{$default}' autocomplete='off'>";
    }

    public function get_value(){
        return $this->captcha_total;
    }
}

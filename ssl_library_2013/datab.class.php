<?php

/* * ********************************************************************
  AUTHOR:    <PERSON><PERSON><PERSON><PERSON>, <EMAIL>
  CREATED:   25.12.2014
  PROJECT:   MENSA CR, database interface
  DESC
  Parent database object
  Pouze tady by se měly vyskytovat příkazy pro komunikaci s databází tak, aby změnou této třídy bylo možno změnit databázový stroj
  /DESC
  HISTORYLIST
  /HISTORYLIST
 * ******************************************************************** */

class datab
{
    /**
     * @var mysqli
     */
    public $conn;

    /**
     * @var string
     */
    protected $path;

    /**
     * @var int
     */
    protected $zacatek;

    /**
     * @var int
     */
    protected $konec;

    /**
     * @var string
     */
    public $error;

    /**
     * 
     * @param string $host
     * @param string $user
     * @param string $password
     * @return mysqli|false
     */
    protected function createConnection($host, $user, $password)
    {
        return @mysqli_connect($host, $user, $password);
    }

    /**
     * @return mysqli
     */
    public function getConnection()
    {
        return $this->conn;
    }

    /**
     * @return string
     */
    public function getError()
    {
        return mysqli_error($this->conn);
    }

    /**
     * @return int
     */
    public function getErrorNumber()
    {
        return mysqli_errno($this->conn);
    }

    /**
     * @param string $database jméno databáze
     */
    protected function SelectDatabase($database)
    {
        mysqli_select_db($this->conn, $database);
    }

    /**
     * @param database connection $conn
     */
    public function closeConnection($conn)
    {
        @mysqli_close($conn);
    }

    /**
     * @param string $SQL
     * @return mysqli_result|false pokud projde, tak objekt výsledku dotazu. Pokud neprojde - false
     */
    public function doQuery($SQL) {
        $this->error = "";
        $this->zacatek = time();

        $vysledek = mysqli_query($this->conn, $SQL);

        $this->error = $this->getError();
        $this->konec = time();

        if (($this->konec - $this->zacatek) >= 1) {
            $hand = fopen($this->path, "a");
            fwrite($hand, date("d.m.Y H:i") . " (" . ($this->konec - $this->zacatek) . "): " . $SQL . "\n\n");
            fclose($hand);
        }

        $this->zacatek = NULL;
        $this->konec = NULL;

        return $vysledek ?: false;
    }

    /**
     * @param mysqli_result $result
     * @return array
     */
    public function FetchAssocAll($result)
    {
        $navrat = array();

        while ($radek = mysqli_fetch_assoc($result)) {
            $navrat[] = $radek;
        }

        return $navrat;
    }

    /**
     * vrací automaticky číslované pole s asociativními poli řádků výsledku pokud není určena položka $key, jinak je číslováno podle primary key
     * 
     * @param mysqli_result $result
     * @param string $key - jméno pole s primary key
     * @return array
     */
    public function FetchAssocKey($result, $key = NULL)
    {
        $navrat = array();

        while ($radek = mysqli_fetch_assoc($result)) {
            if ($key === NULL) {
                $navrat[] = $radek;
            } else {
                $navrat[$radek[$key]] = $radek;
            }
        }

        return $navrat;
    }

    /**
     * @param mysqli_result $result
     * @return int
     */
    public function getNumRows($result)
    {
        return $result ? mysqli_num_rows($result) : 0;
    }

    /**
     * @param mysqli_result $result
     * @return void
     */
    public function setFreeResult($result)
    {
        mysqli_free_result($result);
    }

    /**
     * @param mysqli_result $result
     * @param string $radek
     * @param string $sloupec = ''
     * @return mixed
     */
    protected function mysqli_result($result, $radek, $sloupec)
    {
        $result->data_seek($radek);
        $datarow = $result->fetch_array();

        return isset($datarow[$sloupec]) ? $datarow[$sloupec] : null;
    }

    /**
     * @param mysqli_result $result
     * @param string $radek
     * @param string $sloupec
     * @return mixed
     */
    public function getResult($result, $radek, $sloupec = '')
    {
        return $this->mysqli_result($result, $radek, $sloupec);
    }

    /**
     * @return int|string
     */
    public function getInsertId()
    {
        return mysqli_insert_id($this->conn);
    }

    /**
     * @return int|string
     */
    public function getAffectedRows()
    {
        return mysqli_affected_rows($this->conn);
    }

    /**
     * @param mysqli_result $result
     * @return array|false|null
     */
    public function FetchArray($result, $mode = MYSQLI_BOTH)
    {
        return mysqli_fetch_array($result, $mode);
    }

    /**
     * @param mysqli_result $result
     * @return array|false|null
     */
    public function FetchAssoc($result)
    {
        return mysqli_fetch_assoc($result);
    }

    /**
     * @param mysqli_result $result
     * @return array|false|null
     */
    public function FetchRow($result)
    {
        return mysqli_fetch_row($result);
    }

    /**
     * @param string $string
     * @return string
     */
    public function getEscapedString($string)
    {
        return mysqli_real_escape_string($this->conn, $string);
    }

    /**
     * @return void
     */
    public function getInfo()
    {
        mysqli_info($this->conn);
    }

}

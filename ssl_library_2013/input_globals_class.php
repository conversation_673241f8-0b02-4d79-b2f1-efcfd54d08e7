<?php

/**
 *
 *  @package mensa.cz
 *  <AUTHOR> <PERSON><PERSON><PERSON><PERSON>, <EMAIL>
 *  @version 1.0
 *
 *  třída pro ošetření vstupů z $_POST a $_GET
 *  originální jméno souboru input_globals_class.php
 *
 */
class input_globals {

    //private $nazev;         // jméno proměnné v $_GET nebo $_POST poli
    private $seznamFunkci = array();
    private $vystup;          // proměnná pro výstup informace s nápovedou;

    public function __construct() {
        
    }

    /*
     * funkce vrací nápovědu
     * pokud je help() pak dá seznam funkcí
     * 
     */

    public function help($nameproc = '') {
        $this->vystup = "";
        $this->seznamFunkci[] = array('jmeno' => 'jeGlobals', 'popis' => '<cite>input_globals::jeGlobals($jmeno)</cite> &nbsp;&nbsp; Vrací TRUE nebo FALSE pokud je nebo není proměnn<PERSON> v $_GET nebo v $_POST polích');
        $this->seznamFunkci[] = array('jmeno' => 'jeInteger', 'popis' => '<cite>input_globals::jeInteger($jmeno,[$hodnota])</cite> &nbsp;&nbsp; Vrací TRUE nebo FALSE pokud je nebo není proměnná typu Int a pokud je uvedená hodnota pak i TRUE nebo FALSE pokud odpovídá nebo neodpovídá hodnotě. Bere kladná i záporná čísla. Pokud proměnná neexistuje, vrací NULL;');
        $this->seznamFunkci[] = array('jmeno' => 'getInteger', 'popis' => '<cite>input_globals::getInteger($jmeno,$vraciKdyzNeni)</cite> &nbsp;&nbsp; Pokud proměnná neexistuje nebo není Integer, vrací $vraciKdyzNeni, která je defaultně NULL; Jinak vrací hodnotu proměnné');
        $this->seznamFunkci[] = array('jmeno' => 'getString', 'popis' => '<cite>input_globals::getString($jmeno,$vraciKdyzNeni)</cite> &nbsp;&nbsp; Pokud proměnná neexistuje vrací hodnotu $vraciKdyzNeni, která je defaultně NULL; Jinak vrací hodnotu proměnné. V textu likviduje znak =');
        $this->seznamFunkci[] = array('jmeno' => 'getDatum', 'popis' => '<cite>input_globals::getDatum($jmeno,$vraciKdyzNeni)</cite> &nbsp;&nbsp; Pokud proměnná neexistuje vrací hodnotu $vraciKdyzNeni, která je defaultně NULL; Jinak vrací hodnotu proměnné. Nahradí ,a;a_ za tečku a zruší nedatumové znaky');
        $this->seznamFunkci[] = array('jmeno' => 'getEmail', 'popis' => '<cite>input_globals::getEmail($jmeno,$vraciKdyzNeni)</cite> &nbsp;&nbsp; Pokud proměnná neexistuje vrací hodnotu $vraciKdyzNeni, která je defaultně NULL; Jinak vrací hodnotu emailu, pokud splňuje požadavky na emailovou adresu - tento pattern [a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4} . jinak vrací opět $vraciKdyzNeni');
        foreach ($this->seznamFunkci as $key => $val) {
            $this->vystup.="<strong>".$val['jmeno'] . "</strong>&nbsp;&nbsp;" . $val['popis'] . "<br>";
        }
        return $this->vystup;
    }
    /*
     * Vrací TRUE nebo FALSE pokud je nebo není proměnná se jménem v proměnné $nazev v $_GET nebo v $_POST polích
     */
    public function jeGLobals($nazev) {
        if (isset($_GET[$nazev])) {
            return TRUE;
        } elseif (isset($_POST[$nazev])) {
            return TRUE;
        } else {
            return FALSE;
        }
    }
    
    /*
     * Vrací TRUE nebo FALSE pokud je nebo není proměnná se jménem v proměnné $nazev v $_GET nebo v $_POST polích a tvaru celé číslo
     */
    public function jeInteger($nazev, $hodnota = null) {
        $this->vystup = $this->odchytHodnotu($nazev);
        if(is_null($this->vystup)){
            return NULL;
        }
        if (preg_match('/^\-?[0-9]+/', $this->vystup)) {
            if(!is_null($hodnota)){
                if(preg_match('/^'.$hodnota.'$/', $this->vystup)){
                    return TRUE;
                }
                else{
                    return FALSE;
                }
            }
            else{
                    return TRUE;     
            }
        }
        else{
            return FALSE;
        }
    }
    /*
     * Načítá z globals celočíselnou hodnotu se jménem v proměnné $nazev a vrací její hodnotu, 
     * pokud není, vrací hodnotu $vraciKdyzNeni
     */
    
    public function getInteger($nazev, $vraciKdyzNeni=NULL){
        $this->vystup = $this->odchytHodnotu($nazev);
        if(is_null($this->vystup)){
            return $vraciKdyzNeni;
        }
        if (preg_match('/^\-?[0-9]+/', $this->vystup)) {
            return intval($this->vystup);
        }
        else{
            return $vraciKdyzNeni;
        }
    }
    /*
     * Načítá z globals textovou hodnotu se jménem v proměnné $nazev a vrací její hodnotu, 
     * pokud není, vrací hodnotu $vraciKdyzNeni
     * vyhazuje znaky + a = jako základní ochranu před napadením
     */
    
    public function getString($nazev, $vraciKdyzNeni=NULL){
        $this->vystup = $this->odchytHodnotu($nazev);
        if(is_null($this->vystup)){
            return $vraciKdyzNeni;
        }
        else{
            return preg_replace('/[=]+/', '/\ /', trim($this->vystup));
        }     
    }
    /*
     * Načítá z globals datum hodnotu se jménem v proměnné $nazev a vrací její hodnotu, 
     * pokud není, vrací hodnotu $vraciKdyzNeni
     * nahradí ,;a_ tečkou (ochrana na překlepy) a testuje na neexistenci písmen
     * Neřeší vzhled datumu podle norem
     */
    
    public function getDatum($nazev,$vraciKdyzNeni=NULL){
         $this->vystup = $this->odchytHodnotu($nazev);
        if(is_null($this->vystup)){
            return $vraciKdyzNeni;
        }
        else{
            $this->vystup=preg_replace('/[,;_]+/', '.', trim($this->vystup));
            $this->vystup=preg_replace('/[\ =A-Za-z]+/', '', trim($this->vystup));
            if(strlen($this->vystup)>5){
                return $this->vystup;
            }
            else{
                return $vraciKdyzNeni;
            }
        }
    }
    /*
     * Načítá z globals email hodnotu se jménem v proměnné $nazev a vrací její hodnotu, 
     * pokud není, vrací hodnotu $vraciKdyzNeni
     */
    public function getEmail($nazev, $vraciKdyzNeni=NULL){
        $this->vystup = $this->odchytHodnotu($nazev);
        if(is_null($this->vystup)){
            return $vraciKdyzNeni;
        }
        if (preg_match('/[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}/', $this->vystup)) {
            return $this->vystup;
        }
        else{
            return $vraciKdyzNeni;
        }
    }
    
    /*
     * odchytí proměnnou se jménem v proměnné $nazev z GET a POST a vrátí její hodnotu nebo NULL pokud neexistuje
     */
    private function odchytHodnotu($nazev) {
        if (isset($_GET[$nazev])) {
            return $_GET[$nazev];
        } elseif (isset($_POST[$nazev])) {
            return $_POST[$nazev];
        } else {
            return NULL;
        }
    }

}

?>

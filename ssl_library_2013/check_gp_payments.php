<?php
/*
  Projde nepotvrzené platby za posledních 7 dní, ov<PERSON><PERSON><PERSON> jejich status u platební brány
  a aktualizuje ho v datab<PERSON>zi
*/

const GPWEBPAY_LIVE_URL = 'https://3dsecure.gpwebpay.com/pay-ws/v1/PaymentService';
const GPWEBPAY_PAYMENT_PROVIDER = '0880';
const GPWEBPAY_PRIVATE_KEY_PATH = '/home/<USER>/artifacts/prvKeyProd';
const GPWEBPAY_PRIVATE_KEY_PASS = 'SoukromyKlic2018';
const GPWEBPAY_PUBLIC_KEY_PATH = '/home/<USER>/artifacts/pubKeyProd';
const GPWEBPAY_MERCHANT_NUMBER = '203670002';

function GPsign($value)
{
    $prvKey = file_get_contents(GPWEBPAY_PRIVATE_KEY_PATH);
    $keyId = openssl_get_privatekey($prvKey, GPWEBPAY_PRIVATE_KEY_PASS);
    openssl_sign($value, $signature, $keyId);
    $signature = base64_encode($signature);
    //openssl_free_key($keyId);
    return $signature;
}

function GPsignatureValid($value, $signature)
{
    $signature = base64_decode($signature);
    $pubKey = file_get_contents(GPWEBPAY_PUBLIC_KEY_PATH);
    $keyId = openssl_get_publickey($pubKey);
    return openssl_verify($value, $signature, $keyId);
}

function GPCallWebService($xmlRequest)
{
    $url = GPWEBPAY_LIVE_URL;
    
    // Create HTTP request headers
    $headers = [
        'Content-Type: text/xml; charset=utf-8',
        'Content-Length: ' . strlen($xmlRequest)
    ];
    
    // Create the stream context
    $context = stream_context_create([
        'http' => [
            'method'  => 'POST',
            'header'  => implode("\r\n", $headers),
            'content' => $xmlRequest,
            'ignore_errors' => true // Capture errors in response
        ],
        'ssl' => [
            'verify_peer' => true,  // Ignore SSL verification (optional)
            'verify_peer_name' => false
        ]
    ]);

    // Send the request
    $response = @file_get_contents($url, false, $context);

    // Handle errors
    if ($response === false)
    {
        $error = error_get_last();
        return 'Error: ' . $error['message'];
    }

    return $response;
}

function GPServiceAvailable() {
    // Create the XML structure using DOMDocument
    $dom = new DOMDocument('1.0', 'UTF-8');
    
    // Create the envelope element
    $envelope = $dom->createElement('soapenv:Envelope');
    $envelope->setAttribute('xmlns:soapenv', 'http://schemas.xmlsoap.org/soap/envelope/');
    $envelope->setAttribute('xmlns:v1', 'http://gpe.cz/pay/pay-ws/proc/v1');
    $dom->appendChild($envelope);
    
    // Create the header element
    $header = $dom->createElement('soapenv:Header');
    $envelope->appendChild($header);
    
    // Create the body element
    $body = $dom->createElement('soapenv:Body');
    $envelope->appendChild($body);
    
    // Create the echo element inside the body
    $echo = $dom->createElement('v1:echo');
    $body->appendChild($echo);
    
    // Convert the DOMDocument to a string
    $xmlRequest = $dom->saveXML();
    
    $response = GPCallWebService($xmlRequest);
    if (substr($response, 0, 5) == "Error") return $response;

    // Parse the response to check for the expected result
    $responseDom = new DOMDocument();
    $responseDom->loadXML($response);
    
    // Check if the response contains the <ns4:echoResponse> element
    $xpath = new DOMXPath($responseDom);
    $xpath->registerNamespace('ns4', 'http://gpe.cz/pay/pay-ws/proc/v1');
    
    $echoResponse = $xpath->query('//ns4:echoResponse');
    if ($echoResponse->length > 0) {
        return 'OK';
    } else {
        return 'Unexpected response: ' . $response;
    }
}

function GPGetPaymentStatus($paymentNumber) {

    if (!file_exists(GPWEBPAY_PRIVATE_KEY_PATH)) return ['error' => 'Private key not found'];
    if (!file_exists(GPWEBPAY_PUBLIC_KEY_PATH)) return ['error' => 'Public key not found'];

    //$config = Config::getConfig();
    //$merchangNumber = $config->gpWebPay->merchantNumber;
    $merchantNumber = GPWEBPAY_MERCHANT_NUMBER;
    $provider = GPWEBPAY_PAYMENT_PROVIDER;
    $messageId = uniqid('mensaapi');
     
    // Generate the signature using GPSignature::sign with the parameters joined by '|'
    $digest = implode('|', [$messageId, $provider, $merchantNumber, $paymentNumber]);
    $signature = GPsign($digest);
    
    //echo "Digest:\n$digest\nSignature:\n$signature\n";
    
    // Create the XML structure using DOMDocument
    $dom = new DOMDocument('1.0', 'UTF-8');
    
    // Create the envelope element
    $envelope = $dom->createElement('soapenv:Envelope');
    $envelope->setAttribute('xmlns:soapenv', 'http://schemas.xmlsoap.org/soap/envelope/');
    $envelope->setAttribute('xmlns:v1', 'http://gpe.cz/pay/pay-ws/proc/v1');
    $envelope->setAttribute('xmlns:type', 'http://gpe.cz/pay/pay-ws/proc/v1/type');
    $dom->appendChild($envelope);
    
    // Create the header element
    $header = $dom->createElement('soapenv:Header');
    $envelope->appendChild($header);
    
    // Create the body element
    $body = $dom->createElement('soapenv:Body');
    $envelope->appendChild($body);
    
    // Create the getPaymentStatus element
    $getPaymentStatus = $dom->createElement('v1:getPaymentStatus');
    $body->appendChild($getPaymentStatus);
    
    // Create the paymentStatusRequest element
    $paymentStatusRequest = $dom->createElement('v1:paymentStatusRequest');
    $getPaymentStatus->appendChild($paymentStatusRequest);
    
    // Add child elements to paymentStatusRequest
    $elements = [
        'type:messageId' => $messageId,
        'type:provider' => $provider,
        'type:merchantNumber' => $merchantNumber,
        'type:paymentNumber' => $paymentNumber,
        'type:signature' => $signature
    ];
    
    foreach ($elements as $tag => $value) {
        $element = $dom->createElement($tag, $value);
        $paymentStatusRequest->appendChild($element);
    }
    
    // Convert the DOMDocument to a string
    $xmlRequest = $dom->saveXML();
    
    //echo "Request:\n$xmlRequest\n";
    
    $response = GPCallWebService($xmlRequest);
    if (substr($response, 0, 5) == "Error") return ['error' => 'cURL error: ' . $response];

    // Parse the response to extract the required fields
    $responseDom = new DOMDocument();
    $responseDom->loadXML($response);
    
    $xpath = new DOMXPath($responseDom);
    $xpath->registerNamespace('ns4', 'http://gpe.cz/pay/pay-ws/proc/v1');
    $xpath->registerNamespace('ns3', 'http://gpe.cz/pay/pay-ws/proc/v1/type');

    // Check for <soapenv:Fault> in the response
    $faultNode = $xpath->query('//soapenv:Fault');
    if ($faultNode->length > 0) {
        // Extract faultcode and faultstring
        $faultcodeNode = $xpath->query('//soapenv:Fault/faultcode');
        $faultstringNode = $xpath->query('//soapenv:Fault/faultstring');
        // Extract optional elements inside <detail>
        $primaryReturnCodeNode = $xpath->query('//ns3:primaryReturnCode');
        $secondaryReturnCodeNode = $xpath->query('//ns3:secondaryReturnCode');

        return [
            'error' => 'Error returned by web service',
            'faultcode' => $faultcodeNode->length > 0 ? $faultcodeNode->item(0)->nodeValue : null,
            'faultstring' => $faultstringNode->length > 0 ? $faultstringNode->item(0)->nodeValue : null,
            'primaryReturnCode' => $primaryReturnCodeNode->length > 0 ? $primaryReturnCodeNode->item(0)->nodeValue : null,
            'secondaryReturnCode' => $secondaryReturnCodeNode->length > 0 ? $secondaryReturnCodeNode->item(0)->nodeValue : null,
            'rawResponse' => $response
        ];
    }

    // Extract success response elements
    $stateNode = $xpath->query('//ns3:state');
    $statusNode = $xpath->query('//ns3:status');
    $subStatusNode = $xpath->query('//ns3:subStatus');
    $signatureNode = $xpath->query('//ns3:signature');

    if ($stateNode->length > 0 && $signatureNode->length > 0) {
        $result = [
            'state' => $stateNode->item(0)->nodeValue,
            'status' => $statusNode->length > 0 ? $statusNode->item(0)->nodeValue : null,
            'subStatus' => $subStatusNode->length > 0 ? $subStatusNode->item(0)->nodeValue : null
        ];

        // Verify the signature using GPSignature::sign
        $responseSignatureString = implode('|', [
            $messageId,
            $result['state'],
            $result['status'] ?? '',
            $result['subStatus'] ?? ''
        ]);

        $result['signatureValid'] = GPsignatureValid($responseSignatureString, $signatureNode->item(0)->nodeValue);
        $result['rawResponse'] = $response;

        return $result;
    } else {
        return ['error' => 'Invalid response or missing elements', 'rawResponse' => $response];
    }
}


define('AUTORUN', true);
require_once("../ssl_library_new/database2.class.l");
$db2 = new database2;
$db2->open();
// Check at max 5 payments in one run
$query = "SELECT `_id`, `orderNumber` FROM `payments` WHERE `status` = 'initialized' AND `CreatedAt` >= NOW() - INTERVAL 5 DAY ORDER BY `datum_zmeny` ASC, `CreatedAt` ASC LIMIT 5;";
$result = $db2->Query($query);

$orders = $db2->FetchAssocAll($result);

if (empty($orders))
{
  echo "Zadne nedokoncene platby.\n";
}
else
{
  echo count($orders) . " nedokoncenych plateb\n";
  
  $service_status = GPServiceAvailable();
  if ($service_status != 'OK')
  {
    echo ("Platební brána nedostupná: $service_status . \n");
  }
  else
  {
    foreach($orders as $order)
    {
      $payment_id = $order['_id'];
      $order_number = $order['orderNumber'];
      echo "Kontroluji platbu $order_number\n";
      $payment_status = GPGetPaymentStatus($order_number);
      if (isset($payment_status['status']))
      {
        switch ($payment_status['status'])
        {
          case 'UNPAID':
          case 'REFUNDED':
            // Platba neúspěšná, status je finální
            echo "Platba $order_number selhala\n";
            $db2->Query("UPDATE `payments` SET `status` = 'failed' WHERE `_id` = $payment_id;");
            break;
            
          case 'CAPTURED':
            switch ($payment_status['subStatus'])
            {      
              case 'SENT_TO_SETTLEMENT':
              case 'SETTLED':
              // Platba úspěšná, status je finální
              echo "Platba $order_number uspesna\n";
              $db2->Query("UPDATE `payments` SET `status` = 'paid' WHERE `_id` = $payment_id;");
              break;
            }
            break;
          
          default:
            // Aktualizovat záznam, aby se znovu kontroloval, až se zkontrolují ostatní
            $db2->Query("UPDATE `payments` SET `datum_zmeny` = current_timestamp() WHERE `_id` = $payment_id;");
        }
      }
      elseif (isset($payment_status['error']))
      {
        if (($payment_status['primaryReturnCode'] ?? 0) == 15 and ($payment_status['secondaryReturnCode'] ?? 0) == 1)
        {
            // Platba nenalezena, zřejmě expirovala
            echo "Platba $order_number nenalezena\n";
            $db2->Query("UPDATE `payments` SET `status` = 'missing' WHERE `_id` = $payment_id;");
        }
        else
        {
          $error = $payment_status['error'];
          echo "Chyba pri kontrole platby $order_number: $error\n";
        }
        // Aktualizovat záznam, aby se znovu kontroloval, až se zkontrolují ostatní
        $db2->Query("UPDATE `payments` SET `datum_zmeny` = current_timestamp() WHERE `_id` = $payment_id;");
      }
      else
      {
        // Aktualizovat záznam, aby se znovu kontroloval, až se zkontrolují ostatní
        $db2->Query("UPDATE `payments` SET `datum_zmeny` = current_timestamp() WHERE `_id` = $payment_id;");
      }
    }
  }
}

$db2->close();
